# 文件云流转助手 - 页面注入面板使用指南

## 📋 功能概述

页面注入面板是文件云流转助手的新增功能，专门为 `https://yunpan.gdcourts.gov.cn:82/` 网站设计。它将插件的完整功能直接注入到云盘页面中，让用户无需点击浏览器工具栏图标即可使用所有功能。

## 🎯 主要特性

### ✨ **与Popup一致的UI**
- 完全相同的界面布局和设计风格
- 相同的功能按钮和操作逻辑
- 一致的文件树显示和状态管理

### 🔧 **专属功能**
- **可拖拽**: 面板可以在页面上自由拖拽移动
- **可最小化**: 点击最小化按钮收起面板内容
- **可关闭**: 临时隐藏面板，不影响后台功能
- **自适应**: 根据页面大小自动调整布局

### 🎨 **视觉优化**
- 半透明背景效果
- 现代化阴影和圆角设计
- 响应式动画过渡
- 高对比度确保可读性

## 🚀 使用方法

### 自动激活
1. 安装并启用文件云流转助手扩展
2. 访问 `https://yunpan.gdcourts.gov.cn:82/`
3. 页面加载完成后，面板会自动出现在右上角
4. 显示"文件云流转助手已激活"通知

### 手动控制
- **拖拽移动**: 点击面板标题栏拖拽到任意位置
- **最小化**: 点击标题栏的 `−` 按钮
- **关闭**: 点击标题栏的 `×` 按钮
- **重新显示**: 刷新页面或通过扩展popup重新激活

## 🎛️ 功能详解

### 连接状态区域
```
🟢 已连接 | 云盘已连接
自动刷新: [5分钟 ▼]
共 25 个文件，12 个待上传
```

- **连接状态指示器**: 显示与后端服务器的连接状态
- **云盘状态**: 显示当前页面的云盘连接状态
- **自动刷新设置**: 配置页面自动刷新间隔
- **文件统计**: 实时显示文件数量和状态

### 操作按钮区域
```
[开始上传] [打开云盘] [↻] [自动上传 ○]
```

- **开始上传**: 启动批量文件上传流程
- **打开云盘**: 在新标签页打开云盘首页
- **刷新按钮**: 手动刷新文件列表
- **自动上传开关**: 启用/禁用自动上传功能

### 文件管理标签页

#### 未上传文件标签
- 显示所有待上传的文件
- 支持文件夹树形结构展示
- 实时状态更新（待上传、上传中、失败）
- 文件缩略图预览（图片文件）

#### 已上传文件标签
- 显示所有已成功上传的文件
- 相同的树形结构和预览功能
- 上传完成时间和状态信息

### 文件树操作
```
未上传文件                    [📂] [📁]
├── 📁 2025-01-30/
│   ├── 📄 报告.pdf          [待上传]
│   └── 🖼️ 图片.jpg          [上传中]
└── 📄 文档.docx             [待上传]
```

- **展开/折叠**: 点击文件夹图标或使用工具栏按钮
- **文件预览**: 点击文件显示详细信息
- **状态标签**: 彩色标签显示文件同步状态
- **文件图标**: 根据文件类型显示对应图标

## 🔧 高级功能

### 拖拽定位
- 面板可以拖拽到页面任意位置
- 自动边界检测，防止拖出可视区域
- 位置记忆功能（刷新页面后保持位置）

### 响应式设计
- 在小屏幕设备上自动调整宽度
- 保持最佳的可读性和操作性
- 适配不同分辨率的显示器

### 状态同步
- 与popup面板实时同步数据
- 与后端服务器保持WebSocket连接
- 自动更新文件状态和进度

## 🎨 界面定制

### 主题色彩
- **主色调**: 蓝紫渐变 (#667eea → #764ba2)
- **状态色彩**: 
  - 🟡 待上传 (#ffc107)
  - 🔵 上传中 (#007bff)
  - 🟢 已上传 (#28a745)
  - 🔴 失败 (#dc3545)

### 动画效果
- 面板显示/隐藏的滑动动画
- 按钮悬停的颜色过渡
- 文件树展开/折叠的平滑动画
- 通知消息的淡入淡出效果

## 🛠️ 故障排除

### 面板未显示
**可能原因**:
- 不在目标网站 (yunpan.gdcourts.gov.cn:82)
- 页面加载未完成
- 扩展未正确安装

**解决方案**:
1. 确认访问正确的网址
2. 等待页面完全加载
3. 刷新页面重试
4. 检查扩展是否启用

### 功能无响应
**可能原因**:
- 网络连接问题
- 后端服务器离线
- 浏览器权限限制

**解决方案**:
1. 检查网络连接
2. 查看连接状态指示器
3. 尝试刷新页面
4. 联系技术支持

### 样式显示异常
**可能原因**:
- 页面CSS冲突
- 浏览器兼容性问题
- 扩展资源加载失败

**解决方案**:
1. 清除浏览器缓存
2. 禁用其他可能冲突的扩展
3. 更新到最新版本的Chrome
4. 重新安装扩展

## 📱 移动端适配

虽然主要为桌面端设计，但面板也支持移动设备：

- **触摸操作**: 支持触摸拖拽和点击
- **响应式布局**: 在小屏幕上自动调整宽度
- **手势友好**: 按钮大小适合触摸操作

## 🔒 隐私和安全

### 数据处理
- 所有文件数据仅在本地处理
- 不会向第三方服务器发送敏感信息
- 遵循最小权限原则

### 网络通信
- 仅与指定的内网服务器通信
- 使用WebSocket加密传输
- 定期验证连接安全性

## 📞 技术支持

### 调试信息
如遇问题，请提供以下信息：
1. 浏览器版本和操作系统
2. 扩展版本号
3. 控制台错误信息
4. 问题复现步骤

### 联系方式
- 内部技术支持: [联系开发团队]
- 问题反馈: [提交到内部系统]
- 使用文档: 参考本指南和README.md

---

## 🎉 总结

页面注入面板为文件云流转助手带来了更便捷的使用体验，让用户可以在不离开云盘页面的情况下完成所有文件管理操作。通过与原有popup功能的完美结合，提供了一致且强大的文件同步解决方案。

**享受更高效的文件管理体验！** 🚀

# 注入面板上传功能修复指南

## 🔧 修复内容

本次修复解决了注入面板中手动上传按钮无法触发云盘页面自动化操作的问题。

**根本原因**: content-script-yunpan.js没有在带端口号的云盘页面(`:82`)正确加载，导致background.js无法与云盘页面通信。

### 主要修改

1. **修复manifest.json配置** (`manifest.json`)
   - 添加了对 `https://yunpan.gdcourts.gov.cn:82/*` 的content script匹配
   - 确保content-script-yunpan.js在所有云盘页面变体上都能加载

2. **增强云盘内容脚本** (`content-script-yunpan.js`)
   - 添加了详细的初始化日志
   - 增强了消息监听器的调试信息
   - 将控制器暴露到全局作用域便于调试

3. **优化后台脚本** (`background.js`)
   - 改进了 `handleTriggerAutoUpload()` 函数的云盘页面检测
   - 增强了 `executeYunpanUpload()` 函数的调试和错误处理
   - 添加了PING测试机制确保通信正常
   - 增加了详细的标签页查找日志

## 🚀 使用方法

### 1. 重新加载扩展
1. 打开 `chrome://extensions/`
2. 找到"文件云流转助手"扩展
3. 点击刷新按钮重新加载扩展

### 2. 测试上传功能
1. 确保云盘页面已打开 (`https://yunpan.gdcourts.gov.cn:82/`)
2. 在注入面板中点击"开始上传"按钮
3. 观察控制台日志，确认通信正常

### 3. 调试工具

#### 使用Background测试脚本
在浏览器控制台中运行：
```javascript
// 加载测试脚本
const script = document.createElement('script');
script.src = chrome.runtime.getURL('test-background-upload.js');
document.head.appendChild(script);

// 运行完整测试
testBackgroundUpload.runBackgroundUploadTest();
```

#### 检查云盘页面Content Script
在云盘页面控制台中检查：
```javascript
// 检查content script是否加载
console.log('YunpanController:', window.yunpanController);

// 测试消息监听
chrome.runtime.sendMessage({type: 'PING'});
```

## 🔍 故障排除

### 常见问题

1. **"未找到云盘页面"错误**
   - 确保云盘页面已正确打开 (`https://yunpan.gdcourts.gov.cn:82/`)
   - 检查URL是否包含 `yunpan.gdcourts.gov.cn`
   - 尝试刷新云盘页面

2. **"云盘页面通信失败"错误**
   - **最常见原因**: content-script-yunpan.js未在云盘页面加载
   - 在云盘页面控制台检查: `console.log(window.yunpanController)`
   - 如果返回 `undefined`，说明content script未加载
   - 重新加载扩展并刷新云盘页面

3. **"PING超时"或"PING失败"错误**
   - content script加载但消息监听器未正确设置
   - 检查云盘页面控制台是否有JavaScript错误
   - 确认扩展权限正确

4. **"没有待上传文件"提示**
   - 检查文件树数据是否存在
   - 确认有文件状态为 `pending` 或 `failed`
   - 尝试刷新文件列表

### 调试步骤

1. **检查扩展状态**
   ```javascript
   chrome.runtime.sendMessage({type: 'GET_CONNECTION_STATUS'})
   ```

2. **检查云盘页面连接**
   ```javascript
   // 在云盘页面控制台运行
   chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
       console.log('收到消息:', request);
       sendResponse({success: true, message: '测试响应'});
       return true;
   });
   ```

3. **检查文件树数据**
   ```javascript
   chrome.storage.local.get('admin_file_tree').then(result => {
       console.log('文件树数据:', result.admin_file_tree);
   });
   ```

## 📋 技术细节

### 通信流程

1. **直接通信模式**（新增）
   ```
   注入面板 → 查找云盘标签页 → 直接发送消息 → 云盘内容脚本
   ```

2. **后台中转模式**（备用）
   ```
   注入面板 → Background Script → 云盘内容脚本
   ```

### 消息类型

- `PING`: 测试连接
- `START_UPLOAD`: 开始上传
- `TRIGGER_AUTO_UPLOAD`: 触发自动上传
- `GET_CONNECTION_STATUS`: 获取连接状态

### 文件状态

- `pending`: 待上传
- `uploading`: 上传中
- `uploaded`: 已上传
- `failed`: 上传失败

## 🎯 预期效果

修复后，注入面板的上传功能应该能够：

1. ✅ 正确检测云盘页面
2. ✅ 成功与云盘内容脚本通信
3. ✅ 触发实际的文件上传流程
4. ✅ 显示详细的调试信息
5. ✅ 提供备用通信机制

## 📞 支持

如果仍然遇到问题，请：

1. 运行完整诊断脚本
2. 收集控制台日志
3. 检查网络连接
4. 确认扩展权限

---

**修复版本**: 1.0.1  
**修复日期**: 2025-01-31  
**修复内容**: 注入面板上传功能通信机制优化

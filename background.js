// 服务器配置
const SERVER_URL = 'http://***************:6655';

// WebSocket配置
const WS_HOST = '***************';
const WS_PORT = 6656;
const WS_URL = `ws://${WS_HOST}:${WS_PORT}`;

// 文件云流转页面URL
const FILE_CLOUD_URL = 'https://yunpan.gdcourts.gov.cn:82/';

// WebSocket状态枚举
const WS_STATE = {
    CONNECTING: 0,
    OPEN: 1,
    CLOSING: 2,
    CLOSED: 3
};

let ws = null;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 10;
let pendingFiles = [];
let fileCloudTabId = null;

// 心跳检测变量
let heartbeatInterval = null;
const HEARTBEAT_INTERVAL = 15000; // 15秒发送一次心跳
let lastPongTime = 0;
const HEARTBEAT_TIMEOUT = 30000; // 30秒没有响应就重连

// 连接状态管理
let isReconnecting = false;
let connectionCleanupInProgress = false;

// 缓存最新的文件列表
let cachedFiles = [];

// 定期请求刷新文件列表
let refreshInterval = null;
const REFRESH_INTERVAL = 30000; // 30秒刷新一次文件列表

// 存储等待响应的WebSocket请求
const pendingRequests = {};

// =================================================================
// 主从协调架构 - 全局上传锁和状态管理
// =================================================================

// 全局上传锁机制，防止popup和background同时执行上传操作
let globalUploadLock = {
    isLocked: false,
    lockedBy: null, // 'popup' 或 'background'
    timestamp: 0,
    operation: null // 当前锁定的操作类型
};

// Popup状态跟踪
let popupStatus = {
    isOpen: false,
    lastActiveTime: 0,
    lastHeartbeat: 0
};

// 后台自动化检测状态
let backgroundAutoUpload = {
    enabled: true, // 后台自动上传是否启用 - 默认启用
    detectionInterval: null, // 检测定时器
    intervalDuration: 30000, // 默认30秒检测一次
    lastFileCount: 0, // 上次检测的文件数量
    isRunning: false // 是否正在运行检测
};

// 自动刷新网页状态
let autoPageRefresh = {
    enabled: false, // 自动刷新是否启用
    interval: 300, // 刷新间隔（秒），默认5分钟
    timerId: null, // 定时器ID
    targetTabId: null, // 目标标签页ID
    lastRefreshTime: 0 // 上次刷新时间
};

// 检测频率配置
const DETECTION_INTERVALS = {
    popup_open_background: 60000,    // popup打开时background检测间隔(1分钟)
    popup_closed_background: 30000,  // popup关闭时background检测间隔(30秒)
    conflict_backoff: 120000        // 冲突时的退避间隔(2分钟)
};

/**
 * 获取上传锁
 * @param {string} requester - 请求者标识 ('popup' 或 'background')
 * @param {string} operation - 操作类型描述
 * @param {number} timeout - 锁超时时间，默认30秒
 * @returns {boolean} 是否成功获取锁
 */
async function acquireUploadLock(requester, operation = 'upload', timeout = 30000) {
    const now = Date.now();
    
    // 检查锁是否已被占用
    if (globalUploadLock.isLocked) {
        // 检查锁是否超时
        if (now - globalUploadLock.timestamp > timeout) {
            debug(`上传锁超时释放，原锁定者: ${globalUploadLock.lockedBy}`);
            releaseUploadLock();
        } else {
            debug(`上传锁被占用，当前锁定者: ${globalUploadLock.lockedBy}，请求者: ${requester}`);
            return false;
        }
    }
    
    // 获取锁
    globalUploadLock.isLocked = true;
    globalUploadLock.lockedBy = requester;
    globalUploadLock.timestamp = now;
    globalUploadLock.operation = operation;
    
    debug(`上传锁已获取，锁定者: ${requester}，操作: ${operation}`);
    return true;
}

/**
 * 释放上传锁
 * @param {string} requester - 释放者标识，用于验证
 */
function releaseUploadLock(requester = null) {
    if (requester && globalUploadLock.lockedBy !== requester) {
        debug(`释放锁失败，当前锁定者: ${globalUploadLock.lockedBy}，释放请求者: ${requester}`);
        return false;
    }
    
    const wasLocked = globalUploadLock.isLocked;
    const previousOwner = globalUploadLock.lockedBy;
    
    globalUploadLock.isLocked = false;
    globalUploadLock.lockedBy = null;
    globalUploadLock.timestamp = 0;
    globalUploadLock.operation = null;
    
    if (wasLocked) {
        debug(`上传锁已释放，前锁定者: ${previousOwner}`);
    }
    
    return true;
}

/**
 * 检查上传锁状态
 * @returns {Object} 锁状态信息
 */
function getUploadLockStatus() {
    return {
        isLocked: globalUploadLock.isLocked,
        lockedBy: globalUploadLock.lockedBy,
        operation: globalUploadLock.operation,
        lockedTime: globalUploadLock.timestamp,
        duration: globalUploadLock.timestamp ? Date.now() - globalUploadLock.timestamp : 0
    };
}

/**
 * 更新popup状态
 * @param {boolean} isOpen - popup是否打开
 * @param {string} source - 状态更新来源
 */
function updatePopupStatus(isOpen, source = 'unknown') {
    const now = Date.now();
    const wasOpen = popupStatus.isOpen;
    
    popupStatus.isOpen = isOpen;
    popupStatus.lastActiveTime = now;
    
    if (isOpen) {
        popupStatus.lastHeartbeat = now;
    }
    
    debug(`Popup状态更新: ${wasOpen ? '打开' : '关闭'} → ${isOpen ? '打开' : '关闭'}，来源: ${source}`);
    
    // 状态改变时调整后台检测策略
    if (wasOpen !== isOpen) {
        adjustBackgroundDetectionStrategy();
    }
}

/**
 * 根据popup状态调整后台检测策略
 */
function adjustBackgroundDetectionStrategy() {
    if (!backgroundAutoUpload.enabled) {
        return;
    }
    
    const newInterval = popupStatus.isOpen ? 
        DETECTION_INTERVALS.popup_open_background : 
        DETECTION_INTERVALS.popup_closed_background;
    
    if (backgroundAutoUpload.intervalDuration !== newInterval) {
        backgroundAutoUpload.intervalDuration = newInterval;
        
        debug(`调整后台检测频率: ${newInterval}ms (popup ${popupStatus.isOpen ? '已打开' : '已关闭'})`);
        
        // 重启检测定时器以应用新频率
        if (backgroundAutoUpload.detectionInterval) {
            restartBackgroundDetection();
        }
    }
}

/**
 * 检查WebSocket是否可用于发送消息
 * @returns {boolean} 是否可以安全发送消息
 */
function isWebSocketReady() {
    return ws && ws.readyState === WS_STATE.OPEN;
}

/**
 * 清理所有挂起的请求
 * @param {string} reason 清理原因
 */
function clearPendingRequests(reason = 'WebSocket连接已断开') {
    debug(`清理挂起的请求: ${reason}，共 ${Object.keys(pendingRequests).length} 个`);
    
    Object.values(pendingRequests).forEach(({ reject, timeoutId }) => {
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
        reject(new Error(reason));
    });
    
    // 清空pendingRequests对象
    Object.keys(pendingRequests).forEach(key => {
        delete pendingRequests[key];
    });
}

/**
 * 发送WebSocket请求并等待响应
 * @param {Object} requestData - 要发送的请求数据
 * @param {number} [timeout=30000] - 等待响应的超时时间(毫秒)
 * @returns {Promise} - 返回响应数据的Promise
 */
function sendWebSocketRequest(requestData, timeout = 30000) {
    return new Promise((resolve, reject) => {
        if (!isWebSocketReady()) {
            reject(new Error('WebSocket未连接或状态不可用'));
            return;
        }

        // 生成唯一请求ID
        const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // 添加请求ID到请求数据
        const dataToSend = { ...requestData, requestId };
        
        // 设置请求超时
        const timeoutId = setTimeout(() => {
            if (pendingRequests[requestId]) {
                delete pendingRequests[requestId];
                reject(new Error('WebSocket请求超时'));
            }
        }, timeout);
        
        // 存储请求处理器
        pendingRequests[requestId] = {
            resolve: (response) => {
                clearTimeout(timeoutId);
                delete pendingRequests[requestId];
                resolve(response);
            },
            reject: (error) => {
                clearTimeout(timeoutId);
                delete pendingRequests[requestId];
                reject(error);
            },
            timeoutId
        };
        
        // 发送请求
        try {
            ws.send(JSON.stringify(dataToSend));
            debug('已发送WebSocket请求', dataToSend);
        } catch (error) {
            clearTimeout(timeoutId);
            delete pendingRequests[requestId];
            reject(error);
        }
    });
}

// 处理WebSocket接收到的消息
function handleWebSocketMessage(event) {
    try {
        const message = JSON.parse(event.data);
        debug('收到WebSocket消息', message);

        // 检查是否为请求响应
        if (message.requestId && pendingRequests[message.requestId]) {
            const { resolve, reject } = pendingRequests[message.requestId];
            if (message.error) {
                reject(new Error(message.error));
            } else {
                resolve(message);
            }
            return;
        }

        // 处理不同类型的消息
        if (message.type === 'pong') {
            lastPongTime = Date.now();
            //debug('收到pong响应');
        } else if (message.type === 'files_updated') {
            // 服务器通知文件列表已更新，刷新缓存
            debug('文件列表已更新，正在刷新缓存');
            cachedFiles = message.files || [];
            chrome.storage.local.set({ cachedFiles });
            
            // 强制清理缩略图缓存，确保获取最新图像
            debug('文件列表更新，强制清理缩略图缓存');
            
            // 通知popup文件已更新并需要刷新缩略图
            notifyPopup({ 
                action: 'files_updated',
                clearCache: true,
                forceRefresh: true
            });
            
            // =================================================================
            // 主从协调架构：触发后台检测
            // =================================================================
            // 文件列表更新时，如果后台检测启用且popup未打开，立即触发一次检测
            if (backgroundAutoUpload.enabled && !popupStatus.isOpen) {
                debug('文件列表更新，触发后台自动检测');
                setTimeout(async () => {
                    await coordinatedAutoUploadCheck();
                }, 2000); // 延迟2秒以确保数据同步完成
            }
            // =================================================================
        } else if (message.type === 'admin_file_tree') {
            // 存储管理员文件树 - 增强版本
            debug('开始处理 admin_file_tree 消息', { hasPayload: !!message.payload });
            
            if (message.payload) {
                debug('开始保存文件树到本地存储', { 
                    files: message.payload.files?.length || 0,
                    folders: message.payload.folders?.length || 0
                });
                
                chrome.storage.local.set({ admin_file_tree: message.payload }, () => {
                    if (chrome.runtime.lastError) {
                        debug('保存文件树失败', chrome.runtime.lastError, 'error');
                    } else {
                        debug('文件树已保存到本地存储', { 
                            files: message.payload.files?.length || 0,
                            folders: message.payload.folders?.length || 0
                        }, 'success');
                        
                        // 立即通知所有popup实例更新 - 增强版
                        const notificationData = {
                            type: 'admin_tree_updated',
                            timestamp: Date.now(),
                            forceRefresh: true,
                            dataSource: 'websocket_live',
                            stats: {
                                files: message.payload.files?.length || 0,
                                folders: message.payload.folders?.length || 0
                            }
                        };
                        
                        // 立即发送通知
                        chrome.runtime.sendMessage(notificationData).catch(() => {
                            debug('发送文件树更新通知失败（popup可能未打开）');
                        });
                        
                        // 额外发送旧格式通知确保兼容性
                        notifyPopup({ 
                            action: 'admin_tree_updated',
                            timestamp: Date.now(),
                            forceUpdate: true
                        });
                        
                        debug('文件树更新通知已发送到所有popup');
                        
                        // =================================================================
                        // 主从协调架构：触发后台检测
                        // =================================================================
                        // 文件树更新时，如果后台检测启用且popup未打开，立即触发一次检测
                        if (backgroundAutoUpload.enabled && !popupStatus.isOpen) {
                            debug('文件树更新，触发后台自动检测');
                            setTimeout(async () => {
                                await coordinatedAutoUploadCheck();
                            }, 3000); // 延迟3秒以确保数据同步完成
                        }
                        // =================================================================
                    }
                });
            } else {
                debug('收到无效的文件树数据', message, 'warn');
            }
        } else if (message.type === 'file_uploaded') {
            // 文件上传成功通知
            notifyPopup({ 
                action: 'upload_success', 
                fileName: message.fileName,
                filePath: message.filePath
            });
        } else if (message.type === 'file_status_updated') {
            // 文件状态更新通知（来自服务器）
            debug('收到服务器文件状态更新通知', {
                filePath: message.filePath,
                status: message.status,
                previousStatus: message.previousStatus
            });
            
            // 更新本地缓存
            if (cachedFiles && Array.isArray(cachedFiles)) {
                const fileIndex = cachedFiles.findIndex(f => 
                    f.path === message.filePath || 
                    f.name === message.filePath ||
                    f.filename === message.filePath
                );
                
                if (fileIndex >= 0) {
                    cachedFiles[fileIndex] = {
                        ...cachedFiles[fileIndex],
                        status: message.status,
                        syncStatus: message.status,
                        lastUpdated: new Date().toISOString()
                    };
                    chrome.storage.local.set({ cachedFiles });
                    debug(`本地缓存已同步服务器状态更新: ${message.filePath}`);
                }
            }
            
            // 通知popup更新显示
            notifyPopup({ 
                type: 'file_status_updated',
                filename: message.filePath,
                status: message.status,
                timestamp: Date.now(),
                source: 'server'
            });
            
        } else if (message.type === 'file_status_removed') {
            // 文件状态移除通知（来自服务器）
            debug('收到服务器文件状态移除通知', {
                filePath: message.filePath,
                isFolder: message.isFolder,
                reason: message.reason || 'unknown'
            });
            
            // === 第一步：清理本地文件缓存 ===
            if (cachedFiles && Array.isArray(cachedFiles)) {
                let removedCount = 0;
                const originalLength = cachedFiles.length;
                
                cachedFiles = cachedFiles.filter(f => {
                    const filePath = f.path || f.name || f.filename;
                    
                    // 如果是要删除的文件，过滤掉
                    if (filePath === message.filePath) {
                        removedCount++;
                        debug(`从缓存中移除文件: ${filePath}`);
                        return false;
                    }
                    
                    // 如果是文件夹，也过滤掉所有子文件
                    if (message.isFolder && filePath && filePath.startsWith(message.filePath + '/')) {
                        removedCount++;
                        debug(`从缓存中移除子文件: ${filePath}`);
                        return false;
                    }
                    
                    return true;
                });
                
                if (removedCount > 0) {
                    chrome.storage.local.set({ cachedFiles });
                    debug(`已从本地缓存中移除 ${removedCount} 个文件: ${message.filePath}`, {
                        originalLength: originalLength,
                        newLength: cachedFiles.length,
                        removedCount: removedCount
                    });
                }
            }
            
            // === 第二步：清理Chrome storage中的uploadedFiles ===
            chrome.storage.local.get(['uploadedFiles'], (result) => {
                let uploadedFiles = result.uploadedFiles || [];
                if (Array.isArray(uploadedFiles)) {
                    const originalLength = uploadedFiles.length;
                    
                    // 移除指定文件和子文件（如果是文件夹）
                    uploadedFiles = uploadedFiles.filter(filename => {
                        // 直接匹配文件路径
                        if (filename === message.filePath) {
                            debug(`从Chrome storage uploadedFiles中移除: ${filename}`);
                            return false;
                        }
                        
                        // 匹配文件名（兼容性处理）
                        const fileNameOnly = message.filePath.split('/').pop();
                        if (filename === fileNameOnly) {
                            debug(`从Chrome storage uploadedFiles中移除文件名匹配: ${filename}`);
                            return false;
                        }
                        
                        // 如果是文件夹，移除所有子文件
                        if (message.isFolder && filename.startsWith(message.filePath + '/')) {
                            debug(`从Chrome storage uploadedFiles中移除子文件: ${filename}`);
                            return false;
                        }
                        
                        return true;
                    });
                    
                    const removedCount = originalLength - uploadedFiles.length;
                    if (removedCount > 0) {
                        chrome.storage.local.set({ uploadedFiles }, () => {
                            if (chrome.runtime.lastError) {
                                debug('保存清理后的uploadedFiles失败', chrome.runtime.lastError, 'error');
                            } else {
                                debug(`已从Chrome storage uploadedFiles中移除 ${removedCount} 个文件: ${message.filePath}`, {
                                    originalLength: originalLength,
                                    newLength: uploadedFiles.length,
                                    removedCount: removedCount
                                });
                            }
                        });
                    }
                }
            });
            
            // === 第三步：清理内存中的uploadedFiles Set ===
            if (typeof uploadedFiles !== 'undefined' && uploadedFiles instanceof Set) {
                const originalSize = uploadedFiles.size;
                let removedFromMemory = 0;
                
                // 生成所有可能的路径变体进行清理
                const pathVariants = [
                    message.filePath,
                    message.filePath.split('/').pop(), // 仅文件名
                    '/' + message.filePath.split('/').pop(), // 带前缀的文件名
                    message.filePath.replace(/^\/+/, ''), // 去掉前导斜杠
                    message.filePath.replace(/\\/g, '/') // 规范化路径分隔符
                ];
                
                // 移除所有路径变体
                pathVariants.forEach(variant => {
                    if (uploadedFiles.has(variant)) {
                        uploadedFiles.delete(variant);
                        removedFromMemory++;
                        debug(`从内存uploadedFiles Set中移除路径变体: ${variant}`);
                    }
                });
                
                // 如果是文件夹，清理所有子文件
                if (message.isFolder) {
                    const itemsToRemove = [];
                    uploadedFiles.forEach(item => {
                        if (item.startsWith(message.filePath + '/')) {
                            itemsToRemove.push(item);
                        }
                    });
                    
                    itemsToRemove.forEach(item => {
                        uploadedFiles.delete(item);
                        removedFromMemory++;
                        debug(`从内存uploadedFiles Set中移除子文件: ${item}`);
                    });
                }
                
                if (removedFromMemory > 0) {
                    debug(`已从内存uploadedFiles Set中移除 ${removedFromMemory} 个项目: ${message.filePath}`, {
                        originalSize: originalSize,
                        newSize: uploadedFiles.size,
                        removedCount: removedFromMemory
                    });
                }
            }
            
            // === 第四步：清理其他可能的状态缓存 ===
            // 清理可能存在的文件状态缓存
            chrome.storage.local.get(['fileStatuses', 'syncStatuses'], (result) => {
                let needsUpdate = false;
                const updates = {};
                
                // 清理fileStatuses
                if (result.fileStatuses && typeof result.fileStatuses === 'object') {
                    const fileStatuses = { ...result.fileStatuses };
                    let removedStatusCount = 0;
                    
                    Object.keys(fileStatuses).forEach(statusPath => {
                        if (statusPath === message.filePath || 
                            (message.isFolder && statusPath.startsWith(message.filePath + '/'))) {
                            delete fileStatuses[statusPath];
                            removedStatusCount++;
                        }
                    });
                    
                    if (removedStatusCount > 0) {
                        updates.fileStatuses = fileStatuses;
                        needsUpdate = true;
                        debug(`从fileStatuses中清理了 ${removedStatusCount} 个状态记录`);
                    }
                }
                
                // 清理syncStatuses
                if (result.syncStatuses && typeof result.syncStatuses === 'object') {
                    const syncStatuses = { ...result.syncStatuses };
                    let removedSyncCount = 0;
                    
                    Object.keys(syncStatuses).forEach(statusPath => {
                        if (statusPath === message.filePath || 
                            (message.isFolder && statusPath.startsWith(message.filePath + '/'))) {
                            delete syncStatuses[statusPath];
                            removedSyncCount++;
                        }
                    });
                    
                    if (removedSyncCount > 0) {
                        updates.syncStatuses = syncStatuses;
                        needsUpdate = true;
                        debug(`从syncStatuses中清理了 ${removedSyncCount} 个状态记录`);
                    }
                }
                
                // 如果有需要更新的数据，保存到storage
                if (needsUpdate) {
                    chrome.storage.local.set(updates, () => {
                        if (chrome.runtime.lastError) {
                            debug('保存清理后的状态缓存失败', chrome.runtime.lastError, 'error');
                        } else {
                            debug('状态缓存清理完成', updates);
                        }
                    });
                }
            });
            
            // === 第五步：通知popup更新显示 ===
            notifyPopup({ 
                type: 'file_status_removed',
                filename: message.filePath,
                isFolder: message.isFolder,
                timestamp: Date.now(),
                source: 'server',
                reason: message.reason || 'deleted',
                clearCache: true // 通知popup也清理本地缓存
            });
            
            debug(`文件状态清理完成: ${message.filePath}${message.isFolder ? ' (文件夹)' : ''}`, {
                reason: message.reason || 'deleted',
                timestamp: message.timestamp
            }, 'success');
            
        } else if (message.type === 'new_files_detected') {
            // 新文件检测通知
            debug('收到新文件检测通知', {
                count: message.count,
                files: message.files
            });
            
            // 通知popup有新文件，可能触发自动上传
            notifyPopup({ 
                type: 'new_files_detected',
                files: message.files,
                count: message.count,
                timestamp: Date.now()
            });
            
            // 强制刷新文件树
            setTimeout(() => {
                if (isWebSocketReady()) {
                    sendMessage('get_full_admin_file_tree', {
                        timestamp: Date.now(),
                        forceRefresh: true,
                        reason: 'new_files_detected'
                    });
                }
            }, 500);
            
        } else if (message.type === 'file_status_update_response') {
            // 文件状态更新响应
            debug('收到文件状态更新响应', {
                filename: message.filename,
                status: message.status,
                success: message.success
            });
            
        } else if (message.type === 'file_status_response') {
            // 文件状态查询响应
            debug('收到文件状态查询响应', {
                filename: message.filename,
                status: message.status
            });
            
        } else if (message.type === 'error') {
            debug('服务器返回错误:', message.error);
            notifyPopup({ action: 'error', message: message.error });
        } else {
            debug('收到未知类型的消息:', message);
        }
    } catch (err) {
        console.error('解析WebSocket消息出错:', err, event.data);
    }
}

// 调试函数 - 增强版
function debug(message, data = null, level = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logPrefix = `[文件云流转助手 ${timestamp}]`;
    const fullMessage = `${logPrefix} ${message}${data ? ': ' + JSON.stringify(data) : ''}`;
    
    // 根据日志级别使用不同的console方法
    switch(level) {
        case 'error':
            console.error(fullMessage);
            break;
        case 'warn':
            console.warn(fullMessage);
            break;
        case 'success':
            console.log(`%c${fullMessage}`, 'color: green; font-weight: bold;');
            break;
        case 'info':
        default:
            console.log(fullMessage);
            break;
    }
    
    // 发送日志到popup（仅发送重要消息或存储相关消息）
    try {
        const isStorageRelated = message.includes('存储') || message.includes('storage') || message.includes('文件树');
        if (level === 'error' || level === 'warn' || isStorageRelated) {
            chrome.runtime.sendMessage({
                type: 'DEBUG_LOG',
                level,
                message,
                data,
                timestamp
            }).catch(() => {
                // 忽略发送失败
            });
        }
    } catch (e) {
        // 忽略错误
    }
}

// 发送消息到popup
function sendToPopup(type, data) {
    chrome.runtime.sendMessage({ type, ...data });
}

// 发送消息的辅助函数 - 增强版
function sendMessage(type, data = {}) {
    // 更严格的状态检查
    if (!ws) {
        debug('WebSocket对象不存在，无法发送消息');
        return false;
    }
    
    if (ws.readyState === WS_STATE.CONNECTING) {
        debug('WebSocket正在连接中，无法发送消息');
        return false;
    }
    
    if (ws.readyState === WS_STATE.CLOSING) {
        debug('WebSocket正在关闭中，无法发送消息');
        return false;
    }
    
    if (ws.readyState === WS_STATE.CLOSED) {
        debug('WebSocket已关闭，无法发送消息');
        return false;
    }
    
    if (ws.readyState !== WS_STATE.OPEN) {
        debug(`WebSocket状态异常: ${ws.readyState}，无法发送消息`);
        return false;
    }

    try {
        const message = JSON.stringify({
            type,
            ...data
        });
        debug('发送消息', { type, data });
        ws.send(message);
        return true;
    } catch (error) {
        debug('发送消息失败', error);
        return false;
    }
}

// 启动心跳检测 - 改进版
function startHeartbeat() {
    stopHeartbeat(); // 先停止可能存在的心跳
    
    lastPongTime = Date.now(); // 初始化最后一次pong时间
    
    heartbeatInterval = setInterval(() => {
        // 检查是否正在清理连接
        if (connectionCleanupInProgress) {
            debug('连接清理中，跳过心跳检测');
            return;
        }
        
        // 检查WebSocket状态
        if (!isWebSocketReady()) {
            debug('WebSocket未就绪，停止心跳');
            stopHeartbeat();
            return;
        }
        
        // 检查上次心跳响应是否超时
        if (Date.now() - lastPongTime > HEARTBEAT_TIMEOUT) {
            debug('心跳超时，重新连接WebSocket');
            stopHeartbeat();
            reconnectWebSocket();
            return;
        }
        
        // 发送ping消息
        try {
            if (sendMessage('ping')) {
            debug('发送心跳ping');
            } else {
                debug('心跳发送失败，可能需要重连');
                stopHeartbeat();
                reconnectWebSocket();
            }
        } catch (e) {
            debug('发送心跳异常', e);
            stopHeartbeat();
            reconnectWebSocket();
        }
    }, HEARTBEAT_INTERVAL);
    
    debug('心跳检测已启动');
}

// 停止心跳检测
function stopHeartbeat() {
    if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
        heartbeatInterval = null;
        debug('心跳检测已停止');
    }
}

// 重连WebSocket - 改进版
function reconnectWebSocket() {
    if (isReconnecting) {
        debug('重连已在进行中，跳过重复重连');
        return;
    }
    
    isReconnecting = true;
    debug('开始重新连接WebSocket');
    
    // 先清理当前连接
    cleanupWebSocket();
    
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
        reconnectAttempts++;
        debug(`第 ${reconnectAttempts} 次重连尝试，共 ${MAX_RECONNECT_ATTEMPTS} 次`);
        
        const delay = Math.min(3000 * reconnectAttempts, 30000); // 最大延迟30秒
        setTimeout(() => {
            isReconnecting = false;
            initWebSocket();
        }, delay);
    } else {
        debug('达到最大重连次数，停止重连');
        isReconnecting = false;
        
        // 尝试定时完全重启连接
        setTimeout(() => {
            reconnectAttempts = 0;
            isReconnecting = false;
            initWebSocket();
        }, 60000); // 1分钟后重试
    }
}

/**
 * 清理WebSocket连接和相关资源
 */
function cleanupWebSocket() {
    connectionCleanupInProgress = true;
    debug('开始清理WebSocket连接');
    
    // 停止心跳检测
    stopHeartbeat();
    
    // 停止定期刷新
    stopPeriodicRefresh();
    
    // 清理挂起的请求
    clearPendingRequests('连接正在重置');
    
    // 关闭WebSocket连接
    if (ws) {
        try {
            if (ws.readyState === WS_STATE.OPEN || ws.readyState === WS_STATE.CONNECTING) {
                ws.close(1000, '主动关闭连接');
            }
        } catch (e) {
            debug('关闭WebSocket连接时出错', e);
        }
        ws = null;
    }
    
    // 更新连接状态
    chrome.runtime.sendMessage({
        type: 'CONNECTION_STATUS_CHANGED',
        connected: false
    }).catch(() => {
        // 忽略发送失败的错误
    });
    
    connectionCleanupInProgress = false;
    debug('WebSocket连接清理完成');
}

// 启动定期刷新 - 增强版，增加文件树轮询
function startPeriodicRefresh() {
    stopPeriodicRefresh(); // 先停止可能存在的定时器
    
    // 启动文件树轮询 - 每15秒请求一次最新文件树
    refreshInterval = setInterval(() => {
        if (isWebSocketReady()) {
            debug('定期轮询文件树数据');
            // 发送带时间戳的文件树请求，确保获取最新数据
            sendMessage('get_full_admin_file_tree', {
                timestamp: Date.now(),
                source: 'periodic_refresh',
                forceRefresh: true
            });
        } else {
            debug('WebSocket未连接，跳过定期轮询');
        }
    }, 15000); // 15秒轮询一次
    
    debug('定期文件树轮询已启动 (每15秒)');
}

// 停止定期刷新
function stopPeriodicRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
        debug('定期刷新已停止');
    }
}

// 初始化WebSocket连接 - 改进版
function initWebSocket() {
    if (connectionCleanupInProgress) {
        debug('连接清理中，延迟初始化', null, 'warn');
        setTimeout(initWebSocket, 1000);
        return;
    }
    
    // 确保旧连接完全清理
    cleanupWebSocket();

    debug(`正在连接WebSocket: ${WS_URL}`, { 
        attempt: reconnectAttempts + 1,
        maxAttempts: MAX_RECONNECT_ATTEMPTS,
        isReconnecting: isReconnecting
    }, 'info');
    
    try {
    ws = new WebSocket(WS_URL);
        
        // 记录连接开始时间
        const connectionStartTime = Date.now();

    ws.onopen = () => {
            const connectionTime = Date.now() - connectionStartTime;
            debug('WebSocket连接成功', { 
                connectionTime: `${connectionTime}ms`,
                readyState: ws ? ws.readyState : 'null',
                url: ws ? ws.url : 'null',
                protocol: ws ? (ws.protocol || '默认') : 'null'
            }, 'success');
            
        reconnectAttempts = 0;
            isReconnecting = false;
            lastPongTime = Date.now();
            
            // 更新连接状态
            chrome.runtime.sendMessage({
                type: 'CONNECTION_STATUS_CHANGED',
                connected: true,
                connectionTime,
                timestamp: Date.now()
            }).catch(() => {
                // 忽略发送失败的错误
            });
        
        // 发送初始化消息
            if (sendMessage('init', { client: 'chrome-extension', version: '1.0.0' })) {
                debug('初始化消息已发送', null, 'info');
            } else {
                debug('初始化消息发送失败', null, 'error');
            }

            // 请求完整的管理员文件树
            if (sendMessage('get_full_admin_file_tree')) {
                debug('文件树请求已发送', null, 'info');
            } else {
                debug('文件树请求发送失败', null, 'error');
            }
            
            // 启动心跳检测
            startHeartbeat();
            
            // 启动定期轮询机制
            startPeriodicRefresh();
        };

        ws.onclose = (event) => {
            const closeInfo = {
                code: event.code,
                reason: event.reason || '无原因',
                wasClean: event.wasClean,
                readyState: ws ? ws.readyState : 'null'
            };
            
            debug(`WebSocket连接关闭`, closeInfo, 'warn');
            
            // 根据关闭代码提供更详细的信息
            let closeReason = '';
            switch(event.code) {
                case 1000:
                    closeReason = '正常关闭';
                    break;
                case 1001:
                    closeReason = '端点离开（页面关闭或导航离开）';
                    break;
                case 1002:
                    closeReason = '协议错误';
                    break;
                case 1003:
                    closeReason = '不支持的数据类型';
                    break;
                case 1006:
                    closeReason = '异常关闭（网络错误或服务器崩溃）';
                    break;
                case 1011:
                    closeReason = '服务器错误';
                    break;
                default:
                    closeReason = `未知关闭代码: ${event.code}`;
            }
            
            debug(`关闭原因分析: ${closeReason}`, null, 'info');
            
            // 清理资源（但不清理WebSocket对象，因为已经关闭）
            stopHeartbeat();
            stopPeriodicRefresh();
            clearPendingRequests('WebSocket连接已关闭');
            
            // 更新连接状态
            chrome.runtime.sendMessage({
                type: 'CONNECTION_STATUS_CHANGED',
                connected: false,
                closeCode: event.code,
                closeReason,
                timestamp: Date.now()
            }).catch(() => {
                // 忽略发送失败的错误
            });
            
            ws = null;
            
            // 如果不是主动关闭，则尝试重连
            if (event.code !== 1000 && !connectionCleanupInProgress) {
                debug(`非主动关闭(${event.code})，尝试自动重连`, null, 'warn');
            reconnectWebSocket();
            } else {
                debug('主动关闭连接，不进行重连', null, 'info');
            }
    };

    ws.onerror = (error) => {
            debug('WebSocket错误', { 
                error: error.toString(),
                readyState: ws ? ws.readyState : 'null',
                url: ws ? ws.url : 'null'
            }, 'error');
            // 错误处理交给onclose事件
    };

    ws.onmessage = (event) => {
        try {
                // 更新最后一次通信时间
                lastPongTime = Date.now();
                
                // 处理简单的pong消息
                if (event.data === 'pong' || event.data === '{"type":"pong"}') {
                    debug('收到 pong', { timestamp: lastPongTime }, 'info');
                    return;
                }
                
            const message = JSON.parse(event.data);
                
                // 处理ping消息，自动回复pong
                if (message.type === 'ping') {
                    debug('收到 ping，回复 pong', null, 'info');
                    sendMessage('pong');
                    return;
                }
                
                debug('收到WebSocket消息', { 
                    type: message.type,
                    hasData: !!message.data || !!message.payload,
                    messageSize: event.data.length
                }, 'info');
                
                handleWebSocketMessage({ data: JSON.stringify(message) });
        } catch (error) {
                debug('解析消息失败', { 
                    error: error.message,
                    rawData: event.data.substring(0, 100) + (event.data.length > 100 ? '...' : '')
                }, 'error');
        }
    };
    } catch (error) {
        debug('WebSocket初始化失败', { 
            error: error.message,
            stack: error.stack
        }, 'error');
        ws = null;
        reconnectWebSocket();
    }
}

// 广播新文件信息到所有标签页
async function broadcastNewFile(fileInfo) {
    debug('广播新文件信息', fileInfo);
    
    // 添加到待处理文件队列
    pendingFiles.push(fileInfo);
    
    // 检查是否已经有上传请求在进行中
    if (window._uploadRequestInProgress) {
        debug('已有上传请求在进行中，不重复触发');
        return;
    }
    
    // 查找文件云流转页面标签
    const tabs = await chrome.tabs.query({});
    let fileCloudTab = tabs.find(tab => {
        return tab.url && (
            tab.url.includes('文件云流转') || 
            tab.url.includes(FILE_CLOUD_URL)
        );
    });
    
    if (fileCloudTab) {
        fileCloudTabId = fileCloudTab.id;
        // 发送消息到文件云流转页面
        try {
            await chrome.tabs.sendMessage(fileCloudTabId, {
                type: 'START_UPLOAD_PROCESS'
            });
        } catch (error) {
            debug('发送消息到文件云流转页面失败', error);
            // 如果发送失败，尝试打开新的文件云流转页面
            openFileCloudPage();
        }
    } else {
        // 如果没有找到文件云流转页面，打开一个新页面
        debug('未找到文件云流转页面，将打开新页面');
        openFileCloudPage();
    }
}

// 打开文件云流转页面
async function openFileCloudPage(fileToUpload = null) {
    debug('打开文件云流转页面');
    
    // 检查是否已有文件云流转页面标签
    const tabs = await chrome.tabs.query({});
    const fileCloudTab = tabs.find(tab => {
        return tab.url && (
            tab.url.includes('文件云流转') || 
            tab.url.includes(FILE_CLOUD_URL)
        );
    });
    
    if (fileCloudTab) {
        // 如果已有标签，激活它
        fileCloudTabId = fileCloudTab.id;
        await chrome.tabs.update(fileCloudTabId, { active: true });
        await chrome.windows.update(fileCloudTab.windowId, { focused: true });
        
        // 通知内容脚本开始处理文件
        setTimeout(async () => {
            try {
                // 如果有指定文件要上传，则传递文件信息
                if (fileToUpload) {
                    await chrome.tabs.sendMessage(fileCloudTabId, { 
                        type: 'START_UPLOAD_PROCESS',
                        fileToUpload: fileToUpload
                    });
                } else {
                    await chrome.tabs.sendMessage(fileCloudTabId, { 
                        type: 'START_UPLOAD_PROCESS' 
                    });
                }
            } catch (error) {
                debug('发送消息到文件云流转页面失败', error);
            }
        }, 1000);
    } else {
        // 如果没有，创建新标签
        const newTab = await chrome.tabs.create({ url: FILE_CLOUD_URL });
        fileCloudTabId = newTab.id;
        
        // 等待页面加载完成后发送消息
        chrome.tabs.onUpdated.addListener(function listener(tabId, changeInfo) {
            if (tabId === fileCloudTabId && changeInfo.status === 'complete') {
                chrome.tabs.onUpdated.removeListener(listener);
                
                // 等待内容脚本注入完成
                setTimeout(async () => {
                    try {
                        // 如果有指定文件要上传，则传递文件信息
                        if (fileToUpload) {
                            await chrome.tabs.sendMessage(fileCloudTabId, { 
                                type: 'START_UPLOAD_PROCESS',
                                fileToUpload: fileToUpload
                            });
                        } else {
                            await chrome.tabs.sendMessage(fileCloudTabId, { 
                                type: 'START_UPLOAD_PROCESS' 
                            });
                        }
                    } catch (error) {
                        debug('发送消息到文件云流转页面失败', error);
                    }
                }, 2000);
            }
        });
    }
}

// 处理文件上传到文件云
async function handleFileUploadToCloud(fileData) {
    debug('处理文件上传到文件云', fileData);
    
    if (!fileCloudTabId) {
        debug('文件云流转页面未打开');
        return;
    }
    
    // 发送消息给内容脚本，通知开始上传处理
    try {
        await chrome.tabs.sendMessage(fileCloudTabId, {
            type: 'START_UPLOAD_PROCESS',
            fileData: fileData
        });
    } catch (error) {
        debug('发送上传请求失败', error);
    }
}

// 在页面中执行的上传文件函数
function uploadFileInPage(fileData) {
    console.log('[文件云流转助手] 在页面中执行上传', fileData);
    
    // 移除模拟上传操作，改为使用content.js中的文件处理逻辑
    chrome.runtime.sendMessage({
        type: 'START_UPLOAD_PROCESS'
    });
}

// 处理开始上传
async function handleStartUpload() {
    debug('处理开始上传请求');
    
    // 防止重复发送上传请求
    if (window._uploadRequestInProgress) {
        debug('上传请求已在进行中，避免重复请求');
        return;
    }
    
    window._uploadRequestInProgress = true;
    
    try {
        // 清理content.js中的上传跟踪器
        if (fileCloudTabId) {
            try {
                debug('清理上传跟踪器');
                await chrome.tabs.sendMessage(fileCloudTabId, {
                    type: 'CLEAR_UPLOAD_TRACKER'
                });
            } catch (error) {
                debug('清理上传跟踪器失败，可能页面未加载完成', error);
            }
        }
    
        // 检查是否有文件云流转页面标签
        if (!fileCloudTabId) {
            debug('没有打开的文件云流转页面');
            await openFileCloudPage();
        }
        
        // 获取待上传文件
        const pendingFiles = cachedFiles.filter(f => !f.status || f.status === 'pending');
        
        if (pendingFiles.length === 0) {
            debug('没有待上传的文件');
            window._uploadRequestInProgress = false;
            return;
        }
        
        debug(`发现 ${pendingFiles.length} 个待上传文件，通知内容脚本处理`);
        
        // 发送消息给内容脚本，开始上传处理
        try {
            await chrome.tabs.sendMessage(fileCloudTabId, {
                type: 'START_UPLOAD_PROCESS'
            });
        } catch (error) {
            debug('发送上传请求到内容脚本失败', error);
            // 如果发送失败，尝试重新打开页面
            await openFileCloudPage();
        }
        
        // 5秒后清除上传请求标记，允许新的上传请求
        setTimeout(() => {
            window._uploadRequestInProgress = false;
            debug('上传请求标记已清除，允许新的上传请求');
        }, 5000);
    } catch (error) {
        debug('处理上传请求出错', error);
        window._uploadRequestInProgress = false;
    }
}

// 检查WebSocket连接状态
function checkWebSocketConnection() {
    const isConnected = isWebSocketReady();
    debug(`检查WebSocket连接状态: ${isConnected ? '已连接' : '未连接'}`);
    
    if (!isConnected) {
        debug('WebSocket未连接，尝试重新连接');
        // 只有在没有正在重连时才尝试重连
        if (!isReconnecting && !connectionCleanupInProgress) {
        reconnectWebSocket();
    } else {
            debug('重连已在进行中，跳过此次检查');
        }
    } else {
        // 连接正常，重置重连计数器
        if (reconnectAttempts > 0) {
            debug('连接已恢复，重置重连计数器');
            reconnectAttempts = 0;
        }
    }
    
    return isConnected;
}

// 先移除所有可能的旧监听器，避免冲突
try {
    if (chrome.runtime.onMessage.hasListeners()) {
        // Note: Chrome extensions don't have a direct way to remove *all* listeners.
        // This is a best-effort attempt. If issues persist, manual review might be needed.
        // We rely on the fact that adding the listener below might replace existing ones
        // or that the old listener code was fully removed.
        debug('尝试清理旧的消息监听器 (注意: 可能不完全可靠)');
    }
} catch (e) {
    console.error('检查监听器时出错:', e);
}

// 添加新的监听器
const messageHandler = (request, sender, sendResponse) => {
  debug('收到内部消息 (新监听器):', request);
  
  // 首先尝试主从协调架构消息处理
  const coordinationResult = handleCoordinationMessage(request, sender, sendResponse);
  if (coordinationResult !== undefined) {
    return coordinationResult;
  }
  
  if (request.type === 'GET_ADMIN_TREE') {
    debug('Popup 请求文件树数据');
    chrome.storage.local.get('admin_file_tree', (result) => {
      if (chrome.runtime.lastError) {
        console.error('读取 admin_file_tree 失败:', chrome.runtime.lastError);
        sendResponse({ success: false, error: chrome.runtime.lastError.message });
      } else {
        debug('发送存储的文件树给 Popup', result.admin_file_tree ? '(数据存在)' : '(空)'); // 避免打印整个树
        sendResponse({ success: true, tree: result.admin_file_tree || null });
      }
    });
    return true; // Indicates that the response is sent asynchronously
  } else if (request.type === 'GET_CONNECTION_STATUS') {
    const isConnected = checkWebSocketConnection();
    debug(`响应 GET_CONNECTION_STATUS: ${isConnected}`);
    sendResponse({ connected: isConnected });
    return false; // Synchronous response
  } else if (request.type === 'RESET_CONNECTION') {
    debug('收到重置连接请求');
    try {
      // 强制清理并重新连接
      cleanupWebSocket();
      setTimeout(() => {
        reconnectAttempts = 0;
        isReconnecting = false;
        initWebSocket();
        debug('连接重置完成', null, 'success');
        sendResponse({ success: true, message: '连接已重置' });
      }, 1000);
    } catch (error) {
      debug(`重置连接失败: ${error.message}`, null, 'error');
      sendResponse({ success: false, error: error.message });
    }
    return true; // 异步响应
  } else if (request.type === 'GET_IMAGE_THUMBNAIL') {
    debug('收到图像缩略图请求:', request.filePath);
      
    (async () => {
        try {
            const result = await getImageThumbnail(request.filePath);
            
            if (result.success) {
                sendResponse(result);
            } else {
                // 主路径失败，尝试备选路径
                debug('主路径失败，尝试备选路径:', request.filePath);
                
                if (request.alternatePaths && Array.isArray(request.alternatePaths)) {
                    for (const altPath of request.alternatePaths) {
                        if (altPath && altPath !== request.filePath) {
                            debug('尝试备选路径:', altPath);
                            const altResult = await getImageThumbnail(altPath);
                            
                            if (altResult.success) {
                                sendResponse(altResult);
                                return;
                }
              }
                    }
                }
                
                // 所有路径都失败
                debug('所有路径（包括备选）都失败');
                sendResponse({
                    success: false,
                    error: result.error || '无法获取缩略图'
                });
          }
        } catch (error) {
            console.error('处理缩略图请求时出错:', error);
            sendResponse({
                success: false,
                error: `处理请求失败: ${error.message}`
            });
        }
    })();
    
    return true; // 保持异步响应通道开放
  } else if (request.type === 'OPEN_FILE_CLOUD_PAGE') {
    debug('请求打开文件云页面');
    
    // 检查是否包含要上传的文件信息
    if (request.fileToUpload) {
      debug('收到文件上传请求', request.fileToUpload);
      // 保存文件信息到临时变量
      pendingFiles = [request.fileToUpload];
      
      // 打开文件云页面并进行上传
      openFileCloudPage(request.fileToUpload)
        .then(() => {
          sendResponse({ success: true, message: '文件云页面已打开，准备上传文件' });
        })
        .catch(error => {
          debug('打开文件云页面失败', error);
          sendResponse({ success: false, error: error.message });
        });
    } else {
      // 只打开页面，不上传文件
      openFileCloudPage()
        .then(() => {
          sendResponse({ success: true, message: '文件云页面已打开' });
        })
        .catch(error => {
          debug('打开文件云页面失败', error);
          sendResponse({ success: false, error: error.message });
        });
    }
    return true; // 异步响应
  } else if (request.type === 'GET_FILE_DATA') {
    // 处理文件数据请求
    if (request.filename) {
      debug(`收到文件数据请求: ${request.filename}`);
      handleGetFileData(request, sendResponse);
      return true; // 异步响应
    } else {
      sendResponse({ success: false, error: '未提供文件名' });
      return false; // 同步响应
    }
  } else if (request.type === 'START_YUNPAN_UPLOAD') {
    debug('收到启动云盘上传请求');
    handleStartYunpanUpload(request, sendResponse);
    return true; // 异步响应
  } else if (request.type === 'START_YUNPAN_UPLOAD_STRING') {
    debug('收到字符串化云盘上传请求');
    handleStartYunpanUploadString(request, sendResponse);
    return true; // 异步响应
  } else if (request.type === 'TRIGGER_AUTO_UPLOAD') {
    debug('收到自动上传触发请求');
    handleTriggerAutoUpload(request, sendResponse);
    return true; // 异步响应
  } else if (request.type === 'CHECK_YUNPAN_PAGE') {
    debug('收到检查云盘页面请求');
    handleCheckYunpanPage(sendResponse);
    return true; // 异步响应
  } else if (request.type === 'SEND_WS_MESSAGE') {
      if(request.wsMessage) {
          debug('转发WebSocket消息', request.wsMessage);
          
          // 特殊处理文件树请求，确保获取最新数据
          if (request.wsMessage.type === 'get_full_admin_file_tree') {
              debug('处理文件树刷新请求，强制获取最新数据');
              
              // 添加时间戳确保服务器知道这是一个新请求
              const enhancedMessage = {
                  ...request.wsMessage,
                  timestamp: Date.now(),
                  forceRefresh: true,
                  clientId: 'chrome-extension-' + Date.now()
              };
              
              const sent = sendMessage(enhancedMessage.type, {
                  timestamp: enhancedMessage.timestamp,
                  forceRefresh: enhancedMessage.forceRefresh,
                  clientId: enhancedMessage.clientId
              });
              
              if (sent) {
                  debug('文件树刷新请求已发送', enhancedMessage);
                  sendResponse({ success: true, message: '文件树刷新请求已发送' });
              } else {
                  debug('WebSocket未连接，尝试重新连接');
                  initWebSocket(); // 尝试重新连接
                  sendResponse({ success: false, error: 'WebSocket未连接，已尝试重新连接' });
              }
          } else {
              // 其他消息正常处理
          const sent = sendMessage(request.wsMessage.type, request.wsMessage.data || {});
           if (sent) {
                  sendResponse({ success: true });
           } else {
               sendResponse({ success: false, error: 'WebSocket未连接或发送失败' });
                  initWebSocket(); // 尝试重连
                    }
          }
          return true; // 异步响应
                } else {
          sendResponse({ success: false, error: '无效的 SEND_WS_MESSAGE 请求'});
          return false; // 同步响应
                }
  } else if (request.type === 'UPDATE_FILE_STATUS') {
    debug('收到文件状态更新请求', { 
      filename: request.filename, 
      status: request.status,
      forceUpdate: request.forceUpdate,
      isNewUpload: request.isNewUpload
    });
    
    if (!request.filename || !request.status) {
      sendResponse({ success: false, error: '缺少文件名或状态' });
      return false;
    }
    
    try {
      // === 状态更新调试追踪 ===
      const debugInfo = {
        filename: request.filename,
        newStatus: request.status,
        forceUpdate: request.forceUpdate || false,
        isNewUpload: request.isNewUpload || false,
        timestamp: Date.now(),
        processId: Math.random().toString(36).substr(2, 9)
      };
      
      debug(`[状态更新 ${debugInfo.processId}] 开始处理状态更新`, debugInfo);
      
      // 1. 通过WebSocket发送状态更新到服务器（这是唯一的数据源）
      if (isWebSocketReady()) {
        const sent = sendMessage('update_file_status', {
          filename: request.filename,
          status: request.status,
          timestamp: debugInfo.timestamp,
          forceUpdate: request.forceUpdate,
          isNewUpload: request.isNewUpload,
          processId: debugInfo.processId
        });
        
        if (sent) {
          debug(`[状态更新 ${debugInfo.processId}] WebSocket消息已发送到服务器（唯一数据源）`, {
            filename: request.filename,
            status: request.status
          });
        } else {
          debug(`[状态更新 ${debugInfo.processId}] WebSocket发送失败，无法更新状态`);
          sendResponse({ 
            success: false, 
            error: 'WebSocket连接失败，无法更新状态' 
          });
          return false;
        }
      } else {
        debug(`[状态更新 ${debugInfo.processId}] WebSocket未连接，无法发送状态更新`);
        sendResponse({ 
          success: false, 
          error: 'WebSocket未连接，无法更新状态' 
        });
        return false;
      }
      
      // 2. 移除本地缓存更新逻辑 - 状态完全由服务器管理
      // 等待服务器通过WebSocket广播状态更新给所有客户端
      
      // 3. 不再主动通知popup，等待服务器广播
      // 服务器会发送 'file_status_updated' 消息，由 handleWebSocketMessage 处理
      
      debug(`[状态更新 ${debugInfo.processId}] 状态更新请求已发送到服务器，等待服务器响应`, {
        filename: request.filename,
        newStatus: request.status
      });
      
      sendResponse({ 
        success: true, 
        message: `状态更新请求已发送到服务器: ${request.filename} -> ${request.status}`,
        processId: debugInfo.processId
      });
      
    } catch (error) {
      debug('处理文件状态更新时出错', error, 'error');
      sendResponse({ 
        success: false, 
        error: `状态更新失败: ${error.message}` 
      });
    }
    
    return true; // 异步响应
            } else {
       debug(`未处理的内部消息类型: ${request.type}`);
       // Handle other types or ignore
       // return false; // Indicate no response sent
  }
  // return true; // Only return true if intending to send response async
};

// 确保只添加一次监听器
if (!chrome.runtime.onMessage.hasListener(messageHandler)) {
    chrome.runtime.onMessage.addListener(messageHandler);
    debug('已添加新的内部消息监听器。');
} else {
     debug('新的内部消息监听器已存在。');
}

// 获取文件数据 - HTTP版本，与handleGetFileData保持一致
async function getFileData(filename) {
    debug(`开始获取文件数据: ${filename}`);
    
    try {
        // 构建可能的文件路径
        const pathsToTry = [];
        
        // 检查是否为临时文件（从文件名可能看不出来，但优先尝试临时路径）
        const isTemporaryFile = filename.includes('临时文件上传') || filename.includes('temp') || 
                               /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/.test(filename);
        
        if (isTemporaryFile) {
            debug('文件名显示为临时文件，优先尝试临时路径');
            pathsToTry.push(`/temp/${filename}`);
            pathsToTry.push(`/tmp/${filename}`);
            pathsToTry.push(`/uploads/temp/${filename}`);
        }
        
        // 1. 最常见的uploads路径（基于日志分析的最高成功率）
        pathsToTry.push(`/uploads/${filename}`);
    
        // 2. 编码后的文件名（处理中文等特殊字符）
        const encodedFileName = encodeURIComponent(filename);
        if (encodedFileName !== filename) {
            pathsToTry.push(`/uploads/${encodedFileName}`);
            if (isTemporaryFile) {
                pathsToTry.push(`/temp/${encodedFileName}`);
                pathsToTry.push(`/tmp/${encodedFileName}`);
            }
        }
        
        // 3. 完整的uploads路径变体
        pathsToTry.push(`/nextapp/public/uploads/${filename}`);
        pathsToTry.push(`/public/uploads/${filename}`);
        
        // 4. 直接路径（无uploads前缀）
        pathsToTry.push(`/${filename}`);
        pathsToTry.push(filename);
        
        // 去重路径
        const uniquePaths = [...new Set(pathsToTry)];
        debug('HTTP文件获取将尝试以下路径:', uniquePaths);
        
        let lastError = null;
        
        // 尝试不同的路径
        for (const path of uniquePaths) {
            try {
                debug(`尝试HTTP获取文件数据，路径: ${path}`);
                
                const fileUrl = `${SERVER_URL}${path.startsWith('/') ? path : '/' + path}`;
                debug(`请求URL: ${fileUrl}`);
                
                const response = await fetch(fileUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': '*/*'
                    }
                });
                
                if (response.ok) {
                    // 获取文件数据
                    const arrayBuffer = await response.arrayBuffer();
                    const mimeType = response.headers.get('content-type') || getMimeTypeFromFilename(filename);
                    
                    // 转换为base64字符串
                    const bytes = new Uint8Array(arrayBuffer);
                    let binary = '';
                    for (let i = 0; i < bytes.byteLength; i++) {
                        binary += String.fromCharCode(bytes[i]);
                    }
                    const base64Content = btoa(binary);
                    const dataUrl = `data:${mimeType};base64,${base64Content}`;
                    
                    debug(`HTTP文件数据获取成功，路径: ${path}`, {
                        contentLength: arrayBuffer.byteLength,
                        mimeType: mimeType,
                        statusCode: response.status
                    });
                    
                    return dataUrl; // 返回完整的data URL
                }
                
                debug(`HTTP请求失败，状态码: ${response.status}, 路径: ${path}`);
                
            } catch (e) {
                lastError = e;
                debug(`HTTP请求异常，路径 ${path}: ${e.message}`);
            }
        }
        
        // 如果所有HTTP请求都失败
        debug(`所有HTTP请求失败，最后错误: ${lastError?.message}`);
        throw lastError || new Error(`无法获取文件数据: ${filename}`);
        
    } catch (error) {
        debug(`获取文件数据时出错: ${error.message}`);
        throw new Error(`获取文件数据失败: ${error.message}`);
    }
}

// 获取WebSocket连接
function getWebSocket() {
    return new Promise((resolve, reject) => {
        if (ws && ws.readyState === WebSocket.OPEN) {
            resolve(ws);
            return;
        }
        
        // 如果WebSocket未连接，尝试重新连接
        debug('WebSocket未连接，尝试重新连接');
        initWebSocket();
        
        // 等待连接建立
        const maxWaitTime = 5000; // 最多等待5秒
        const startTime = Date.now();
        
        const checkInterval = setInterval(() => {
            if (ws && ws.readyState === WebSocket.OPEN) {
                clearInterval(checkInterval);
                resolve(ws);
                return;
            }
            
            // 检查是否超时
            if (Date.now() - startTime > maxWaitTime) {
                clearInterval(checkInterval);
                reject(new Error('WebSocket连接超时'));
            }
        }, 100);
    });
}

// 在Chrome存储的树结构中查找文件
async function findFileInChromeStorage(filename) {
    debug(`在Chrome存储中查找文件: ${filename}`);
    
    try {
        // 从Chrome存储中获取树结构
        const result = await new Promise((resolve) => {
            chrome.storage.local.get(['adminTree'], resolve);
        });
        
        const adminTree = result.adminTree;
        if (!adminTree) {
            debug('未找到存储的文件树');
            return null;
        }
        
        debug('开始在树结构中查找文件');
        // 尝试通过文件名查找
        const fileByName = findFileInTreeByNameOrPath(adminTree, filename, 'name');
        if (fileByName) {
            debug(`按名称在树中找到文件: ${filename}`);
            return fileByName;
        }
        
        // 尝试通过文件路径查找
        const fileByPath = findFileInTreeByNameOrPath(adminTree, filename, 'path');
        if (fileByPath) {
            debug(`按路径在树中找到文件: ${filename}`);
            return fileByPath;
        }
        
        debug(`在树结构中未找到文件: ${filename}`);
        return null;
    } catch (error) {
        debug(`在Chrome存储中查找文件时出错: ${error.message}`);
        return null;
    }
}

// 在树结构中按名称或路径查找文件
function findFileInTreeByNameOrPath(tree, targetValue, property = 'name') {
    if (!tree || typeof tree !== 'object') {
        return null;
    }

    const allMatches = [];

    function searchRecursively(currentTree, depth = 0) {
        // 检查当前层级的文件
        if (currentTree.files && Array.isArray(currentTree.files)) {
            currentTree.files.forEach(file => {
                if (file && file[property] === targetValue) {
                    allMatches.push({
                        file: file,
                        depth: depth,
                        pathLength: (file.path || '').split('/').length
                    });
                }
            });
        }

        // 递归检查子文件夹
        if (currentTree.folders && Array.isArray(currentTree.folders)) {
            currentTree.folders.forEach(folder => {
                if (folder && folder.children) {
                    searchRecursively(folder.children, depth + 1);
                }
            });
        }
    }

    searchRecursively(tree);

    if (allMatches.length === 0) {
        return null;
    }
    
    // 如果只有一个匹配，直接返回
    if (allMatches.length === 1) {
        return allMatches[0].file;
    }

    // 多个匹配时，应用优先级规则
    allMatches.sort((a, b) => {
        // 1. 优先级：根目录文件（depth=0）
        if (a.depth !== b.depth) {
            return a.depth - b.depth;
        }
        
        // 2. 优先级：路径更短的文件
        if (a.pathLength !== b.pathLength) {
            return a.pathLength - b.pathLength;
        }
        
        // 3. 优先级：非 portal 文件
        const aIsPortal = a.file.isPortalFile || false;
        const bIsPortal = b.file.isPortalFile || false;
        if (aIsPortal !== bIsPortal) {
            return aIsPortal ? 1 : -1;
        }
        
        return 0;
    });

    const selectedFile = allMatches[0].file;
    
    // 调试日志
    if (allMatches.length > 1) {
        debug(`找到${allMatches.length}个匹配的文件，选择了: ${selectedFile.path}`, {
            target: targetValue,
            property: property,
            allPaths: allMatches.map(m => m.file.path),
            selected: selectedFile.path
        });
    }
    
    return selectedFile;
}

// 通过WebSocket获取文件的base64数据
function getFileViaWebSocket(filename) {
    debug('通过WebSocket请求文件数据', filename);
    
    return new Promise((resolve, reject) => {
        if (!ws || ws.readyState !== WebSocket.OPEN) {
            reject(new Error('WebSocket未连接'));
            return;
        }
        
        // 生成唯一请求ID，用于跟踪响应
        const requestId = Date.now().toString(36) + Math.random().toString(36).substr(2);
        
        // 设置超时处理
        const timeoutId = setTimeout(() => {
            // 移除监听器
            delete window[`_wsFileRequest_${requestId}`];
            reject(new Error('获取文件数据超时'));
        }, 15000); // 15秒超时
        
        // 设置响应处理器
        window[`_wsFileRequest_${requestId}`] = (data) => {
            clearTimeout(timeoutId);
            delete window[`_wsFileRequest_${requestId}`];
            
            if (data.error) {
                reject(new Error(data.error));
            } else if (data.base64) {
                resolve(data.base64);
            } else {
                reject(new Error('未收到有效的文件数据'));
            }
        };
        
        // 发送获取文件请求
        ws.send(JSON.stringify({
            type: 'get_file_data',
            filename: filename,
            requestId: requestId
        }));
        
        // 添加消息监听器
        const messageHandler = (event) => {
            try {
                const message = JSON.parse(event.data);
                
                if (message.type === 'file_data_response' && message.requestId === requestId) {
                    // 找到我们的响应，调用处理器
                    const handler = window[`_wsFileRequest_${requestId}`];
                    if (handler) {
                        handler(message);
                    }
                    
                    // 移除监听器
                    ws.removeEventListener('message', messageHandler);
                }
            } catch (error) {
                debug('解析WebSocket消息出错', error);
            }
        };
        
        // 添加消息监听
        ws.addEventListener('message', messageHandler);
    });
}

// ArrayBuffer转Base64
function arrayBufferToBase64(buffer, mimeType) {
    // 转换ArrayBuffer为Uint8Array
    const bytes = new Uint8Array(buffer);
    
    // 将Uint8Array转换为字符串
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    
    // 将二进制字符串转换为Base64
    const base64 = btoa(binary);
    
    // 添加MIME类型前缀
    return `data:${mimeType};base64,${base64}`;
}

// 添加获取图像缩略图的函数
async function getImageThumbnail(filePath) {
    debug('获取图像缩略图', filePath);
    
    try {
        // 构建完整URL
        const SERVER_URL = 'http://***************:6655';
        
        // === 第一步：使用新的 file-info API 精确查找文件 ===
        const fileName = filePath.split('/').pop();
        debug(`步骤1: 使用file-info API精确查找文件: ${fileName}`);
        
        try {
            const fileInfoResponse = await fetch(`${SERVER_URL}/api/file-info`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    fileName: fileName,
                    searchType: 'exact',
                    maxResults: 5
                })
            });
            
            if (fileInfoResponse.ok) {
                const fileInfoData = await fileInfoResponse.json();
                debug('file-info API缩略图响应:', fileInfoData);
                
                if (fileInfoData.success && fileInfoData.results && fileInfoData.results.length > 0) {
                    // 找到了文件，尝试使用提供的访问URL
                    for (const result of fileInfoData.results) {
                        if (result.accessUrl) {
                            debug(`尝试使用file-info提供的缩略图访问URL: ${result.accessUrl}`);
                            
                            try {
                                const response = await fetch(result.accessUrl, {
                                    method: 'GET',
                                    headers: {
                                        'Accept': 'image/*'
                                    }
                                });
                                
                                if (response.ok) {
                                    const blob = await response.blob();
                                    const base64 = await new Promise((resolve) => {
                                        const reader = new FileReader();
                                        reader.onload = () => resolve(reader.result);
                                        reader.readAsDataURL(blob);
                                    });
                                    
                                    debug(`✓ 通过file-info API成功获取缩略图`, {
                                        fileSize: blob.size,
                                        mimeType: blob.type,
                                        accessUrl: result.accessUrl
                                    });
                                    
                                    return {
                                        success: true,
                                        data: {
                                            base64: base64,
                                            size: blob.size,
                                            type: blob.type,
                                            path: result.accessUrl,
                                            context: result.context
                                        }
                                    };
                                } else {
                                    debug(`file-info访问URL失败: ${response.status} - ${response.statusText}`);
                                }
                            } catch (urlError) {
                                debug(`file-info访问URL异常: ${urlError.message}`);
                            }
                        }
                    }
                    
                    debug('file-info找到文件但所有访问URL都失败，使用备用路径策略');
                } else {
                    debug('file-info API未找到文件，使用备用路径策略');
                }
            } else {
                debug(`file-info API请求失败: ${fileInfoResponse.status}`);
            }
        } catch (apiError) {
            debug(`file-info API调用异常: ${apiError.message}`);
        }
        
        // === 第二步：备用的路径策略（保持原有逻辑作为备用） ===
        debug(`步骤2: 使用优化的路径策略作为备用方案`);
        
        // 尝试获取图片的真实路径
        let realFilePath = filePath;
        let fileInfo = null;
        
        // 优先尝试获取文件信息，查询文件的真实路径
        try {
            fileInfo = await getFileInfoByPath(filePath);
            if (fileInfo) {
                // 使用文件的实际路径
                realFilePath = fileInfo.path || fileInfo.fullPath || fileInfo.name;
                debug(`找到文件信息，使用路径: ${realFilePath}`, fileInfo);
            }
        } catch (infoError) {
            debug(`获取文件信息失败，将使用原始路径: ${infoError.message}`);
        }
        
        // 提取文件名（支持中文）
        const encodedFileName = encodeURIComponent(fileName);
        
        // 基于日志分析的成功模式，构建优化的路径尝试列表
        const pathsToTry = [];
        
        // === 使用与handleGetFileData相同的智能路径策略 ===
        
        // 1. 最高优先级：智能路径简化（基于实际文件系统观察）
        // 首先尝试获取文件信息进行智能路径简化
        try {
            const smartFileInfo = fileInfo || await getFileInfoByPath(realFilePath || fileName);
            if (smartFileInfo && smartFileInfo.path) {
                const pathParts = smartFileInfo.path.split('/').filter(Boolean);
        
                // 如果路径包含多个部分，尝试不同的简化组合
                if (pathParts.length > 1) {
                    // 去掉第一级目录（通常是临时文件上传）
                    if (pathParts.length > 2) {
                        const skipFirstLevel = pathParts.slice(1).join('/');
                        pathsToTry.push(`/uploads/${skipFirstLevel}`);
                        pathsToTry.push(`/uploads/${encodeURIComponent(skipFirstLevel)}`);
                    }
                    
                    // 去掉前两级目录（去掉临时文件上传和device ID）
                    if (pathParts.length > 3) {
                        const skipTwoLevels = pathParts.slice(2).join('/');
                        pathsToTry.push(`/uploads/${skipTwoLevels}`);
                        pathsToTry.push(`/uploads/${encodeURIComponent(skipTwoLevels)}`);
                    }
                    
                    // 只保留最后两级目录
                    if (pathParts.length > 2) {
                        const lastTwoLevels = pathParts.slice(-2).join('/');
                        pathsToTry.push(`/uploads/${lastTwoLevels}`);
                        pathsToTry.push(`/uploads/${encodeURIComponent(lastTwoLevels)}`);
                    }
                }
                
                // 添加完整路径（去掉临时前缀）
                const fullPathWithoutTemp = smartFileInfo.path.replace(/^临时文件上传\/[^\/]+\//, '');
                if (fullPathWithoutTemp !== smartFileInfo.path) {
                    pathsToTry.push(`/uploads/${fullPathWithoutTemp}`);
                    pathsToTry.push(`/uploads/${encodeURIComponent(fullPathWithoutTemp)}`);
                }
            }
        } catch (smartInfoError) {
            debug(`智能路径分析失败: ${smartInfoError.message}`);
        }
        
        // 2. 次高优先级：直接文件名
        pathsToTry.push(`/uploads/${fileName}`);
        pathsToTry.push(`/uploads/${encodedFileName}`);
        
        // 3. 传统的备选路径（降低优先级）
        pathsToTry.push(`/nextapp/public/uploads/${fileName}`);
        pathsToTry.push(`/public/uploads/${fileName}`);
        pathsToTry.push(`/${fileName}`);
        pathsToTry.push(fileName);
        
        // 4. 仅在必要时才尝试复杂的临时文件路径（最低优先级）
        if (realFilePath && realFilePath !== fileName) {
            const isTemporaryFile = realFilePath.includes('临时文件上传') || 
                                   realFilePath.includes('temp') || 
                                   /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/.test(realFilePath);
                
            if (isTemporaryFile) {
                debug('作为最后尝试添加复杂临时文件路径');
                // 临时文件路径作为最后的尝试
                pathsToTry.push(`/uploads/${realFilePath}`);
                pathsToTry.push(`/temp/${realFilePath}`);
                pathsToTry.push(`/tmp/${realFilePath}`);
            } else {
                pathsToTry.push(`/uploads/${realFilePath}`);
            }
                }
                
        // 去重
        const uniquePaths = [...new Set(pathsToTry)];
        
        debug('备用缩略图路径策略将尝试以下路径:', uniquePaths);
        
        // 按优先级尝试每个路径
        for (const path of uniquePaths) {
            try {
                const url = `${SERVER_URL}${path}`;
                debug(`尝试缩略图URL: ${url}`);
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'image/*'
                    }
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const base64 = await new Promise((resolve) => {
                        const reader = new FileReader();
                        reader.onload = () => resolve(reader.result);
                        reader.readAsDataURL(blob);
                    });
                
                    debug(`成功获取缩略图，使用路径: ${path}`);
                    return {
                        success: true,
                        data: {
                            base64: base64,
                            size: blob.size,
                            type: blob.type,
                            path: path
                        }
                    };
                } else {
                    debug(`路径 ${path} 失败: ${response.status} ${response.statusText}`);
                    continue;
                }
            } catch (fetchError) {
                debug(`请求路径 ${path} 时出错: ${fetchError.message}`);
                continue;
            }
        }
        
        debug('所有缩略图路径都失败:', uniquePaths.join(', '));
        return {
            success: false,
            error: `无法获取缩略图: 尝试了 ${uniquePaths.length} 个路径都失败`
        };
        
    } catch (error) {
        console.error('获取图像缩略图时出错:', error);
        return {
            success: false,
            error: `获取缩略图失败: ${error.message}`
        };
    }
}

// 添加获取文件信息的辅助函数
async function getFileInfoByPath(filePath) {
    debug(`尝试根据路径获取文件信息: ${filePath}`);
    
    // 优先从存储的文件树中查找
    try {
        const result = await chrome.storage.local.get(['admin_file_tree']);
        const storedTree = result.admin_file_tree;
        
        if (storedTree) {
            // 数据直接就是文件树结构，不需要 .payload
            const fileInfo = findFileInTreeByNameOrPath(storedTree, filePath, 'path');
            if (fileInfo) {
                debug('在存储的文件树中找到文件:', fileInfo);
                return fileInfo;
            }
            
            // 如果直接路径匹配失败，尝试文件名匹配
            const fileName = filePath.split('/').pop();
            const fileByName = findFileInTreeByNameOrPath(storedTree, fileName, 'name');
            if (fileByName) {
                debug('通过文件名在文件树中找到:', fileByName);
                return fileByName;
            }
            
            // 尝试去掉前导斜杠的路径匹配
            const pathWithoutSlash = filePath.startsWith('/') ? filePath.substring(1) : filePath;
            const fileByPath = findFileInTreeByNameOrPath(storedTree, pathWithoutSlash, 'path');
            if (fileByPath) {
                debug('通过去除前导斜杠的路径找到:', fileByPath);
                return fileByPath;
            }
        } else {
            debug('存储中未找到 admin_file_tree 数据');
        }
    } catch (error) {
        debug('从存储中获取文件树失败:', error);
    }
    
    // 从缓存的文件列表中查找匹配的文件（保留原有逻辑作为备选）
    if (cachedFiles && cachedFiles.length > 0) {
        // 尝试多种匹配方式
        const normalizedPath = filePath.startsWith('/') ? filePath : '/' + filePath;
        
        // 1. 直接路径匹配
        let fileInfo = cachedFiles.find(f => f.path === filePath || f.path === normalizedPath);
        
        // 2. 如果没找到，尝试匹配文件名
        if (!fileInfo) {
            const fileName = filePath.split('/').pop();
            fileInfo = cachedFiles.find(f => f.filename === fileName || f.name === fileName);
            debug(`通过文件名查找: ${fileName}, 结果: ${fileInfo ? '找到' : '未找到'}`);
        }
        
        // 3. 如果还是没找到，尝试部分路径匹配
        if (!fileInfo) {
            // 获取filePath的最后两部分作为匹配项
            const pathParts = filePath.split('/').filter(Boolean);
            if (pathParts.length >= 2) {
                const lastTwoParts = pathParts.slice(-2).join('/');
                fileInfo = cachedFiles.find(f => f.path && f.path.includes(lastTwoParts));
                debug(`通过部分路径查找: ${lastTwoParts}, 结果: ${fileInfo ? '找到' : '未找到'}`);
            }
        }
        
        if (fileInfo) {
            debug('在缓存文件列表中找到文件信息', fileInfo);
            return fileInfo;
        }
    }
    
    debug('未能找到文件信息:', filePath);
    return null;
}

// 启动WebSocket连接
initWebSocket();

// 设置一个周期性的连接状态检查，确保长时间运行时连接的稳定性
setInterval(checkWebSocketConnection, 60000); // 每分钟检查一次连接状态

/**
 * 通知popup页面状态变化
 * @param {Object} data 要发送的数据
 */
function notifyPopup(data) {
    try {
        chrome.runtime.sendMessage(data).catch(() => {
            // 忽略发送失败的错误（popup可能未打开）
        });
    } catch (error) {
        // 忽略发送错误
    }
}

// 处理获取文件数据请求 - 使用新的file-info API
async function handleGetFileData(request, sendResponse) {
    try {
        debug(`获取文件数据请求: ${request.filename}`, { path: request.path });
        
        // 第一步：使用新的file-info API精确查找文件
        debug(`步骤1: 使用file-info API精确查找文件: ${request.filename}`);
        
        try {
            const fileInfoResponse = await fetch(`${SERVER_URL}/api/file-info`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    fileName: request.filename,
                    searchType: 'exact',
                    maxResults: 5
                })
            });
            
            if (fileInfoResponse.ok) {
                const fileInfoData = await fileInfoResponse.json();
                debug('file-info API响应:', fileInfoData);
                
                if (fileInfoData.success && fileInfoData.results && fileInfoData.results.length > 0) {
                    // 找到了文件，尝试使用提供的访问URL
                    for (const result of fileInfoData.results) {
                        if (result.accessUrl) {
                            debug(`尝试使用file-info提供的访问URL: ${result.accessUrl}`);
                            
                    try {
                                const fileResponse = await fetch(result.accessUrl);
                                
                                if (fileResponse.ok) {
                                    const arrayBuffer = await fileResponse.arrayBuffer();
                                    const mimeType = fileResponse.headers.get('content-type') || getMimeTypeFromFilename(request.filename);
                                    
                                    const bytes = new Uint8Array(arrayBuffer);
                                    let binary = '';
                                    for (let i = 0; i < bytes.byteLength; i++) {
                                        binary += String.fromCharCode(bytes[i]);
                                    }
                                    const base64Content = btoa(binary);
                                    const dataUrl = `data:${mimeType};base64,${base64Content}`;
                                    
                                    debug(`✓ 通过file-info API成功获取文件`, {
                                        fileSize: arrayBuffer.byteLength,
                                        mimeType: mimeType,
                                        accessUrl: result.accessUrl
                                    });
                                    
                                    sendResponse({
                                        success: true,
                                        data: {
                                            name: request.filename,
                                            content: dataUrl,
                                            mimeType: mimeType,
                                            accessUrl: result.accessUrl,
                                            context: result.context
                                        }
                                    });
                                    return;
                            } else {
                                    debug(`访问URL失败: ${fileResponse.status} - ${fileResponse.statusText}`);
                                }
                            } catch (urlError) {
                                debug(`访问URL异常: ${urlError.message}`);
                            }
                        }
                    }
                    
                    debug('file-info找到文件但所有访问URL都失败，尝试备用策略');
                } else {
                    debug('file-info API未找到文件，使用备用策略');
                }
            } else {
                debug(`file-info API请求失败: ${fileInfoResponse.status}`);
            }
        } catch (apiError) {
            debug(`file-info API调用异常: ${apiError.message}`);
        }
        
        // 第二步：使用和缩略图相同的成功路径策略作为备用
        debug(`步骤2: 使用优化的路径策略作为备用方案`);
        
        // 提取文件名
        const fileName = request.filename;
        const encodedFileName = encodeURIComponent(fileName);
        
        // 使用和 getImageThumbnail 相同的路径优先级策略
        const pathsToTry = [];
        
        // 1. 最高优先级：智能路径简化（基于实际文件系统观察）
        try {
            const fileInfo = await getFileInfoByPath(request.path || fileName);
            if (fileInfo && fileInfo.path) {
                const pathParts = fileInfo.path.split('/').filter(Boolean);
                
                // 如果路径包含多个部分，尝试不同的简化组合
                if (pathParts.length > 1) {
                    // 去掉第一级目录（通常是临时文件上传）
                    if (pathParts.length > 2) {
                        const skipFirstLevel = pathParts.slice(1).join('/');
                        pathsToTry.push(`/uploads/${skipFirstLevel}`);
                        pathsToTry.push(`/uploads/${encodeURIComponent(skipFirstLevel)}`);
                    }
                    
                    // 去掉前两级目录（去掉临时文件上传和device ID）
                    if (pathParts.length > 3) {
                        const skipTwoLevels = pathParts.slice(2).join('/');
                        pathsToTry.push(`/uploads/${skipTwoLevels}`);
                        pathsToTry.push(`/uploads/${encodeURIComponent(skipTwoLevels)}`);
                    }
                    
                    // 只保留最后两级目录
                    if (pathParts.length > 2) {
                        const lastTwoLevels = pathParts.slice(-2).join('/');
                        pathsToTry.push(`/uploads/${lastTwoLevels}`);
                        pathsToTry.push(`/uploads/${encodeURIComponent(lastTwoLevels)}`);
                    }
                }
                
                // 添加完整路径（去掉临时前缀）
                const fullPathWithoutTemp = fileInfo.path.replace(/^临时文件上传\/[^\/]+\//, '');
                if (fullPathWithoutTemp !== fileInfo.path) {
                    pathsToTry.push(`/uploads/${fullPathWithoutTemp}`);
                    pathsToTry.push(`/uploads/${encodeURIComponent(fullPathWithoutTemp)}`);
                }
            }
        } catch (infoError) {
            debug(`获取文件信息失败: ${infoError.message}`);
        }
        
        // 2. 次高优先级：直接文件名
        pathsToTry.push(`/uploads/${fileName}`);
        pathsToTry.push(`/uploads/${encodedFileName}`);
        
        // 3. 传统的备选路径（降低优先级）
        pathsToTry.push(`/nextapp/public/uploads/${fileName}`);
        pathsToTry.push(`/public/uploads/${fileName}`);
        pathsToTry.push(`/${fileName}`);
        pathsToTry.push(fileName);
        
        // 去重路径
        const uniquePaths = [...new Set(pathsToTry)];
        debug('备用HTTP文件获取将尝试以下路径:', uniquePaths);
        
        let lastError = null;
        
        // 尝试不同的路径
        for (const path of uniquePaths) {
            try {
                debug(`尝试HTTP获取文件数据，路径: ${path}`);
                
                const fileUrl = `${SERVER_URL}${path.startsWith('/') ? path : '/' + path}`;
                debug(`请求URL: ${fileUrl}`);
                
                const response = await fetch(fileUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': '*/*'
                    }
                });
                
                debug(`HTTP响应状态: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    // 获取文件数据
                    const arrayBuffer = await response.arrayBuffer();
                    const mimeType = response.headers.get('content-type') || getMimeTypeFromFilename(request.filename);
                    
                    // 转换为base64字符串
                    const bytes = new Uint8Array(arrayBuffer);
                    let binary = '';
                    for (let i = 0; i < bytes.byteLength; i++) {
                        binary += String.fromCharCode(bytes[i]);
                            }
                    const base64Content = btoa(binary);
                    const dataUrl = `data:${mimeType};base64,${base64Content}`;
                    
                    debug(`✓ 通过备用路径成功获取文件数据`, {
                        path: path,
                        fileSize: arrayBuffer.byteLength,
                        mimeType: mimeType
                    });
                    
                    sendResponse({
                        success: true,
                        data: {
                            name: request.filename,
                            content: dataUrl,
                            mimeType: mimeType,
                            accessPath: path
                        }
                    });
                    return;
                } else {
                    lastError = `HTTP ${response.status}: ${response.statusText}`;
                    debug(`路径 ${path} 失败: ${lastError}`);
                }
            } catch (error) {
                lastError = error.message;
                debug(`路径 ${path} 异常: ${lastError}`);
            }
        }
        
        // 所有路径都失败，返回详细错误信息
        debug(`所有路径都失败，最后错误: ${lastError}`);
        
        sendResponse({
            success: false,
            error: `无法找到文件 "${request.filename}"`,
            details: {
                lastError: lastError,
                triedPaths: uniquePaths,
                suggestion: '文件可能已被移动或删除。请刷新文件列表或重新选择文件。',
                troubleshooting: {
                    step1: '检查文件是否仍在服务器上存在',
                    step2: '刷新文件列表',
                    step3: '检查服务器API /api/file-info 是否正常工作',
                    step4: '检查文件访问权限'
                }
            }
        });
        
    } catch (error) {
        debug(`处理文件数据获取时发生严重错误: ${error.message}`, { error: error.stack });
        
        sendResponse({
            success: false,
            error: `处理文件数据时发生错误: ${error.message}`,
            details: {
                suggestion: '这可能是服务器连接问题，请稍后重试',
                errorType: 'internal_error'
            }
        });
    }
}

// 根据文件名获取MIME类型
function getMimeTypeFromFilename(filename) {
    const extension = filename.split('.').pop()?.toLowerCase();
    const mimeTypes = {
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'gif': 'image/gif',
        'webp': 'image/webp',
        'svg': 'image/svg+xml',
        'pdf': 'application/pdf',
        'txt': 'text/plain',
        'html': 'text/html',
        'css': 'text/css',
        'js': 'application/javascript',
        'json': 'application/json',
        'xml': 'application/xml',
        'zip': 'application/zip',
        'rar': 'application/x-rar-compressed',
        '7z': 'application/x-7z-compressed',
        'mp4': 'video/mp4',
        'mp3': 'audio/mpeg',
        'wav': 'audio/wav',
        'doc': 'application/msword',
        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xls': 'application/vnd.ms-excel',
        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    };
    
    return mimeTypes[extension] || 'application/octet-stream';
}

// 处理启动云盘上传请求 - 修复版
async function handleStartYunpanUpload(request, sendResponse) {
    try {
        debug('收到启动云盘上传请求');
        debug('request对象:', request);
        debug('request类型:', typeof request);
        debug('request.type:', request?.type);
        debug('request.data:', request?.data);
        debug('request.data类型:', typeof request?.data);

        // 尝试多种方式获取数据
        let requestData = request?.data;

        // 如果data不存在，尝试其他可能的属性
        if (!requestData) {
            debug('request.data为空，尝试其他属性...');
            debug('request.files:', request?.files);
            debug('request.requestData:', request?.requestData);

            // 尝试直接从request获取files（简化格式支持）
            if (request?.files) {
                requestData = {
                    files: request.files,
                    source: request.source || 'simplified_format'
                };
                debug('使用简化格式数据:', requestData);
            }
        }

        // 验证请求数据
        if (!requestData) {
            const error = '上传请求数据为空';
            debug(error);
            debug('完整request对象:', JSON.stringify(request, null, 2));
            sendResponse({
                success: false,
                error: error,
                details: {
                    suggestion: '请检查上传请求的数据格式',
                    receivedRequest: request,
                    requestType: typeof request,
                    requestKeys: request ? Object.keys(request) : []
                }
            });
            return;
        }

        if (!requestData.files || !Array.isArray(requestData.files)) {
            const error = '上传请求中缺少文件列表';
            debug(error);
            sendResponse({
                success: false,
                error: error,
                details: {
                    suggestion: '请确保上传请求包含有效的文件列表'
                }
            });
            return;
        }

        debug('开始处理云盘上传请求', {
            filesCount: requestData.files.length,
            requestData: requestData
        });
        
        // 查找云盘页面标签
        debug('开始查找云盘页面标签');
        const tabs = await chrome.tabs.query({});
        let yunpanTab = null;
        
        for (const tab of tabs) {
            debug(`检查标签页: ${tab.url}`);
            if (tab.url && tab.url.includes('yunpan.gdcourts.gov.cn')) {
                debug(`找到云盘页面: ${tab.url}`, { tabId: tab.id });
                yunpanTab = tab;
                break;
            }
        }
        
        if (!yunpanTab) {
            const error = '未找到云盘页面，请先打开 https://yunpan.gdcourts.gov.cn/';
            debug(error);
            sendResponse({
                success: false,
                error: error,
                details: {
                    suggestion: '请在新标签页中打开云盘页面后重试'
                }
            });
            return;
        }
        
        debug('准备向云盘页面发送上传请求', {
            tabId: yunpanTab.id,
            tabUrl: yunpanTab.url,
            filesCount: requestData.files.length
        });
        
        // 发送上传请求到云盘页面的content script
        try {
            const response = await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('云盘页面响应超时（60秒）'));
                }, 60000);
                
                chrome.tabs.sendMessage(yunpanTab.id, {
                    type: 'START_UPLOAD',
                    data: requestData
                }, (response) => {
                    clearTimeout(timeout);
                    
                    if (chrome.runtime.lastError) {
                        reject(new Error(`云盘页面通信失败: ${chrome.runtime.lastError.message}`));
                    } else if (!response) {
                        reject(new Error('云盘页面没有响应'));
        } else {
                        resolve(response);
                    }
                });
            });
            
            debug('收到云盘页面响应', response);
            
            // 检查content script的响应格式
            if (response.success) {
                // content script返回成功
                const uploadStats = response.result || {
                    total: requestData.files.length,
                    completed: 0,
                    failed: requestData.files.length
                };
                
                debug('云盘上传成功', uploadStats);
                
                sendResponse({
                    success: true,
                    result: {
                        total: uploadStats.total || requestData.files.length,
                        completed: uploadStats.completed || 0,
                        failed: uploadStats.failed || 0,
                        uploadStats: uploadStats
                    }
                });
            } else {
                // content script返回失败
                const errorMsg = response.error || '云盘上传失败';
                debug('云盘上传失败', { error: errorMsg, response: response });
                
                sendResponse({
                    success: false,
                    error: errorMsg,
                    details: {
                        contentScriptError: true,
                        originalResponse: response,
                        suggestion: '请检查云盘页面是否正常加载，或重新加载页面后重试'
                    }
                });
            }
            
    } catch (error) {
            debug('云盘页面通信异常', error);
            
            sendResponse({
                success: false,
                error: `云盘页面通信异常: ${error.message}`,
                details: {
                    communicationError: true,
                    errorType: error.name,
                    suggestion: '请确保云盘页面已加载并处于活跃状态'
                }
            });
        }
        
    } catch (error) {
        debug('处理云盘上传请求时发生错误', error);
        
        sendResponse({
            success: false,
            error: `上传请求处理失败: ${error.message}`,
            details: {
                internalError: true,
                errorStack: error.stack,
                suggestion: '请重新加载扩展后重试'
            }
        });
    }
}

// 检查云盘页面是否可用 - 增强版
async function handleCheckYunpanPage(sendResponse) {
    try {
        debug('开始检查云盘页面可用性');
        const tabs = await chrome.tabs.query({});
        let yunpanTab = null;
        
        // 检查所有标签页
        for (const tab of tabs) {
            if (tab.url) {
                debug(`检查标签页: ${tab.url}`);
                
                // 更精确的云盘页面检测
                if (tab.url.includes('yunpan.gdcourts.gov.cn') || 
                    tab.url.includes('yunpan') || 
                    tab.url.includes('cloud') || 
                    tab.url.includes('file')) {
                    
                    yunpanTab = tab;
                    debug(`找到潜在云盘页面: ${tab.url}`, { tabId: tab.id });
                    break;
                }
            }
        }
        
        if (!yunpanTab) {
            debug('未找到云盘页面', null, 'warn');
            sendResponse({
                success: false,
                available: false,
                message: '未找到云盘页面，请先打开政府云盘网站 (https://yunpan.gdcourts.gov.cn/)',
                details: {
                    totalTabs: tabs.length,
                    suggestion: '请在新标签页中打开 https://yunpan.gdcourts.gov.cn/'
                }
            });
            return;
        }
        
        debug(`准备检查云盘页面脚本状态`, { 
            tabId: yunpanTab.id,
            tabUrl: yunpanTab.url
        });

        // Ping云盘页面检查脚本是否就绪 - 增强版
        chrome.tabs.sendMessage(yunpanTab.id, {
            type: 'PING'
        }, (response) => {
            if (chrome.runtime.lastError) {
                const errorMsg = `云盘页面脚本未就绪: ${chrome.runtime.lastError.message}`;
                debug(errorMsg, { 
                    lastError: chrome.runtime.lastError,
                    tabId: yunpanTab.id,
                    tabUrl: yunpanTab.url
                }, 'warn');
                
                sendResponse({
                    success: false,
                    available: false,
                    message: errorMsg,
                    details: {
                        chromeError: chrome.runtime.lastError.message,
                        tabId: yunpanTab.id,
                        tabUrl: yunpanTab.url,
                        suggestion: '请刷新云盘页面，确保页面完全加载后重试'
                    }
                });
            } else if (!response) {
                const errorMsg = '云盘页面脚本没有响应';
                debug(errorMsg, { tabId: yunpanTab.id, tabUrl: yunpanTab.url }, 'warn');
                
                sendResponse({
                    success: false,
                    available: false,
                    message: errorMsg,
                    details: {
                        tabId: yunpanTab.id,
                        tabUrl: yunpanTab.url,
                        suggestion: '页面可能正在加载中，请稍等片刻后重试'
                    }
                });
            } else {
                debug('云盘页面脚本运行正常', response, 'success');
                sendResponse({
                    success: true,
                    available: true,
                    message: '云盘页面已就绪',
                    tabId: yunpanTab.id,
                    tabUrl: yunpanTab.url,
                    scriptResponse: response
                });
            }
        });
        
    } catch (error) {
        const errorMsg = `检查云盘页面时发生错误: ${error.message}`;
        debug(errorMsg, { 
            error: error.message,
            stack: error.stack 
        }, 'error');
        
        sendResponse({
            success: false,
            available: false,
            message: errorMsg,
            details: {
                originalError: error.message,
                suggestion: '请检查浏览器设置和扩展权限'
            }
        });
    }
}

// =================================================================

/**
 * 启动后台自动检测
 */
function startBackgroundDetection() {
    if (backgroundAutoUpload.detectionInterval) {
        debug('后台检测已在运行，跳过启动');
        return;
    }
    
    debug(`启动后台自动检测，检测间隔: ${backgroundAutoUpload.intervalDuration}ms`);
    
    backgroundAutoUpload.detectionInterval = setInterval(async () => {
        if (backgroundAutoUpload.enabled && !backgroundAutoUpload.isRunning) {
            await coordinatedAutoUploadCheck();
        }
    }, backgroundAutoUpload.intervalDuration);
    
    // 立即执行一次检测
    setTimeout(async () => {
        if (backgroundAutoUpload.enabled) {
            await coordinatedAutoUploadCheck();
        }
    }, 5000); // 延迟5秒启动，确保其他组件初始化完成
}

/**
 * 停止后台自动检测
 */
function stopBackgroundDetection() {
    if (backgroundAutoUpload.detectionInterval) {
        clearInterval(backgroundAutoUpload.detectionInterval);
        backgroundAutoUpload.detectionInterval = null;
        debug('后台自动检测已停止');
    }
}

/**
 * 重启后台检测（应用新的检测频率）
 */
function restartBackgroundDetection() {
    stopBackgroundDetection();
    if (backgroundAutoUpload.enabled) {
        startBackgroundDetection();
    }
}

/**
 * 协调式自动上传检查 - 核心逻辑
 * 这是主从协调架构的核心，确保与popup检测不冲突
 */
async function coordinatedAutoUploadCheck() {
    if (backgroundAutoUpload.isRunning) {
        debug('后台检测已在运行中，跳过本次检测');
        return;
    }
    
    backgroundAutoUpload.isRunning = true;
    
    try {
        // 1. 尝试获取上传锁
        const lockAcquired = await acquireUploadLock('background', 'auto_detection', 30000);
        if (!lockAcquired) {
            debug('后台检测跳过：无法获取上传锁（可能popup正在操作）');
            return;
        }
        
        debug('后台自动检测开始执行');
        
        try {
            // 2. 检查popup状态并决定检测策略
            if (popupStatus.isOpen) {
                debug('Popup已打开，后台检测进入协助模式');
                // Popup打开时只做文件变化检测，不执行上传
                await backgroundFileChangeDetection();
            } else {
                debug('Popup未打开，后台检测执行完整自动上传流程');
                // Popup关闭时执行完整的检测和上传
                await backgroundFullAutoUpload();
            }
        } finally {
            // 3. 释放锁
            releaseUploadLock('background');
        }
        
    } catch (error) {
        debug('后台自动检测执行出错:', error);
        releaseUploadLock('background'); // 确保释放锁
    } finally {
        backgroundAutoUpload.isRunning = false;
    }
}

/**
 * 后台文件变化检测（popup打开时的协助模式）
 */
async function backgroundFileChangeDetection() {
    try {
        // 获取当前文件树
        const currentTree = await getCurrentFileTree();
        if (!currentTree) {
            debug('无法获取文件树，跳过后台检测');
            return;
        }
        
        // 统计文件数量
        const currentFileCount = countFilesInTree(currentTree);
        
        if (backgroundAutoUpload.lastFileCount > 0 && currentFileCount > backgroundAutoUpload.lastFileCount) {
            debug(`检测到新文件，文件数量从 ${backgroundAutoUpload.lastFileCount} 增加到 ${currentFileCount}`);
            
            // 通知popup有新文件，但不执行上传
            notifyPopup({
                type: 'background_new_files_detected',
                previousCount: backgroundAutoUpload.lastFileCount,
                currentCount: currentFileCount,
                source: 'background_detection'
            });
        }
        
        backgroundAutoUpload.lastFileCount = currentFileCount;
        
    } catch (error) {
        debug('后台文件变化检测出错:', error);
    }
}

// =================================================================
// 自动刷新网页功能
// =================================================================

/**
 * 启动自动刷新网页
 */
function startAutoPageRefresh(interval, tabId = null) {
    console.log(`[自动刷新] 启动请求 - 间隔: ${interval}秒, 标签页ID: ${tabId}`);
    debug(`[自动刷新] 启动请求 - 间隔: ${interval}秒, 标签页ID: ${tabId}`);

    if (interval <= 0) {
        console.log('[自动刷新] 间隔无效，跳过启动');
        debug('[自动刷新] 间隔无效，跳过启动');
        return;
    }

    // 检查是否已经有相同配置的定时器在运行
    if (autoPageRefresh.enabled &&
        autoPageRefresh.interval === interval &&
        autoPageRefresh.timerId &&
        (tabId === null || autoPageRefresh.targetTabId === tabId)) {
        console.log(`[自动刷新] 相同配置已在运行，跳过重复设置`);
        debug(`[自动刷新] 相同配置已在运行，跳过重复设置`);
        return;
    }

    // 停止现有的刷新
    stopAutoPageRefresh();

    autoPageRefresh.enabled = true;
    autoPageRefresh.interval = interval;
    autoPageRefresh.targetTabId = tabId;

    console.log(`[自动刷新] 启动成功 - 间隔: ${interval}秒, 目标标签页: ${tabId || '自动检测'}`);
    debug(`[自动刷新] 启动成功 - 间隔: ${interval}秒, 目标标签页: ${tabId || '自动检测'}`);

    // 设置定时器
    autoPageRefresh.timerId = setInterval(async () => {
        console.log(`[自动刷新] 定时器触发 - ${new Date().toLocaleTimeString()}`);
        debug(`[自动刷新] 定时器触发 - ${new Date().toLocaleTimeString()}`);
        await performPageRefresh();
    }, interval * 1000);

    console.log(`[自动刷新] 定时器已设置，ID: ${autoPageRefresh.timerId}, 间隔: ${interval * 1000}ms`);
    debug(`[自动刷新] 定时器已设置，ID: ${autoPageRefresh.timerId}`);

    // 保存设置到存储
    chrome.storage.local.set({
        autoPageRefreshEnabled: true,
        autoPageRefreshInterval: interval
    });

    console.log(`[自动刷新] 配置完成，等待 ${interval} 秒后首次执行`);
}

/**
 * 停止自动刷新网页
 */
function stopAutoPageRefresh() {
    console.log('[自动刷新] 停止自动刷新请求');

    if (autoPageRefresh.timerId) {
        clearInterval(autoPageRefresh.timerId);
        autoPageRefresh.timerId = null;
        console.log('[自动刷新] 定时器已清除');
        debug('自动刷新网页已停止');
    } else {
        console.log('[自动刷新] 没有活跃的定时器需要清除');
    }

    autoPageRefresh.enabled = false;
    console.log('[自动刷新] 状态已设置为禁用');

    // 保存设置到存储
    chrome.storage.local.set({
        autoPageRefreshEnabled: false
    });
}

/**
 * 执行页面刷新
 */
async function performPageRefresh() {
    try {
        console.log('[自动刷新] 开始执行页面刷新检查');
        debug('[自动刷新] 开始执行页面刷新检查');

        // 查找云盘标签页
        const tabs = await chrome.tabs.query({
            url: ['*://yunpan.gdcourts.gov.cn:82/*']
        });

        console.log(`[自动刷新] 找到 ${tabs.length} 个云盘标签页`);
        debug(`[自动刷新] 找到 ${tabs.length} 个云盘标签页`);

        if (tabs.length === 0) {
            console.log('[自动刷新] 未找到云盘标签页，跳过刷新');
            debug('[自动刷新] 未找到云盘标签页，跳过刷新');
            return;
        }

        // 如果指定了目标标签页，优先使用
        let targetTab = null;
        if (autoPageRefresh.targetTabId) {
            targetTab = tabs.find(tab => tab.id === autoPageRefresh.targetTabId);
            debug(`[自动刷新] 查找指定标签页 ${autoPageRefresh.targetTabId}: ${targetTab ? '找到' : '未找到'}`);
        }

        // 如果没有找到指定标签页，使用第一个云盘标签页
        if (!targetTab) {
            targetTab = tabs[0];
            autoPageRefresh.targetTabId = targetTab.id;
            debug(`[自动刷新] 使用第一个标签页: ${targetTab.id}`);
        }

        console.log(`[自动刷新] 目标标签页状态 - ID: ${targetTab.id}, 活跃: ${targetTab.active}, URL: ${targetTab.url}`);
        debug(`[自动刷新] 目标标签页状态 - ID: ${targetTab.id}, 活跃: ${targetTab.active}, URL: ${targetTab.url}`);

        // 检查标签页是否活跃（可配置是否跳过活跃标签页）
        if (targetTab.active) {
            console.log('[自动刷新] 目标标签页正在使用中');

            // 获取配置：是否允许刷新活跃标签页
            const settings = await chrome.storage.local.get('allowRefreshActiveTab');
            const allowRefreshActive = settings.allowRefreshActiveTab === true;

            if (!allowRefreshActive) {
                console.log('[自动刷新] 配置为不刷新活跃标签页，跳过刷新');
                debug('[自动刷新] 目标标签页正在使用中，跳过自动刷新');

                // 通知用户跳过了刷新
                try {
                    await notifyContentScript(targetTab.id, {
                        type: 'REFRESH_SKIPPED',
                        reason: 'active_tab',
                        message: '跳过刷新：标签页正在使用中'
                    });
                } catch (error) {
                    debug('[自动刷新] 通知跳过刷新失败:', error.message);
                }

                return;
            } else {
                console.log('[自动刷新] 配置为允许刷新活跃标签页，继续执行');
            }
        }

        // 执行刷新
        debug(`[自动刷新] 开始刷新标签页 ${targetTab.id}`);
        await chrome.tabs.reload(targetTab.id);
        autoPageRefresh.lastRefreshTime = Date.now();

        debug(`[自动刷新] 刷新完成 - 标签页ID: ${targetTab.id}, 时间: ${new Date().toLocaleTimeString()}`);

        // 通知面板刷新已执行
        try {
            await notifyContentScript(targetTab.id, {
                type: 'PAGE_REFRESHED',
                timestamp: autoPageRefresh.lastRefreshTime,
                interval: autoPageRefresh.interval
            });
            debug('[自动刷新] 通知发送成功');
        } catch (notifyError) {
            debug('[自动刷新] 通知发送失败:', notifyError.message);
        }

    } catch (error) {
        debug('[自动刷新] 执行页面刷新失败:', error);
    }
}

/**
 * 通知内容脚本
 */
async function notifyContentScript(tabId, message) {
    try {
        await chrome.tabs.sendMessage(tabId, message);
    } catch (error) {
        // 忽略通知失败，不影响主要功能
        debug('通知内容脚本失败:', error.message);
    }
}

// =================================================================

/**
 * 后台完整自动上传流程（popup关闭时）
 */
async function backgroundFullAutoUpload() {
    try {
        // 检查自动上传是否启用
        const autoUploadSettings = await chrome.storage.local.get('backgroundAutoUploadEnabled');
        if (!autoUploadSettings.backgroundAutoUploadEnabled) {
            debug('后台自动上传未启用，跳过检测');
            return;
        }
        
        // 获取当前文件树和未上传文件
        const currentTree = await getCurrentFileTree();
        if (!currentTree) {
            debug('无法获取文件树，跳过后台上传');
            return;
        }
        
        const unuploadedFiles = getUnuploadedFilesFromTree(currentTree);
        const currentFileCount = countFilesInTree(currentTree);
        
        debug(`后台检测结果: 总文件 ${currentFileCount} 个，未上传 ${unuploadedFiles.length} 个`);
        
        // 检测新文件
        if (backgroundAutoUpload.lastFileCount > 0 && currentFileCount > backgroundAutoUpload.lastFileCount) {
            debug(`检测到 ${currentFileCount - backgroundAutoUpload.lastFileCount} 个新文件`);
        }
        
        backgroundAutoUpload.lastFileCount = currentFileCount;
        
        // 如果有未上传文件，执行自动上传
        if (unuploadedFiles.length > 0) {
            debug(`开始后台自动上传 ${unuploadedFiles.length} 个文件`);
            
            // 通知popup开始后台上传
            notifyPopup({
                type: 'background_upload_started',
                fileCount: unuploadedFiles.length,
                source: 'background_auto_upload'
            });
            
            // 执行上传流程
            await triggerBackgroundUpload(unuploadedFiles);
        }
        
    } catch (error) {
        debug('后台完整自动上传出错:', error);
        
        // 通知popup上传出错
        notifyPopup({
            type: 'background_upload_error',
            error: error.message,
            source: 'background_auto_upload'
        });
    }
}

/**
 * 触发后台上传流程
 * @param {Array} files - 要上传的文件列表
 */
async function triggerBackgroundUpload(files) {
    try {
        debug(`后台上传开始，文件数量: ${files.length}`);
        
        // 检查云盘页面是否可用
        const yunpanReady = await checkYunpanPageAvailability();
        if (!yunpanReady) {
            debug('云盘页面不可用，尝试打开云盘页面');
            await openFileCloudPage();
            
            // 等待页面准备
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
        
        // 开始上传流程
        const uploadResult = await handleStartYunpanUpload({
            data: { files: files }
        }, (response) => {
            debug('后台上传响应:', response);
        });
        
        if (uploadResult) {
            debug('后台上传成功完成');
            
            // 通知popup上传完成
            notifyPopup({
                type: 'background_upload_completed',
                fileCount: files.length,
                source: 'background_auto_upload'
            });
        }
        
    } catch (error) {
        debug('后台上传流程出错:', error);
        throw error;
    }
}

/**
 * 从文件树中获取未上传的文件
 * @param {Object} tree - 文件树对象
 * @returns {Array} 未上传的文件列表
 */
function getUnuploadedFilesFromTree(tree) {
    const unuploadedFiles = [];
    
    function extractFiles(node, currentPath = '') {
        if (node.type === 'file') {
            // 检查文件是否已上传（这里需要与popup的逻辑保持一致）
            const isUploaded = checkFileUploadStatus(node.name);
            if (!isUploaded) {
                unuploadedFiles.push({
                    name: node.name,
                    path: currentPath ? `${currentPath}/${node.name}` : node.name,
                    size: node.size,
                    lastModified: node.lastModified
                });
            }
        } else if (node.type === 'folder' && node.children) {
            const folderPath = currentPath ? `${currentPath}/${node.name}` : node.name;
            node.children.forEach(child => extractFiles(child, folderPath));
        }
    }
    
    if (tree.files) {
        tree.files.forEach(file => extractFiles(file));
    }
    if (tree.folders) {
        tree.folders.forEach(folder => extractFiles(folder));
    }
    
    return unuploadedFiles;
}

/**
 * 检查文件上传状态
 * @param {string} fileName - 文件名
 * @returns {boolean} 是否已上传
 */
function checkFileUploadStatus(fileName) {
    // 这里应该与popup.js中的isFileUploaded逻辑保持一致
    // 简化实现：检查localStorage中的上传状态
    try {
        const uploadedFiles = JSON.parse(localStorage.getItem('uploadedFiles') || '[]');
        return uploadedFiles.includes(fileName);
    } catch (error) {
        debug('检查文件上传状态出错:', error);
        return false;
    }
}

/**
 * 统计文件树中的文件数量
 * @param {Object} tree - 文件树对象
 * @returns {number} 文件总数
 */
function countFilesInTree(tree) {
    let count = 0;
    
    function countInNode(node) {
        if (node.type === 'file') {
            count++;
        } else if (node.type === 'folder' && node.children) {
            node.children.forEach(child => countInNode(child));
        }
    }
    
    if (tree.files) {
        tree.files.forEach(file => countInNode(file));
    }
    if (tree.folders) {
        tree.folders.forEach(folder => countInNode(folder));
    }
    
    return count;
}

/**
 * 获取当前文件树
 * @returns {Object|null} 文件树对象
 */
async function getCurrentFileTree() {
    try {
        const result = await chrome.storage.local.get('admin_file_tree');
        return result.admin_file_tree || null;
    } catch (error) {
        debug('获取文件树出错:', error);
        return null;
    }
}

/**
 * 检查云盘页面可用性
 * @returns {boolean} 云盘页面是否可用
 */
async function checkYunpanPageAvailability() {
    return new Promise((resolve) => {
        handleCheckYunpanPage((response) => {
            resolve(response && response.available);
        });
    });
}

// =================================================================

/**
 * 处理主从协调架构相关的消息
 * @param {Object} request - 消息请求对象
 * @param {Object} sender - 发送者信息
 * @param {Function} sendResponse - 响应函数
 * @returns {boolean|undefined} 是否异步处理
 */
function handleCoordinationMessage(request, sender, sendResponse) {
    switch (request.type) {
        case 'POPUP_STATUS_CHANGE':
            debug(`收到popup状态变更: ${request.isOpen ? '打开' : '关闭'}`);
            updatePopupStatus(request.isOpen, 'popup_notification');
            sendResponse({ success: true });
            return false;
            
        case 'SET_AUTO_UPLOAD_STATE':
            debug(`收到自动上传状态设置: ${request.enabled ? '启用' : '禁用'}`);
            
            const wasEnabled = backgroundAutoUpload.enabled;
            backgroundAutoUpload.enabled = request.enabled;
            
            // 保存状态到存储
            chrome.storage.local.set({ backgroundAutoUploadEnabled: request.enabled });
            
            if (request.enabled && !wasEnabled) {
                // 启用后台自动检测
                startBackgroundDetection();
                debug('后台自动检测已启动');
            } else if (!request.enabled && wasEnabled) {
                // 停止后台自动检测
                stopBackgroundDetection();
                debug('后台自动检测已停止');
            }
            
            sendResponse({ 
                success: true, 
                backgroundEnabled: backgroundAutoUpload.enabled,
                detectionRunning: !!backgroundAutoUpload.detectionInterval
            });
            return false;
            
        case 'GET_BACKGROUND_STATUS':
            const status = {
                autoUploadEnabled: backgroundAutoUpload.enabled,
                detectionRunning: !!backgroundAutoUpload.detectionInterval,
                isRunning: backgroundAutoUpload.isRunning,
                intervalDuration: backgroundAutoUpload.intervalDuration,
                lastFileCount: backgroundAutoUpload.lastFileCount,
                popupStatus: popupStatus,
                uploadLock: getUploadLockStatus()
            };
            debug('响应后台状态查询:', status);
            sendResponse({ success: true, status });
            return false;
            
        case 'REQUEST_UPLOAD_LOCK':
            (async () => {
                const acquired = await acquireUploadLock('popup', request.operation || 'manual_upload');
                debug(`Popup请求上传锁: ${acquired ? '成功' : '失败'}`);
                sendResponse({ 
                    success: true, 
                    acquired: acquired,
                    lockStatus: getUploadLockStatus()
                });
            })();
            return true;
            
        case 'RELEASE_UPLOAD_LOCK':
            const released = releaseUploadLock('popup');
            debug(`Popup释放上传锁: ${released ? '成功' : '失败'}`);
            sendResponse({ 
                success: true, 
                released: released,
                lockStatus: getUploadLockStatus()
            });
            return false;
            
        case 'POPUP_HEARTBEAT':
            // 更新popup心跳时间
            popupStatus.lastHeartbeat = Date.now();
            popupStatus.isOpen = true;
            sendResponse({ success: true, timestamp: Date.now() });
            return false;

        case 'GET_AUTO_UPLOAD_STATUS':
            // 获取自动上传状态
            sendResponse({
                success: true,
                enabled: backgroundAutoUpload.enabled,
                intervalDuration: backgroundAutoUpload.intervalDuration,
                isRunning: backgroundAutoUpload.isRunning
            });
            return false;

        case 'SET_AUTO_UPLOAD':
            // 设置自动上传状态
            const enabled = request.enabled === true;
            backgroundAutoUpload.enabled = enabled;

            // 保存到存储
            chrome.storage.local.set({ backgroundAutoUploadEnabled: enabled });

            if (enabled) {
                startBackgroundDetection();
                debug('自动上传已启用，开始后台检测');
            } else {
                stopBackgroundDetection();
                debug('自动上传已禁用，停止后台检测');
            }

            sendResponse({
                success: true,
                enabled: backgroundAutoUpload.enabled,
                message: enabled ? '自动上传已启用' : '自动上传已禁用'
            });
            return false;

        case 'SET_AUTO_REFRESH':
            // 设置自动刷新网页
            const interval = parseInt(request.interval) || 0;
            console.log(`[后台] 收到自动刷新设置请求: ${interval}秒`);

            if (interval > 0) {
                // 获取发送消息的标签页ID
                const tabId = sender?.tab?.id;
                console.log(`[后台] 开始设置自动刷新，标签页ID: ${tabId}`);

                startAutoPageRefresh(interval, tabId);
                console.log(`[后台] 自动刷新网页已启用，间隔: ${interval}秒`);
                debug(`自动刷新网页已启用，间隔: ${interval}秒`);

                sendResponse({
                    success: true,
                    enabled: true,
                    interval: interval,
                    message: `自动刷新已设置为${interval}秒`
                });
            } else {
                console.log(`[后台] 关闭自动刷新`);
                stopAutoPageRefresh();
                debug('自动刷新网页已禁用');

                sendResponse({
                    success: true,
                    enabled: false,
                    interval: 0,
                    message: '自动刷新已关闭'
                });
            }
            return false;

        case 'GET_AUTO_REFRESH_STATUS':
            // 获取自动刷新状态
            sendResponse({
                success: true,
                enabled: autoPageRefresh.enabled,
                interval: autoPageRefresh.interval,
                lastRefreshTime: autoPageRefresh.lastRefreshTime,
                targetTabId: autoPageRefresh.targetTabId
            });
            return false;

        case 'GET_ALLOW_ACTIVE_REFRESH':
            // 获取是否允许刷新活跃标签页的设置
            chrome.storage.local.get('allowRefreshActiveTab', (result) => {
                sendResponse({
                    success: true,
                    allowed: result.allowRefreshActiveTab === true
                });
            });
            return true; // 异步响应

        case 'SET_ALLOW_ACTIVE_REFRESH':
            // 设置是否允许刷新活跃标签页
            const allowed = request.allowed === true;
            chrome.storage.local.set({ allowRefreshActiveTab: allowed }, () => {
                console.log(`[后台] 设置允许刷新活跃标签页: ${allowed}`);
                sendResponse({
                    success: true,
                    allowed: allowed,
                    message: allowed ? '已启用刷新当前页面' : '已禁用刷新当前页面'
                });
            });
            return true; // 异步响应

        case 'MANUAL_REFRESH_CHECK':
            // 手动触发刷新检查
            console.log('[后台] 收到手动刷新检查请求');
            if (autoPageRefresh.enabled && autoPageRefresh.timerId) {
                console.log('[后台] 自动刷新已启用，手动执行一次刷新检查');
                performPageRefresh().then(() => {
                    sendResponse({
                        success: true,
                        message: '手动刷新检查已执行'
                    });
                }).catch(error => {
                    sendResponse({
                        success: false,
                        error: error.message
                    });
                });
            } else {
                console.log('[后台] 自动刷新未启用');
                sendResponse({
                    success: false,
                    message: '自动刷新未启用'
                });
            }
            return true; // 异步响应

        case 'RESET_CONNECTION':
            // 重置WebSocket连接
            console.log('[后台] 收到重置连接请求');
            try {
                // 断开现有连接
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.close();
                }

                // 重新初始化连接
                setTimeout(() => {
                    initWebSocket();
                }, 1000);

                sendResponse({
                    success: true,
                    message: '连接重置已启动'
                });
            } catch (error) {
                console.error('[后台] 重置连接失败:', error);
                sendResponse({
                    success: false,
                    error: error.message
                });
            }
            return false;

        case 'GET_CONNECTION_STATUS':
            // 获取WebSocket连接状态
            const connected = ws && ws.readyState === WebSocket.OPEN;
            console.log(`[后台] 连接状态查询: ${connected ? '已连接' : '未连接'}`);
            sendResponse({
                success: true,
                connected: connected,
                readyState: ws ? ws.readyState : -1
            });
            return false;

        default:
            return undefined; // 未处理的消息类型
    }
}

// =================================================================

// =================================================================
// 插件初始化和启动逻辑
// =================================================================

/**
 * 初始化主从协调架构
 */
async function initCoordinationArchitecture() {
    debug('初始化主从协调架构');
    
    try {
        // 1. 恢复后台自动上传状态，默认启用
        const settings = await chrome.storage.local.get('backgroundAutoUploadEnabled');

        // 如果没有存储的设置，默认启用自动上传
        if (settings.backgroundAutoUploadEnabled === undefined) {
            backgroundAutoUpload.enabled = true;
            // 保存默认设置
            chrome.storage.local.set({ backgroundAutoUploadEnabled: true });
            debug('首次运行，设置后台自动上传状态：启用（默认）');
        } else {
            backgroundAutoUpload.enabled = settings.backgroundAutoUploadEnabled;
            debug(`已恢复后台自动上传状态：${backgroundAutoUpload.enabled ? '启用' : '禁用'}`);
        }

        // 如果启用了自动上传，启动后台检测
        if (backgroundAutoUpload.enabled) {
            // 延迟启动后台检测，确保其他组件初始化完成
            setTimeout(() => {
                startBackgroundDetection();
                debug('后台自动检测已启动');
            }, 10000); // 延迟10秒
        }

        // 2. 恢复自动刷新网页状态
        const refreshSettings = await chrome.storage.local.get(['autoPageRefreshEnabled', 'autoPageRefreshInterval']);
        if (refreshSettings.autoPageRefreshEnabled && refreshSettings.autoPageRefreshInterval > 0) {
            // 延迟启动自动刷新，确保标签页已加载
            setTimeout(() => {
                startAutoPageRefresh(refreshSettings.autoPageRefreshInterval);
                debug(`已恢复自动刷新网页，间隔: ${refreshSettings.autoPageRefreshInterval}秒`);
            }, 15000); // 延迟15秒，比自动上传稍晚启动
        } else {
            debug('自动刷新网页状态：禁用');
        }
        
        // 2. 初始化popup状态为关闭
        updatePopupStatus(false, 'initialization');
        
        // 3. 清理可能存在的旧锁
        releaseUploadLock();
        
        debug('主从协调架构初始化完成');
        
    } catch (error) {
        debug('初始化主从协调架构出错:', error);
    }
}

/**
 * 插件启动时的初始化
 */
function initializeExtension() {
    debug('开始初始化文件云流转助手插件');
    
    // 启动WebSocket连接
    initWebSocket();
    
    // 初始化主从协调架构
    initCoordinationArchitecture();
    
    debug('插件初始化完成');
}

// Chrome扩展生命周期事件监听器
chrome.runtime.onStartup.addListener(() => {
    debug('Chrome扩展启动事件');
    initializeExtension();
});

chrome.runtime.onInstalled.addListener((details) => {
    debug('Chrome扩展安装/更新事件', details);
    initializeExtension();
});

// 立即初始化（用于开发环境重载）
debug('开始立即初始化');
initializeExtension();

// =================================================================

// 处理自动上传触发请求
async function handleTriggerAutoUpload(request, sendResponse) {
    try {
        debug('收到自动上传触发请求', request);

        // 立即响应面板，表示触发成功
        sendResponse({
            success: true,
            message: '自动上传已触发',
            timestamp: Date.now()
        });

        // 异步执行上传逻辑（立即执行，不延迟）
        (async () => {
            try {
                debug('开始执行自动上传逻辑');

                // 1. 获取文件树数据
                debug('获取文件树数据...');
                const result = await chrome.storage.local.get('admin_file_tree');
                const fileTree = result.admin_file_tree;

                debug('文件树数据获取结果:', fileTree ? '存在' : '不存在');

                if (!fileTree) {
                    debug('没有找到文件树数据，无法执行自动上传');
                    return;
                }

                // 2. 提取待上传文件
                debug('提取待上传文件...');
                const unuploadedFiles = extractUnuploadedFilesFromTree(fileTree);

                debug(`提取结果: 找到 ${unuploadedFiles.length} 个待上传文件`);

                if (unuploadedFiles.length === 0) {
                    debug('没有待上传文件，结束上传流程');
                    return;
                }

                debug('待上传文件详情:', unuploadedFiles);

                // 3. 检查云盘页面是否可用
                debug('检查云盘页面是否可用...');
                const tabs = await chrome.tabs.query({});
                const yunpanTab = tabs.find(tab =>
                    tab.url && (
                        tab.url.includes('yunpan.gdcourts.gov.cn') ||
                        tab.url.includes('yunpan.gdcourts.gov.cn:82')
                    )
                );

                if (!yunpanTab) {
                    debug('未找到云盘页面，尝试打开云盘页面');
                    await openFileCloudPage();

                    // 等待页面准备
                    await new Promise(resolve => setTimeout(resolve, 3000));

                    // 重新查找云盘页面
                    const newTabs = await chrome.tabs.query({});
                    const newYunpanTab = newTabs.find(tab =>
                        tab.url && (
                            tab.url.includes('yunpan.gdcourts.gov.cn') ||
                            tab.url.includes('yunpan.gdcourts.gov.cn:82')
                        )
                    );

                    if (!newYunpanTab) {
                        debug('打开云盘页面失败，无法执行上传');
                        return;
                    }
                }

                // 4. 直接执行上传逻辑
                debug('调用executeYunpanUpload...');
                const requestData = {
                    files: unuploadedFiles,
                    source: 'background_auto_upload'
                };

                const uploadResult = await executeYunpanUpload(requestData);
                debug('上传执行完成:', uploadResult);

            } catch (error) {
                debug('自动上传执行失败', error);
                debug('错误详情:', error.message);
                debug('错误堆栈:', error.stack);
            }
        })();

    } catch (error) {
        debug('处理自动上传触发请求失败', error);
        sendResponse({
            success: false,
            error: `自动上传触发失败: ${error.message}`,
            details: {
                suggestion: '请检查扩展状态和权限',
                originalError: error.message
            }
        });
    }
}

// 从文件树中提取待上传文件
function extractUnuploadedFilesFromTree(fileTree) {
    debug('开始从文件树中提取待上传文件');
    debug('文件树结构:', fileTree);

    const unuploadedFiles = [];
    let totalFiles = 0;
    let pendingFiles = 0;
    let failedFiles = 0;

    function traverseTree(node, currentPath = '') {
        debug(`遍历节点: ${node.name || 'unnamed'}, 类型: ${node.type}, 状态: ${node.status}`);

        if (node.type === 'file') {
            totalFiles++;

            // 检查文件是否需要上传
            if (node.status === 'pending') {
                pendingFiles++;
                const fileInfo = {
                    name: node.fileName || node.name,
                    fileName: node.fileName || node.name,
                    path: node.path || (currentPath + '/' + (node.fileName || node.name)),
                    size: node.size || 0,
                    type: node.type || 'unknown',
                    status: node.status
                };
                unuploadedFiles.push(fileInfo);
                debug('添加待上传文件:', fileInfo);
            } else if (node.status === 'failed') {
                failedFiles++;
                const fileInfo = {
                    name: node.fileName || node.name,
                    fileName: node.fileName || node.name,
                    path: node.path || (currentPath + '/' + (node.fileName || node.name)),
                    size: node.size || 0,
                    type: node.type || 'unknown',
                    status: node.status
                };
                unuploadedFiles.push(fileInfo);
                debug('添加失败重试文件:', fileInfo);
            }
        } else if (node.children && Array.isArray(node.children)) {
            // 递归遍历子节点
            debug(`遍历文件夹: ${node.name}, 子节点数: ${node.children.length}`);
            for (const child of node.children) {
                const childPath = currentPath ? `${currentPath}/${node.name}` : node.name;
                traverseTree(child, childPath);
            }
        }
    }

    if (fileTree && fileTree.children && Array.isArray(fileTree.children)) {
        debug(`开始遍历根节点，子节点数: ${fileTree.children.length}`);
        for (const child of fileTree.children) {
            traverseTree(child);
        }
    } else {
        debug('文件树结构异常，没有children数组');
    }

    debug(`文件提取完成 - 总文件数: ${totalFiles}, 待上传: ${pendingFiles}, 失败重试: ${failedFiles}, 最终提取: ${unuploadedFiles.length}`);

    return unuploadedFiles;
}

// 直接执行云盘上传（不通过消息传递）
async function executeYunpanUpload(requestData) {
    try {
        debug('直接执行云盘上传', requestData);

        // 验证请求数据
        if (!requestData || !requestData.files || !Array.isArray(requestData.files)) {
            throw new Error('上传数据无效');
        }

        debug('开始处理云盘上传请求', {
            filesCount: requestData.files.length,
            requestData: requestData
        });

        // 查找云盘页面标签
        debug('开始查找云盘页面标签');
        const tabs = await chrome.tabs.query({});
        debug(`查询到 ${tabs.length} 个标签页`);

        // 打印所有标签页的URL用于调试
        tabs.forEach((tab, index) => {
            debug(`标签页 ${index}: ${tab.url}`);
        });

        const yunpanTab = tabs.find(tab =>
            tab.url && (
                tab.url.includes('yunpan.gdcourts.gov.cn') ||
                tab.url.includes('yunpan.gdcourts.gov.cn:82')
            )
        );

        if (!yunpanTab) {
            debug('未找到云盘页面标签，可能的原因：');
            debug('1. 云盘页面未打开');
            debug('2. URL不匹配');
            debug('3. 页面正在加载中');
            throw new Error('未找到云盘页面，请先打开云盘页面');
        }

        debug('找到云盘页面标签', {
            tabId: yunpanTab.id,
            tabUrl: yunpanTab.url,
            tabTitle: yunpanTab.title
        });

        // 先测试与云盘页面的连接
        debug('测试与云盘页面的连接...');
        try {
            const pingResponse = await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('PING超时'));
                }, 5000);

                chrome.tabs.sendMessage(yunpanTab.id, {
                    type: 'PING'
                }, (response) => {
                    clearTimeout(timeout);
                    if (chrome.runtime.lastError) {
                        reject(new Error(`PING失败: ${chrome.runtime.lastError.message}`));
                    } else {
                        resolve(response);
                    }
                });
            });

            debug('PING响应:', pingResponse);

            if (!pingResponse || !pingResponse.success) {
                throw new Error('云盘页面内容脚本未响应');
            }
        } catch (pingError) {
            debug('PING测试失败:', pingError);
            throw new Error(`云盘页面连接失败: ${pingError.message}`);
        }

        debug('准备向云盘页面发送上传请求', {
            tabId: yunpanTab.id,
            tabUrl: yunpanTab.url,
            filesCount: requestData.files.length
        });

        // 发送上传请求到云盘页面的content script
        const response = await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('云盘页面响应超时（60秒）'));
            }, 60000); // 60秒超时

            chrome.tabs.sendMessage(yunpanTab.id, {
                type: 'START_UPLOAD',
                data: requestData
            }, (response) => {
                clearTimeout(timeout);

                if (chrome.runtime.lastError) {
                    reject(new Error(`云盘页面通信失败: ${chrome.runtime.lastError.message}`));
                } else {
                    resolve(response);
                }
            });
        });

        debug('云盘页面响应', response);

        if (response && response.success) {
            // content script返回成功
            const uploadStats = response.result || {
                total: requestData.files.length,
                completed: 0,
                failed: requestData.files.length
            };

            debug('云盘上传成功', uploadStats);
            return uploadStats;

        } else {
            // content script返回失败
            const errorMsg = response?.error || '云盘页面处理失败';
            debug('云盘页面返回失败', errorMsg);
            throw new Error(errorMsg);
        }

    } catch (error) {
        debug('直接执行云盘上传失败', error);
        throw error;
    }
}

debug('文件云流转助手 Background Script 加载完成');

// =================================================================
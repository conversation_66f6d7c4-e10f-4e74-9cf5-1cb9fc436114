// 调试函数 - 优化版，减少噪音
function debug(message, data = null) {
    // 过滤掉过度的状态日志
    const shouldSuppress = [
        '文件未上传',
        'DOM渲染完成',
        '备用初始化',
        '开始预加载可见缩略图'
    ].some(pattern => message.includes(pattern));
    
    if (!shouldSuppress) {
        console.log(`[文件云流转助手] ${new Date().toLocaleTimeString()}: ${message}`, data || '');
    }
}

// 全局变量
let isPopupReady = false;
let lastSyncTime = 0; // 最后同步时间戳
let currentAdminTree = null; // 当前的文件树结构
let activeTab = 'pending'; // 当前活跃的标签页
let folderStates = {}; // 文件夹折叠状态

// === 重构：上传队列管理系统 ===
let uploadQueue = []; // 上传任务队列
let activeUploadTasks = new Map(); // 当前活跃的上传任务 Map<taskId, taskInfo>
let uploadTaskCounter = 0; // 任务ID计数器
let maxConcurrentUploads = 1; // 最大并发上传数（当前设为1，未来可配置）

// 原有的状态变量保留用于兼容性，但逐步替换
let uploadInProgress = false; // 渐进式替换：用于简单的上传状态检查
let autoUploadEnabled = false; // 自动上传开关状态
let autoUploadWaitingQueue = []; // 自动上传等待队列
let lastFileCount = 0; // 上次的文件数量，用于检测新文件
let uploadStartTime = 0; // 上传开始时间，用于防止状态混乱
let fileUploadStates = new Map(); // 文件上传状态映射表（保留用于兼容性）

// 简化本地存储键名 - 只保留必要的UI状态
const FOLDER_STATES_KEY = 'cloud_transfer_folder_states';
const SYNC_INTERVAL = 60000; // 同步间隔，单位：毫秒

// 导入必要的模块
import LazyThumbnail from './components/LazyThumbnail.js';
import PreviewModal from './components/PreviewModal.js';
import thumbnailCache from './utils/thumbnail-cache.js';

let lazyThumbnailManager = null;
let previewModal = null;

// 初始化函数
function initialize() {
    debug('初始化popup');
    isPopupReady = true;
    
    // === 重置上传状态（重要：防止页面刷新后状态残留）===
    debug('重置上传队列状态');
    uploadQueue.length = 0;
    activeUploadTasks.clear();
    uploadInProgress = false;
    uploadStartTime = 0;
    uploadTaskCounter = 0;
    
    // 获取DOM元素
    const refreshListBtn = document.getElementById('refreshList');
    const uploadAllBtn = document.getElementById('uploadAll');
    const openCloudBtn = document.getElementById('openCloud');

    if (!refreshListBtn || !uploadAllBtn || !openCloudBtn) {
        debug('错误: 未找到必要的DOM元素');
        return;
    }

    // 绑定按钮事件
    refreshListBtn.addEventListener('click', () => {
        debug('刷新文件树 - 重新获取最新数据');
        
        // 1. 重置强制刷新标记（现在缓存已禁用，这个主要用于视觉提示）
        window._forceRefreshMode = true;
        
        // 2. 直接请求最新文件树
        requestFileTree();
        
        // 3. 同时通过WebSocket强制重新获取文件树
        chrome.runtime.sendMessage({ 
            type: 'SEND_WS_MESSAGE',
            wsMessage: {
                type: 'get_full_admin_file_tree',
                forceRefresh: true,
                timestamp: Date.now()
            }
        });
        
        // 4. 短暂显示刷新状态
        showStatus('正在刷新文件列表...', 'info');
        
        // 5. 3秒后重置刷新标记
        setTimeout(() => {
            window._forceRefreshMode = false;
            debug('已重置强制刷新模式');
        }, 3000);
    });

    // 修改上传按钮事件处理 - 连接云盘上传功能
    uploadAllBtn.addEventListener('click', async () => {
        debug('上传按钮被点击');
        await handleUploadClick();
    });

    openCloudBtn.addEventListener('click', () => {
        debug('打开云盘页面');
        chrome.runtime.sendMessage({ type: 'OPEN_FILE_CLOUD_PAGE' });
    });

    // 初始化标签页功能
    initializeTabs();
    
    // 初始化文件夹操作按钮
    initializeFolderActions();
    
    // 移除缓存加载 - 改为无状态架构，只保留UI状态
    // 移除: loadUploadedFiles();
    // 移除: loadFileUploadStates();
    loadFolderStates(); // 只保留文件夹折叠状态
    
    // 初始化缩略图系统
    initializeThumbnailSystem();
    
    // 初始化云盘上传功能
    initUploadButton();
    
    // 初始化自动上传开关
    initAutoUploadToggle();
    
    // 定期检查云盘页面状态
    checkYunpanPageStatus();
    setInterval(checkYunpanPageStatus, 30000); // 每30秒检查一次
    
    // 获取连接状态
    chrome.runtime.sendMessage({ type: 'GET_CONNECTION_STATUS' }, (response) => {
        if (response) {
            updateConnectionStatus(response.connected);
            
            // 连接正常时立即获取最新数据
            if (response.connected) {
                debug('WebSocket已连接，立即请求最新文件树');
                // 强制从服务器获取最新数据
                chrome.runtime.sendMessage({ 
                    type: 'SEND_WS_MESSAGE',
                    wsMessage: {
                        type: 'get_full_admin_file_tree',
                        forceRefresh: true,
                        source: 'popup_init',
                        timestamp: Date.now()
                    }
                });
            }
        }
    });

    // 请求文件树数据
    requestFileTree();
    
    // 启动popup的定期更新机制 - 作为备用
    startPopupPeriodicUpdate();

    // 确保消息监听器只添加一次
    if (!chrome.runtime.onMessage.hasListener(handleBackgroundMessages)) {
        chrome.runtime.onMessage.addListener(handleBackgroundMessages);
    }
    
    // 绑定文件项目事件
    bindFileItemEvents();
    
    // 延迟进行自动上传初始检查，确保所有初始化完成
    setTimeout(() => {
        debug('执行自动上传初始检查');
        if (autoUploadEnabled) {
            debug('自动上传已启用，执行初始检查');
            checkAndTriggerAutoUpload();
        } else {
            debug('自动上传未启用，跳过初始检查');
        }
    }, 5000); // 5秒后执行，确保文件树和其他组件都已加载完成
    
    // === 暴露调试函数到全局作用域 ===
    window.uploadDebug = {
        getQueueStatus: getUploadQueueStatus,
        forceReset: forceResetUploadState,
        queue: () => uploadQueue,
        activeTasks: () => activeUploadTasks,
        triggerAutoUpload: () => checkAndTriggerAutoUpload()
    };
    
    debug('上传调试函数已暴露到 window.uploadDebug');
}

// 初始化标签页功能
function initializeTabs() {
    const pendingTab = document.getElementById('pendingTab');
    const uploadedTab = document.getElementById('uploadedTab');
    
    if (pendingTab && uploadedTab) {
        pendingTab.addEventListener('click', () => switchTab('pending'));
        uploadedTab.addEventListener('click', () => switchTab('uploaded'));
    }
}

// 切换标签页
function switchTab(tab) {
    debug(`切换到标签页: ${tab}`);
    
    // 更新活跃标签
    activeTab = tab;
    
    // 更新标签页按钮状态
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });
    document.getElementById(tab + 'Tab').classList.add('active');
    
    // 更新标签页面板状态
    document.querySelectorAll('.tab-panel').forEach(panel => {
        panel.classList.remove('active');
    });
    document.getElementById(tab + 'Panel').classList.add('active');
    
    // 重新渲染当前标签页的内容
    if (currentAdminTree) {
        renderTreeForCurrentTab();
        
        // 使用多层异步确保DOM完全渲染后再处理缩略图
        requestAnimationFrame(() => {
            requestAnimationFrame(() => {
                const currentContainer = document.getElementById(activeTab + 'TreeContainer');
                
                if (currentContainer && lazyThumbnailManager) {
                    // 验证容器内是否有缩略图元素
                    const thumbnailElements = currentContainer.querySelectorAll('[data-thumbnail-path]');
                    debug(`发现 ${thumbnailElements.length} 个缩略图元素需要处理`);
                    
                    if (thumbnailElements.length > 0) {
                        // 重新初始化缩略图系统
                        lazyThumbnailManager.updateContainer(currentContainer);
                        
                        // 额外延迟确保所有异步操作完成
                        setTimeout(() => {
                            if (lazyThumbnailManager) {
                                lazyThumbnailManager.preloadVisible();
                                debug(`已重新初始化 ${activeTab} 标签页的缩略图系统`);
                            }
                        }, 100);
                    } else {
                        debug(`${activeTab} 标签页没有缩略图元素，跳过缩略图初始化`);
                    }
                } else {
                    debug(`缩略图系统初始化失败: container=${!!currentContainer}, manager=${!!lazyThumbnailManager}`);
                }
            });
        });
    }
}

// 初始化文件夹操作按钮
function initializeFolderActions() {
    // 展开所有文件夹按钮
    document.getElementById('expandAllPending')?.addEventListener('click', () => {
        expandAllFolders('pending');
    });
    
    document.getElementById('expandAllUploaded')?.addEventListener('click', () => {
        expandAllFolders('uploaded');
    });
    
    // 折叠所有文件夹按钮
    document.getElementById('collapseAllPending')?.addEventListener('click', () => {
        collapseAllFolders('pending');
    });
    
    document.getElementById('collapseAllUploaded')?.addEventListener('click', () => {
        collapseAllFolders('uploaded');
    });
}

// 展开所有文件夹
function expandAllFolders(tab) {
    debug(`展开所有文件夹: ${tab}`);
    const container = document.getElementById(tab + 'TreeContainer');
    if (container) {
        container.querySelectorAll('.folder-toggle').forEach(toggle => {
            if (!toggle.classList.contains('expanded')) {
                toggle.click();
            }
        });
    }
}

// 折叠所有文件夹
function collapseAllFolders(tab) {
    debug(`折叠所有文件夹: ${tab}`);
    const container = document.getElementById(tab + 'TreeContainer');
    if (container) {
        container.querySelectorAll('.folder-toggle.expanded').forEach(toggle => {
            toggle.click();
        });
    }
}

// 请求文件树数据
function requestFileTree() {
    debug('请求文件树数据');
    showStatus('正在加载文件列表...', 'info');
    
    chrome.runtime.sendMessage({ type: 'GET_ADMIN_TREE' }, (response) => {
        if (chrome.runtime.lastError) {
            console.error('请求文件树失败:', chrome.runtime.lastError.message);
            showStatus(`获取文件列表失败: ${chrome.runtime.lastError.message}`, 'error');
            showEmptyState('加载文件列表失败');
        } else if (response && response.success) {
            debug('从后台收到文件树', response.tree);
            currentAdminTree = response.tree;
            
            if (currentAdminTree) {
                renderTreeForCurrentTab();
                updateFileCounts();
            } else {
                showEmptyState('文件树数据为空');
            }
        } else {
            console.error('后台返回无效的文件树响应', response);
            showStatus('获取文件列表时发生未知错误', 'error');
            showEmptyState('加载文件列表出错');
        }
    });
}

// 处理后台消息
function handleBackgroundMessages(message, sender, sendResponse) {
    debug('收到后台消息:', message);

    switch (message.type) {
        case 'CONNECTION_STATUS_CHANGED':
            updateConnectionStatus(message.connected);
            
            // 连接状态变化时，如果重新连接则立即获取数据
            if (message.connected) {
                debug('WebSocket重新连接，立即请求最新数据');
                setTimeout(() => {
                    requestFileTree();
                }, 1000); // 延迟1秒确保连接稳定
            }
            break;
            
        case 'files_updated':
            debug('服务器文件列表已更新');
            showStatus('文件列表已更新', 'success');
            // 立即重新获取文件树
            requestFileTree();
            // 同时重新渲染当前标签页
            setTimeout(() => {
                renderTreeForCurrentTab();
            }, 500);
            break;
            
        case 'admin_tree_updated':
            debug('文件树已更新，立即刷新UI', message);
            showStatus(`文件树已更新 (${message.stats?.files || 0} 个文件)`, 'success');
            
            // 立即重新获取并渲染
            requestFileTree();
            
            // 强制重新渲染当前标签页
            setTimeout(() => {
                if (currentAdminTree) {
                    renderTreeForCurrentTab();
                    updateFileCounts();
                }
            }, 200);
            break;
            
        case 'BACKGROUND_AUTO_UPLOAD_STARTED':
            // 新增：处理后台自动上传启动通知
            debug('收到后台自动上传启动通知', message);
            showStatus(
                `后台自动上传已启动，正在上传 ${message.filesCount} 个文件...`,
                'info'
            );
            
            // 如果popup当前打开，显示上传状态
            if (message.tabId) {
                showStatus(
                    `云盘标签页 ${message.tabId} 正在处理 ${message.filesCount} 个文件`,
                    'info'
                );
            }
            
            // 在一段时间后刷新文件列表，查看上传结果
            setTimeout(() => {
                debug('后台自动上传后刷新文件列表');
                requestFileTree();
            }, 30000); // 30秒后刷新，给上传过程一些时间
            break;
            
        default:
            // 处理其他未知消息类型
            debug('收到未知类型的后台消息', message);
            break;
    }
}

// 渲染当前标签页的树状结构
function renderTreeForCurrentTab() {
    debug(`渲染树状结构，当前标签: ${activeTab}`);
    
    if (!currentAdminTree) {
        debug('没有文件树数据');
        return;
    }
    
    const container = document.getElementById(activeTab + 'TreeContainer');
    if (!container) {
        debug('找不到容器:', activeTab + 'TreeContainer');
        return;
    }
    
    // 根据当前标签页过滤文件树
    const filteredTree = filterTreeByUploadStatus(currentAdminTree, activeTab === 'uploaded');
    
    if (!filteredTree || (!filteredTree.files?.length && !filteredTree.folders?.length)) {
        showEmptyState(activeTab === 'uploaded' ? '暂无已上传文件' : '暂无待上传文件', container);
        return;
    }

    // 渲染树状结构
    const treeHTML = renderTreeNode(filteredTree, 0, '');
    container.innerHTML = treeHTML;
    
    // 恢复文件夹状态
    restoreFolderStates(container);
    
    // 使用MutationObserver确保DOM完全渲染后再初始化缩略图
    const observer = new MutationObserver((mutations) => {
        // 检查是否有新的缩略图元素被添加
        const hasNewThumbnails = mutations.some(mutation => 
            Array.from(mutation.addedNodes).some(node => 
                node.nodeType === Node.ELEMENT_NODE && 
                (node.hasAttribute?.('data-thumbnail-path') || 
                 node.querySelector?.('[data-thumbnail-path]'))
            )
        );
        
        if (hasNewThumbnails || mutations.length > 0) {
            // 停止观察
            observer.disconnect();
            
            // 延迟初始化缩略图，确保DOM稳定
            setTimeout(() => {
                if (lazyThumbnailManager) {
                    const thumbnailElements = container.querySelectorAll('[data-thumbnail-path]');
                    if (thumbnailElements.length > 0) {
                        debug(`DOM渲染完成，发现 ${thumbnailElements.length} 个缩略图元素`);
                        lazyThumbnailManager.preloadVisible();
                    }
                }
            }, 50);
        }
    });
    
    // 开始观察DOM变化
    observer.observe(container, {
        childList: true,
        subtree: true
    });
    
    // 设置超时机制，避免观察器长时间运行
    setTimeout(() => {
        observer.disconnect();
        // 备用初始化，确保即使MutationObserver失效也能正常工作
        if (lazyThumbnailManager) {
            const thumbnailElements = container.querySelectorAll('[data-thumbnail-path]');
            if (thumbnailElements.length > 0) {
                debug(`备用初始化: 发现 ${thumbnailElements.length} 个缩略图元素`);
                lazyThumbnailManager.preloadVisible();
            }
        }
    }, 500);
    
    // 绑定事件
    bindTreeEvents(container);
}

// 根据上传状态过滤文件树
function filterTreeByUploadStatus(tree, showUploaded) {
    if (!tree) return null;
    
    const filtered = {
        files: [],
        folders: []
    };
    
    // 过滤文件
    if (tree.files && Array.isArray(tree.files)) {
        filtered.files = tree.files.filter(file => {
            const isUploaded = isFileUploaded(file);
            return showUploaded ? isUploaded : !isUploaded;
        });
    }
    
    // 递归过滤文件夹
    if (tree.folders && Array.isArray(tree.folders)) {
        tree.folders.forEach(folder => {
            const filteredFolder = filterTreeByUploadStatus(folder.children, showUploaded);
            
            // 只有当文件夹包含符合条件的文件或子文件夹时才包含它
            if (filteredFolder && (filteredFolder.files.length > 0 || filteredFolder.folders.length > 0)) {
                filtered.folders.push({
                    ...folder,
                    children: filteredFolder
                });
            }
        });
    }
    
    return filtered;
}

// 检查文件是否已上传 - 重构为实时从服务器状态获取
function isFileUploaded(file) {
    if (!file) return false;
    
    // 直接从服务器状态检查，不再使用本地缓存
    const serverStatus = file.syncStatus || file.status;
    
    // 检查服务器状态是否表示已上传
    const uploadedStatuses = ['已同步', '已上传', 'uploaded'];
    const isUploaded = uploadedStatuses.includes(serverStatus);
    
    debug(`文件上传状态检查: ${file.name || file.filename} -> ${serverStatus} (${isUploaded ? '已上传' : '未上传'})`);
    return isUploaded;
}

// 渲染树节点
function renderTreeNode(node, level, parentPath) {
    if (!node) return '';
    
    let html = '';
    
    // 渲染文件夹
    if (node.folders && Array.isArray(node.folders)) {
        node.folders.forEach(folder => {
            const folderPath = parentPath ? `${parentPath}/${folder.name}` : folder.name;
            const folderId = `folder_${folderPath.replace(/[^a-zA-Z0-9]/g, '_')}`;
            const hasChildren = (folder.children?.files?.length > 0) || (folder.children?.folders?.length > 0);
            const fileCount = countFilesInFolder(folder.children);
            
            html += `
                <div class="tree-item tree-folder" data-level="${level}" data-folder-path="${folderPath}">
                    ${hasChildren ? `<button class="folder-toggle" data-folder-id="${folderId}">▶</button>` : '<span style="width: 20px;"></span>'}
                    <span class="folder-icon">📁</span>
                    <span class="folder-name">${folder.name}</span>
                    <span class="folder-stats">${fileCount} 项</span>
                </div>
                <div class="folder-content" id="${folderId}" style="display: none;">
                    ${hasChildren ? renderTreeNode(folder.children, level + 1, folderPath) : ''}
                </div>
            `;
        });
    }
    
    // 渲染文件
    if (node.files && Array.isArray(node.files)) {
        node.files.forEach(file => {
            const filePath = parentPath ? `${parentPath}/${file.name}` : file.name;
            const fileType = getFileType(file.name);
            const extension = getFileExtension(file.name);
            const isUploaded = isFileUploaded(file);
            const hasPreview = ['image', 'video', 'audio', 'document', 'text'].includes(fileType);
            
            // 构建更完整的缩略图路径信息
            const fileName = file.name || file.filename;
            
            // === 在前端进行智能路径简化，与后台策略保持一致 ===
            let smartThumbnailPath = fileName; // 默认使用文件名
            
            // 优先使用智能简化后的路径
            const originalPath = file.path || file.fullPath || filePath;
            if (originalPath && originalPath !== fileName) {
                const pathParts = originalPath.split('/').filter(Boolean);
                
                debug(`处理文件路径简化: ${originalPath}`, { pathParts });
                
                if (pathParts.length > 1) {
                    // 临时文件上传路径处理 (格式: 临时文件上传/设备ID/实际路径)
                    if (originalPath.includes('临时文件上传')) {
                        // 查找"临时文件上传"后面的有效路径部分
                        const tempUploadIndex = pathParts.findIndex(part => part === '临时文件上传');
                        if (tempUploadIndex >= 0 && tempUploadIndex < pathParts.length - 2) {
                            // 跳过"临时文件上传"和设备ID，获取实际的目录结构
                            const actualPathParts = pathParts.slice(tempUploadIndex + 2); // 跳过临时文件上传和设备ID
                            if (actualPathParts.length > 0) {
                                // 去掉文件名，保留目录结构
                                const dirParts = actualPathParts.slice(0, -1);
                                if (dirParts.length > 0) {
                                    smartThumbnailPath = dirParts.join('/') + '/' + fileName;
                                    debug(`临时文件路径简化: ${originalPath} -> ${smartThumbnailPath}`);
                                } else {
                                    smartThumbnailPath = fileName;
                                    debug(`临时文件路径简化，无目录结构: ${originalPath} -> ${smartThumbnailPath}`);
                                }
                            }
                        }
                    }
                    // 如果路径包含设备ID（UUID格式），但不是临时文件上传格式
                    else if (/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/.test(originalPath)) {
                        // 找到设备ID的位置并跳过它
                        const deviceIdIndex = pathParts.findIndex(part => 
                            /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/.test(part)
                        );
                        
                        if (deviceIdIndex >= 0 && deviceIdIndex < pathParts.length - 1) {
                            // 获取设备ID后面的路径部分
                            const afterDeviceId = pathParts.slice(deviceIdIndex + 1);
                            if (afterDeviceId.length > 1) {
                                // 去掉文件名，保留目录结构
                                const dirParts = afterDeviceId.slice(0, -1);
                                smartThumbnailPath = dirParts.join('/') + '/' + fileName;
                                debug(`设备ID路径简化: ${originalPath} -> ${smartThumbnailPath}`);
                            } else {
                                smartThumbnailPath = fileName;
                                debug(`设备ID路径简化，无目录结构: ${originalPath} -> ${smartThumbnailPath}`);
                            }
                        }
                    }
                    // 对于普通的多级路径
                    else if (pathParts.length > 2) {
                        // 去掉文件名，保留目录结构
                        const dirParts = pathParts.slice(0, -1);
                        smartThumbnailPath = dirParts.join('/') + '/' + fileName;
                        debug(`普通路径保持目录结构: ${originalPath} -> ${smartThumbnailPath}`);
                    } else if (pathParts.length === 2) {
                        // 只有一级目录
                        const dirName = pathParts[0];
                        if (dirName !== fileName) {
                            smartThumbnailPath = dirName + '/' + fileName;
                            debug(`单级目录路径: ${originalPath} -> ${smartThumbnailPath}`);
                        }
                    } else {
                        smartThumbnailPath = originalPath;
                        debug(`保持原路径: ${originalPath}`);
                    }
                } else {
                    smartThumbnailPath = originalPath;
                }
                
                debug(`最终缩略图路径: ${smartThumbnailPath}`);
            }
            
            // 构建备选路径数组，用于缩略图系统尝试
            const alternatePaths = [];
            
            // 1. 智能简化后的路径（最高优先级）
            alternatePaths.push(smartThumbnailPath);
            
            // 2. 直接文件名
            alternatePaths.push(fileName);
            
            // 3. 原始文件信息路径（作为备选）
            if (file.path && file.path !== smartThumbnailPath) alternatePaths.push(file.path);
            if (file.fullPath && file.fullPath !== smartThumbnailPath) alternatePaths.push(file.fullPath);
            
            // 4. 基于逻辑路径的构建
            if (filePath !== smartThumbnailPath && filePath !== fileName) {
                alternatePaths.push(filePath);
            }
            
            // 5. 如果有parent路径信息，尝试不同的组合
            if (parentPath) {
                const parentBasedPath = `${parentPath}/${fileName}`;
                if (!alternatePaths.includes(parentBasedPath)) {
                    alternatePaths.push(parentBasedPath);
                }
            }
            
            // 去重备选路径
            const uniqueAlternatePaths = [...new Set(alternatePaths)];
            
            debug(`文件 ${fileName} 缩略图路径策略:`, {
                原始路径: originalPath,
                智能简化路径: smartThumbnailPath,
                备选路径: uniqueAlternatePaths
            });
            
            html += `
                <div class="tree-item tree-file" 
                     data-level="${level}" 
                     data-file-path="${filePath}"
                     data-file-name="${fileName}"
                     data-file-type="${fileType}"
                     data-file-size="${file.size || 0}"
                     data-file-original-path="${file.path || ''}"
                     data-file-full-path="${file.fullPath || ''}">
                    
                    ${fileType === 'image' || fileType === 'video' ? `
                        <div class="thumbnail-container" 
                             data-thumbnail-path="${smartThumbnailPath}"
                             data-thumbnail-name="${fileName}"
                             data-thumbnail-alternates='${JSON.stringify(uniqueAlternatePaths)}'
                             data-thumbnail-options='{"size":"small","quality":"medium"}'>
                            <div class="thumbnail-placeholder">
                                <span>${getFileIcon(extension)}</span>
                            </div>
                        </div>
                    ` : `
                        <div class="file-icon">${getFileIcon(extension)}</div>
                    `}
                    
                    <span class="file-name" title="${fileName}">${fileName}</span>
                    
                    <!-- 上传状态指示器 -->
                    <div class="upload-status-indicator" data-status="${isUploaded ? 'uploaded' : 'pending'}">
                        <span class="status-icon">${isUploaded ? '✅' : '⏳'}</span>
                        <span class="status-text">${isUploaded ? '已上传' : '等待中'}</span>
                    </div>
                    
                    <span class="file-size">${formatFileSize(file.size)}</span>
                    
                    <div class="file-actions">
                        ${hasPreview ? '<button class="action-btn preview-btn" title="预览"><span>👁️</span></button>' : ''}
                        ${!isUploaded ? '<button class="action-btn upload-btn" title="上传"><span>⬆️</span></button>' : '<button class="action-btn revert-btn" title="标记为未上传"><span>↩️</span></button>'}
                    </div>
                </div>
            `;
        });
    }
    
    return html;
}

// 统计文件夹中的文件数量
function countFilesInFolder(folder) {
    if (!folder) return 0;
    
    let count = 0;
    
    if (folder.files && Array.isArray(folder.files)) {
        count += folder.files.length;
    }
    
    if (folder.folders && Array.isArray(folder.folders)) {
        folder.folders.forEach(subfolder => {
            count += countFilesInFolder(subfolder.children);
        });
    }
    
    return count;
}

// 绑定树状结构事件
function bindTreeEvents(container) {
    // 移除现有的事件监听器（如果有的话）
    const existingHandler = container._treeEventHandler;
    if (existingHandler) {
        container.removeEventListener('click', existingHandler);
    }
    
    // 创建新的事件处理器
    const eventHandler = async (e) => {
        // 文件夹折叠/展开事件
        if (e.target.classList.contains('folder-toggle')) {
            e.preventDefault();
            e.stopPropagation();
            
            const toggle = e.target;
            const folderId = toggle.getAttribute('data-folder-id');
            const folderContent = document.getElementById(folderId);
            const folderPath = toggle.closest('.tree-folder').getAttribute('data-folder-path');
            
            if (folderContent) {
                const isExpanded = toggle.classList.contains('expanded');
                
                if (isExpanded) {
                    // 折叠
                    folderContent.style.display = 'none';
                    toggle.classList.remove('expanded');
                    toggle.textContent = '▶';
                    folderStates[folderPath] = false;
        } else { 
                    // 展开
                    folderContent.style.display = 'block';
                    toggle.classList.add('expanded');
                    toggle.textContent = '▼';
                    folderStates[folderPath] = true;
                }
                
                saveFolderStates();
            }
            return;
        }
        
        // 预览按钮事件
        if (e.target.closest('.preview-btn')) {
            e.preventDefault();
            e.stopPropagation();
            
            const fileItem = e.target.closest('.tree-file');
            const filePath = fileItem.getAttribute('data-file-path');
            const fileName = fileItem.getAttribute('data-file-name');
            const fileType = fileItem.getAttribute('data-file-type');
            const fileSize = parseInt(fileItem.getAttribute('data-file-size')) || 0;
            
            // 准备文件列表（同类型文件）
            const sameTypeFiles = Array.from(container.querySelectorAll(`[data-file-type="${fileType}"]`))
                .map(item => ({
                    path: item.getAttribute('data-file-path'),
                    name: item.getAttribute('data-file-name'),
                    type: item.getAttribute('data-file-type'),
                    size: parseInt(item.getAttribute('data-file-size')) || 0
                }));
            
            // 找到当前文件的索引
            const currentIndex = sameTypeFiles.findIndex(file => file.path === filePath);
            
            // 打开预览模态框
            if (previewModal) {
                await previewModal.open(sameTypeFiles, Math.max(0, currentIndex));
            }
            return;
        }
        
        // 上传按钮事件 - 添加防重复点击保护
        if (e.target.closest('.upload-btn')) {
            e.preventDefault();
            e.stopPropagation();
            
            const uploadBtn = e.target.closest('.upload-btn');
            
            // 检查是否已在处理中
            if (uploadBtn.dataset.uploading === 'true') {
                debug('上传按钮重复点击被阻止');
                return;
            }
            
            // 设置上传状态
            uploadBtn.dataset.uploading = 'true';
            uploadBtn.style.opacity = '0.6';
            uploadBtn.style.pointerEvents = 'none';
            
            // 获取文件信息 - 移到try块内部确保作用域正确
            const fileItem = e.target.closest('.tree-file');
            const filePath = fileItem.getAttribute('data-file-path');
            const fileName = fileItem.getAttribute('data-file-name');
            const fileSize = parseInt(fileItem.getAttribute('data-file-size')) || 0;
            const fileType = getFileExtension(fileName);
            
            try {
                debug(`单个文件上传请求: ${fileName}`);
                
                // 直接通过WebSocket通知后端状态更新（无状态架构）
                chrome.runtime.sendMessage({
                    type: 'UPDATE_FILE_STATUS',
                    filename: fileName,
                    status: 'uploading'
                });
                
                // 创建单文件上传数组
                const singleFile = [{
                    name: fileName,
                    path: filePath,
                    size: fileSize,
                    type: fileType
                }];
                
                // 使用智能云盘页面管理
                showStatus('检查云盘页面...', 'info');
                
                // 执行异步云盘页面管理和上传
                (async () => {
                    try {
                        const yunpanResult = await ensureYunpanPageReady();
                        
                        if (!yunpanResult.success) {
                            showStatus(`云盘页面不可用: ${yunpanResult.message}`, 'error');
                            // 通知服务器上传失败
                            chrome.runtime.sendMessage({
                                type: 'UPDATE_FILE_STATUS',
                                filename: fileName,
                                status: '同步失败'
                            });
                            return;
                        }
                        
                        // 直接启动云盘上传
                        showStatus(`正在上传文件: ${fileName}`, 'info');
                        await startYunpanUpload(singleFile);
                        
                    } catch (error) {
                        debug(`单个文件上传失败: ${error.message}`);
                        showStatus(`上传失败: ${error.message}`, 'error');
                        // 通知服务器上传失败
                        chrome.runtime.sendMessage({
                            type: 'UPDATE_FILE_STATUS',
                            filename: fileName,
                            status: '同步失败'
                        });
                    } finally {
                        // 恢复按钮状态
                        setTimeout(() => {
                            uploadBtn.dataset.uploading = 'false';
                            uploadBtn.style.opacity = '';
                            uploadBtn.style.pointerEvents = '';
                        }, 2000);
                    }
                })();
                
            } catch (error) {
                debug(`单个文件上传初始化失败: ${error.message}`);
                showStatus(`上传初始化失败: ${error.message}`, 'error');
                // 通知服务器上传失败
                chrome.runtime.sendMessage({
                    type: 'UPDATE_FILE_STATUS',
                    filename: fileName,
                    status: '同步失败'
                });
                
                // 恢复按钮状态
                setTimeout(() => {
                    uploadBtn.dataset.uploading = 'false';
                    uploadBtn.style.opacity = '';
                    uploadBtn.style.pointerEvents = '';
                }, 2000);
            }
            return;
        }
        
        // 撤销按钮事件 - 标记为未上传（无状态架构版本）
        if (e.target.closest('.revert-btn')) {
            e.preventDefault();
            e.stopPropagation();
            
            const fileItem = e.target.closest('.tree-file');
            const filePath = fileItem.getAttribute('data-file-path');
            const fileName = fileItem.getAttribute('data-file-name');
            
            debug(`撤销上传状态: ${fileName}`);
            
            // 仅通过WebSocket通知后端状态变更（无状态架构）
            chrome.runtime.sendMessage({
                type: 'UPDATE_FILE_STATUS',
                filename: fileName,
                status: '未同步' // 使用服务器端的状态值
            }, (response) => {
                if (response && response.success) {
                    debug(`服务器状态更新成功: ${fileName} -> 未同步`);
                    showStatus(`已将 "${fileName}" 标记为未上传`, 'info');
                    
                    // 立即刷新显示以反映状态变化
                    requestFileTree();
                    setTimeout(() => {
                        renderTreeForCurrentTab();
                        updateFileCounts();
                    }, 500);
                } else {
                    debug(`服务器状态更新失败: ${response?.error || '未知错误'}`);
                    showStatus(`状态更新失败: ${response?.error || '未知错误'}`, 'error');
                }
            });
            
            return;
        }
        
        // 缩略图点击事件
        if (e.target.closest('.thumbnail-container')) {
            const fileItem = e.target.closest('.tree-file');
            if (fileItem) {
                const previewBtn = fileItem.querySelector('.preview-btn');
                if (previewBtn) {
                    previewBtn.click();
                }
            }
            return;
        }
    };
    
    // 绑定新的事件处理器
    container.addEventListener('click', eventHandler);
    
    // 保存事件处理器引用以便后续清理
    container._treeEventHandler = eventHandler;
}

// 恢复文件夹状态
function restoreFolderStates(container) {
    Object.entries(folderStates).forEach(([folderPath, isExpanded]) => {
        const folderItem = container.querySelector(`[data-folder-path="${folderPath}"]`);
        if (folderItem && isExpanded) {
            const toggle = folderItem.querySelector('.folder-toggle');
            if (toggle && !toggle.classList.contains('expanded')) {
                toggle.click();
            }
        }
    });
}

// 显示空状态
function showEmptyState(message, container = null) {
    const emptyHTML = `
        <div class="empty-state">
            <div class="empty-icon">📁</div>
            <h3>暂无文件</h3>
            <p>${message}</p>
        </div>
    `;
    
    if (container) {
        container.innerHTML = emptyHTML;
    } else {
        // 显示在当前活跃的标签页中
        const activeContainer = document.getElementById(activeTab + 'TreeContainer');
        if (activeContainer) {
            activeContainer.innerHTML = emptyHTML;
        }
    }
}

// 统计树中的所有文件
function countAllFiles(tree) {
    if (!tree) return 0;
    
    let count = 0;
    
    if (tree.files && Array.isArray(tree.files)) {
        count += tree.files.length;
    }
    
    if (tree.folders && Array.isArray(tree.folders)) {
        tree.folders.forEach(folder => {
            count += countAllFiles(folder.children);
        });
    }
    
    return count;
}

// 更新文件计数
function updateFileCounts() {
    if (!currentAdminTree) return;
    
    // 计算待上传文件数量
    const pendingTree = filterTreeByUploadStatus(currentAdminTree, false);
    const pendingCount = countAllFiles(pendingTree);
    
    // 计算已上传文件数量
    const uploadedTree = filterTreeByUploadStatus(currentAdminTree, true);
    const uploadedCount = countAllFiles(uploadedTree);
    
    // 更新标签页计数
    const pendingCountEl = document.getElementById('pendingCount');
    const uploadedCountEl = document.getElementById('uploadedCount');
    
    if (pendingCountEl) pendingCountEl.textContent = pendingCount;
    if (uploadedCountEl) uploadedCountEl.textContent = uploadedCount;
    
    // 更新状态信息
    showStatus(`待上传: ${pendingCount}，已上传: ${uploadedCount}`, 'info');
    
    // 智能的自动上传触发检查 - 修复：移除uploadInProgress阻塞检查
    if (autoUploadEnabled) {
        const hasSignificantChange = Math.abs(pendingCount - lastFileCount) > 0;
        const hasNewFiles = pendingCount > lastFileCount;
        const queueStatus = getUploadQueueStatus();
        
        // 新的触发条件：有文件且文件数量有变化，不再检查uploadInProgress
        const shouldConsiderAutoUpload = hasSignificantChange && pendingCount > 0;
        
        debug(`文件计数更新检查`, {
            pendingCount,
            lastFileCount,
            hasSignificantChange,
            hasNewFiles,
            shouldConsiderAutoUpload,
            autoUploadEnabled,
            queueStatus: {
                queueLength: queueStatus.queueLength,
                activeTasksCount: queueStatus.activeTasksCount,
                isProcessing: queueStatus.isProcessing
            }
        });
        
        // 只要有显著变化且有待上传文件就触发检查，队列系统会处理排队
        if (shouldConsiderAutoUpload) {
            debug(`触发自动上传检查: 文件数量从 ${lastFileCount} 变为 ${pendingCount}`);
            
            // 使用适当的延迟，避免在快速文件更新期间重复触发
            setTimeout(() => {
                // 再次检查条件，确保状态仍然有效
                if (autoUploadEnabled) {
                    checkAndTriggerAutoUpload();
                } else {
                    debug('自动上传检查时发现开关已关闭');
                }
            }, 1500); // 1.5秒延迟，给文件树稳定时间
        } else {
            debug('跳过自动上传检查', {
                reason: shouldConsiderAutoUpload ? '无' : 
                        hasSignificantChange ? '无文件' : '无显著变化'
            });
        }
        
        // 更新文件计数记录
        lastFileCount = pendingCount;
    } else {
        debug('自动上传未开启，跳过触发检查');
        // 仍然更新计数器，即使自动上传未开启
        lastFileCount = pendingCount;
    }
}

// 加载已上传文件列表
function loadUploadedFiles() {
    chrome.storage.local.get([UPLOADED_FILES_KEY], (result) => {
        if (result[UPLOADED_FILES_KEY]) {
            uploadedFiles = new Set(result[UPLOADED_FILES_KEY]);
            debug(`加载了 ${uploadedFiles.size} 个已上传文件`);
        }
    });
}

// 保存已上传文件列表
function saveUploadedFiles() {
    const data = { [UPLOADED_FILES_KEY]: Array.from(uploadedFiles) };
    chrome.storage.local.set(data);
    debug(`保存了 ${uploadedFiles.size} 个已上传文件`);
}



// 保存文件夹状态
function saveFolderStates() {
    const data = { [FOLDER_STATES_KEY]: folderStates };
    chrome.storage.local.set(data);
}

// 设置文件上传状态
function setFileUploadState(fileName, status, options = {}) {
    const timestamp = Date.now();
    const existingState = fileUploadStates.get(fileName);
    
    const newState = {
        status: status,
        timestamp: timestamp,
        retryCount: options.retryCount || (existingState?.retryCount || 0),
        details: options.details || null
    };
    
    fileUploadStates.set(fileName, newState);
    
    debug(`设置文件状态: ${fileName} -> ${status}`, {
        timestamp: new Date(timestamp).toLocaleTimeString(),
        retryCount: newState.retryCount
    });
    
    // 在无状态架构中，状态由服务器管理，不需要本地保存
    // 状态更新会通过WebSocket从服务器同步
    // UI会通过其他机制(如重新渲染树状结构)自动更新
}

// 初始化缩略图系统
function initializeThumbnailSystem() {
  // 由于新的结构使用多个容器，我们需要为每个标签页容器初始化缩略图系统
  const initContainer = (containerId) => {
    const container = document.getElementById(containerId);
    if (container) {
      const thumbnailManager = new LazyThumbnail(container, {
        size: 'small',
        quality: 'medium',
        threshold: 0.1,
        rootMargin: '50px'
      });
      return thumbnailManager;
    }
    return null;
  };
  
  // 为两个标签页容器都初始化缩略图系统
  const pendingManager = initContainer('pendingTreeContainer');
  const uploadedManager = initContainer('uploadedTreeContainer');
  
  // 使用其中一个作为主要管理器
  lazyThumbnailManager = pendingManager || uploadedManager;
  
  // 初始化预览模态框
  if (!previewModal) {
    previewModal = new PreviewModal({
      showMetadata: true,
      enableKeyboardNavigation: true,
      preloadNeighbors: true
    });
  }
  
  console.log('缩略图系统已初始化，支持树状结构');
}

// 绑定文件项目事件
function bindFileItemEvents() {
  // 这个函数现在主要用于全局事件绑定
  document.addEventListener('DOMContentLoaded', () => {
    // 监听窗口大小变化，调整缩略图布局
    window.addEventListener('resize', () => {
      if (lazyThumbnailManager) {
        // 重新计算可见区域
        setTimeout(() => {
          lazyThumbnailManager.preloadVisible();
        }, 100);
                }
            });
        });
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (!bytes || bytes === 0) return '0 B';
    const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化日期
function formatDate(dateString) {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now - date);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) {
    return '今天';
  } else if (diffDays === 2) {
    return '昨天';
  } else if (diffDays <= 7) {
    return `${diffDays}天前`;
  } else {
    return date.toLocaleDateString();
  }
}

// 获取文件类型
function getFileType(filename) {
  const extension = filename.split('.').pop().toLowerCase();
  const types = {
    image: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'svg'],
    video: ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm', 'm4v'],
    audio: ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a', 'opus'],
    document: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'odt', 'ods', 'odp'],
    archive: ['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 'lz', 'lzma'],
    code: ['js', 'ts', 'html', 'css', 'php', 'py', 'java', 'cpp', 'c', 'h', 'cs', 'rb', 'go', 'rs', 'swift'],
    text: ['txt', 'md', 'log', 'conf', 'ini', 'cfg', 'yaml', 'yml', 'json', 'xml', 'csv']
  };
  
  for (const [type, extensions] of Object.entries(types)) {
    if (extensions.includes(extension)) {
      return type;
    }
  }
  
  return 'other';
}

// 获取文件图标
function getFileIcon(extension) {
  const icons = {
    // 图片
    jpg: '🖼️', jpeg: '🖼️', png: '🖼️', gif: '🎭', webp: '🖼️', bmp: '🖼️', svg: '🎨',
    
    // 视频
    mp4: '🎬', avi: '🎬', mov: '🎬', mkv: '🎬', wmv: '🎬', flv: '🎬', webm: '🎬',
    
    // 音频
    mp3: '🎵', wav: '🎵', flac: '🎵', aac: '🎵', ogg: '🎵', m4a: '🎵',
    
    // 文档
    pdf: '📄', doc: '📝', docx: '📝', xls: '📊', xlsx: '📊', ppt: '📎', pptx: '📎',
    
    // 压缩
    zip: '🗜️', rar: '🗜️', '7z': '🗜️', tar: '📦', gz: '📦',
    
    // 代码
    js: '📜', ts: '📜', html: '🌐', css: '🎨', php: '🐘', py: '🐍', java: '☕',
    
    // 文本
    txt: '📝', md: '📋', json: '🔧', xml: '🔧', csv: '📊',
    
    // 默认
    default: '📁'
  };
  
  return icons[extension] || icons.default;
}

// 更新连接状态显示
function updateConnectionStatus(isConnected) {
    const statusDot = document.getElementById('connectionStatusDot');
    const statusText = document.getElementById('connectionStatusText');
    
    if (statusDot && statusText) {
        if (isConnected) {
            statusDot.className = 'connection-status-dot connected';
            statusText.textContent = '已连接到服务器';
        } else {
            statusDot.className = 'connection-status-dot disconnected';
            statusText.textContent = '未连接到服务器';
        }
    }
}

// 显示状态信息
function showStatus(message, type = 'info') {
    const statusInfo = document.getElementById('fileCountInfo');
    if (statusInfo) {
        statusInfo.textContent = message;
        statusInfo.className = 'status-info '; // Base class
        if(type === 'success') statusInfo.classList.add('text-green-500');
        else if (type === 'error') statusInfo.classList.add('text-red-500');
        else statusInfo.classList.add('text-blue-500');
    }
}

// 显示加载状态
function showLoadingState() {
    const activeContainer = document.getElementById(activeTab + 'TreeContainer');
    const loadingState = activeContainer?.querySelector('.loading-state');
    if (loadingState) {
        loadingState.style.display = 'block';
    }
}

// 隐藏加载状态
function hideLoadingState() {
    const activeContainer = document.getElementById(activeTab + 'TreeContainer');
    const loadingState = activeContainer?.querySelector('.loading-state');
    if (loadingState) {
        loadingState.style.display = 'none';
    }
}

// 获取文件扩展名
function getFileExtension(filename) {
    if (!filename) return '';
    const parts = filename.split('.');
    return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
}

// popup定期更新机制
let popupUpdateInterval = null;

function startPopupPeriodicUpdate() {
    // 防止重复启动
    if (popupUpdateInterval) {
        clearInterval(popupUpdateInterval);
    }
    
    popupUpdateInterval = setInterval(() => {
        debug('popup定期更新：请求最新文件树');
        
        // 通过WebSocket请求最新数据
            chrome.runtime.sendMessage({ 
            type: 'SEND_WS_MESSAGE',
            wsMessage: {
                type: 'get_full_admin_file_tree',
                forceRefresh: true,
                source: 'popup_periodic',
                timestamp: Date.now()
            }
            }, (response) => {
            if (response && response.success) {
                debug('popup定期更新：WebSocket请求已发送');
                } else {
                debug('popup定期更新：WebSocket请求失败，使用备用方案');
                // WebSocket失败时，直接请求本地存储的数据
                requestFileTree();
            }
        });
        
    }, 20000); // 20秒更新一次
    
    debug('popup定期更新已启动 (每20秒)');
}

function stopPopupPeriodicUpdate() {
    if (popupUpdateInterval) {
        clearInterval(popupUpdateInterval);
        popupUpdateInterval = null;
        debug('popup定期更新已停止');
    }
}

// 页面卸载时清理定时器
window.addEventListener('beforeunload', () => {
    stopPopupPeriodicUpdate();
});

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initialize);

// 导出主要函数供其他模块使用
window.popupApp = {
    switchTab,
    renderTreeForCurrentTab,
    updateFileCounts,
    showStatus,
    debug
};

// 云盘上传功能 - 初始化上传按钮
function initUploadButton() {
    const uploadButton = document.getElementById('uploadAll');
    if (uploadButton) {
        updateUploadButtonState();
    }
}

// 处理上传按钮点击
async function handleUploadClick() {
    debug('上传按钮被点击');
    
    // 获取上传按钮并设置状态
    const uploadButton = document.getElementById('uploadAll');
    if (uploadButton) {
        uploadButton.disabled = true;
        uploadButton.textContent = '准备中...';
    }
    
    try {
        // 获取未上传的文件列表
        debug('获取未上传文件列表');
        const unuploadedFiles = getUnuploadedFiles();
        if (unuploadedFiles.length === 0) {
            showStatus('没有需要上传的文件', 'info');
            return;
        }
        
        debug(`准备上传 ${unuploadedFiles.length} 个文件`, unuploadedFiles);
        
        // 使用队列系统：创建并排队上传任务
        const task = createUploadTask(unuploadedFiles, { 
            source: 'manual',
            maxRetries: 3 
        });
        
        const taskId = enqueueUploadTask(task);
        
        // 获取队列状态并显示给用户
        const queueStatus = getUploadQueueStatus();
        if (queueStatus.queuedTasks > 1) {
            showStatus(`上传任务已加入队列 (位置: ${queueStatus.queuedTasks})，当前 ${queueStatus.activeTasksCount} 个任务处理中`, 'info');
        } else if (queueStatus.isProcessing) {
            showStatus(`上传任务已加入队列，当前有任务正在处理中...`, 'info');
        } else {
            showStatus(`开始处理上传任务 #${taskId}...`, 'info');
        }
        
    } catch (error) {
        const errorMsg = `创建上传任务失败: ${error.message || error || '未知错误'}`;
        debug(errorMsg, { 
            error: error,
            stack: error?.stack
        });
        showStatus(errorMsg, 'error');
    } finally {
        // 恢复按钮状态
        if (uploadButton) {
            uploadButton.disabled = false;
            // updateUploadButtonState() 会在processUploadQueue中调用
        }
    }
}

// 处理自动上传
async function handleAutoUpload(files) {
    if (!autoUploadEnabled) {
        debug('自动上传未开启，跳过任务创建');
        return;
    }
    
    debug(`自动上传：创建任务处理 ${files.length} 个文件`);
    
    try {
        // 使用队列系统：创建自动上传任务
        const task = createUploadTask(files, { 
            source: 'auto',
            maxRetries: 2 
        });
        
        const taskId = enqueueUploadTask(task);
        
        // 获取队列状态
        const queueStatus = getUploadQueueStatus();
        
        if (queueStatus.queuedTasks > 1) {
            debug('自动上传任务已排队，当前队列位置:', queueStatus.queuedTasks);
            showStatus(`自动上传已排队 (位置: ${queueStatus.queuedTasks})`, 'info');
        } else if (queueStatus.isProcessing) {
            debug('自动上传任务已排队，等待当前任务完成');
            showStatus('自动上传已排队，等待当前任务完成...', 'info');
        } else {
            debug('自动上传任务立即开始处理');
            showStatus(`自动上传开始：任务 #${taskId}`, 'info');
        }
        
    } catch (error) {
        debug('自动上传任务创建失败:', error);
        showStatus(`自动上传失败: ${error.message}`, 'error');
    }
}

// 确保云盘页面就绪（新增智能页面管理函数）
async function ensureYunpanPageReady() {
    debug('开始智能云盘页面管理流程');
    
    // 第一步：检查当前云盘页面状态
    let yunpanStatus = await checkYunpanPageAvailability();
    debug('初始云盘页面检查:', yunpanStatus);
    
    if (yunpanStatus.available) {
        debug('云盘页面已可用，直接使用');
        return { success: true, message: '云盘页面已就绪', tabId: yunpanStatus.tabId };
    }
    
    // 第二步：页面不可用，尝试智能处理
    debug('云盘页面不可用，开始智能处理', { reason: yunpanStatus.message });
    
    // 检查是否是脚本未就绪但页面已打开
    if (yunpanStatus.details?.tabId && yunpanStatus.details?.chromeError) {
        debug('云盘页面已打开但脚本未就绪，尝试激活页面');
        showStatus('云盘页面已打开，正在激活并等待就绪...', 'info');
        
        try {
            // 激活云盘标签页
            await chrome.tabs.update(yunpanStatus.details.tabId, { active: true });
            
            // 等待脚本就绪
            const waitResult = await waitForYunpanScriptReady(yunpanStatus.details.tabId, 15000);
            if (waitResult.success) {
                return { success: true, message: '云盘页面已激活并就绪', tabId: yunpanStatus.details.tabId };
            } else {
                debug('激活后仍无法就绪，尝试刷新页面');
                await chrome.tabs.reload(yunpanStatus.details.tabId);
                
                const reloadWaitResult = await waitForYunpanScriptReady(yunpanStatus.details.tabId, 20000);
                if (reloadWaitResult.success) {
                    return { success: true, message: '云盘页面已刷新并就绪', tabId: yunpanStatus.details.tabId };
                }
            }
        } catch (error) {
            debug('激活云盘页面失败:', error);
        }
    }
    
    // 第三步：需要打开新的云盘页面
    debug('需要打开新的云盘页面');
    showStatus('正在打开云盘页面...', 'info');
    
    try {
        const newTab = await chrome.tabs.create({
            url: 'https://yunpan.gdcourts.gov.cn/',
            active: true
        });
        
        debug(`新云盘页面已打开: ${newTab.id}`);
        showStatus('云盘页面已打开，正在等待加载完成...', 'info');
        
        // 等待新页面加载并脚本就绪
        const waitResult = await waitForYunpanScriptReady(newTab.id, 30000);
        
        if (waitResult.success) {
            return { success: true, message: '新云盘页面已就绪', tabId: newTab.id };
        } else {
            return { 
                success: false, 
                message: '云盘页面打开后脚本加载失败', 
                details: waitResult.details 
            };
        }
        
    } catch (error) {
        debug('打开云盘页面失败:', error);
        return { 
            success: false, 
            message: `打开云盘页面失败: ${error.message}`,
            details: { error: error.message }
        };
    }
}

// 等待云盘脚本就绪（新增辅助函数）
async function waitForYunpanScriptReady(tabId, timeoutMs = 20000) {
    debug(`等待云盘脚本就绪, 标签页ID: ${tabId}, 超时: ${timeoutMs}ms`);
    
    const startTime = Date.now();
    const checkInterval = 1000; // 每秒检查一次
    
    while (Date.now() - startTime < timeoutMs) {
        try {
            const response = await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('单次检查超时'));
                }, 5000);
                
                chrome.tabs.sendMessage(tabId, { type: 'PING' }, (response) => {
                    clearTimeout(timeout);
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });
            
            if (response) {
                debug('云盘脚本已就绪');
                return { success: true, response };
            }
        } catch (error) {
            debug(`脚本就绪检查失败: ${error.message}`);
            // 继续循环，不立即失败
        }
        
        // 等待下次检查
        await new Promise(resolve => setTimeout(resolve, checkInterval));
        showStatus(`等待云盘页面加载... (${Math.round((Date.now() - startTime) / 1000)}s)`, 'info');
    }
    
    debug('等待云盘脚本就绪超时');
    return { 
        success: false, 
        message: '云盘脚本加载超时',
        details: { timeoutMs, tabId }
    };
}

// 获取未上传的文件列表
function getUnuploadedFiles() {
    const unuploadedFiles = [];
    
    debug('开始获取未上传文件列表，当前文件树:', currentAdminTree);
    
    // 从当前文件树中获取未上传的文件
    if (currentAdminTree) {
        const extractFiles = (node, currentPath = '') => {
            // 检查文件
            if (node.files && Array.isArray(node.files)) {
                node.files.forEach(file => {
                    const filePath = currentPath ? `${currentPath}/${file.name}` : file.name;
                    if (!isFileUploaded(file)) {
                        unuploadedFiles.push({
                            name: file.name,
                            path: filePath,
                            size: file.size || 0,
                            type: file.type || 'file'
                        });
                        debug(`找到未上传文件: ${file.name} (路径: ${filePath})`);
                    }
                });
            }
            
            // 递归处理文件夹
            if (node.folders && Array.isArray(node.folders)) {
                node.folders.forEach(folder => {
                    const newPath = currentPath ? `${currentPath}/${folder.name}` : folder.name;
                    if (folder.children) {
                        extractFiles(folder.children, newPath);
                    }
                });
            }
        };
        
        // 开始从根节点提取文件
        extractFiles(currentAdminTree);
                    } else {
        debug('当前文件树为空，无法获取文件列表');
    }
    
    debug(`总共找到 ${unuploadedFiles.length} 个未上传文件`, unuploadedFiles);
    return unuploadedFiles;
}

// 检查云盘页面可用性
function checkYunpanPageAvailability() {
    return new Promise((resolve) => {
        debug('发送CHECK_YUNPAN_PAGE请求');
            chrome.runtime.sendMessage({ 
            type: 'CHECK_YUNPAN_PAGE'
            }, (response) => {
                if (chrome.runtime.lastError) {
                debug('检查云盘页面失败 - Chrome错误:', chrome.runtime.lastError);
                resolve({
                    available: false,
                    message: '无法与后台服务通信',
                    details: {
                        chromeError: chrome.runtime.lastError.message,
                        suggestion: '请重新加载扩展'
                    }
                });
            } else if (!response) {
                debug('检查云盘页面失败 - 无响应');
                resolve({
                    available: false,
                    message: '后台服务没有响应',
                    details: {
                        suggestion: '请重新加载扩展或重启浏览器'
                    }
                });
                } else {
                debug('云盘页面检查完成:', response);
                resolve(response);
                }
            });
        });
}

// 开始云盘上传
async function startYunpanUpload(files) {
    setUploadInProgress(true, 'startYunpanUpload');
    updateUploadButtonState();
    
    try {
        showStatus(`开始上传 ${files.length} 个文件到云盘...`, 'info');
        debug('发送上传请求到后台', { filesCount: files.length, files: files });
        
        // 通过WebSocket通知服务器所有文件状态设置为上传中（无状态架构）
        files.forEach(file => {
            chrome.runtime.sendMessage({
                type: 'UPDATE_FILE_STATUS',
                filename: file.name,
                status: 'uploading'
            });
        });
        
        // 添加上传进度提示
        let progressMessageInterval;
        let progressCounter = 0;
        const progressMessages = [
            '正在连接云盘...',
            '正在处理文件...',
            '正在创建目录结构...',
            '正在上传文件内容...',
            '正在验证上传结果...'
        ];
        
        // 开始显示进度消息
        showStatus(progressMessages[0], 'info');
        progressMessageInterval = setInterval(() => {
            progressCounter = (progressCounter + 1) % progressMessages.length;
            showStatus(progressMessages[progressCounter], 'info');
        }, 30000); // 每30秒更新一次进度消息
        
        const response = await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('上传请求超时（5分钟）- 对于大文件或多级目录可能需要更长时间，请检查云盘页面确认上传状态'));
            }, 300000); // 300秒 = 5分钟超时，适应大文件和复杂目录结构上传
            
            chrome.runtime.sendMessage({
                type: 'START_YUNPAN_UPLOAD',
                data: {
                    files: files
                }
            }, (response) => {
                clearTimeout(timeout);
                
                if (chrome.runtime.lastError) {
                    reject(new Error(`后台通信失败: ${chrome.runtime.lastError.message}`));
                } else if (!response) {
                    reject(new Error('后台服务没有响应'));
                } else {
                    resolve(response);
                }
            });
        });
        
        // 清理进度提示
        if (progressMessageInterval) {
            clearInterval(progressMessageInterval);
        }
        
        debug('收到上传响应:', response);
        
        // 改进响应处理逻辑 - 支持更多状态
        if (response.success === true) {
            // 标准成功响应格式
            const stats = response.result || {
                total: files.length,
                completed: 0,
                failed: files.length
            };
            
            debug('上传成功:', stats);
            
            showStatus(
                `上传完成！成功: ${stats.completed}, 失败: ${stats.failed}, 总计: ${stats.total}`,
                stats.failed > 0 ? 'warning' : 'success'
            );
            
            // 如果有成功上传的文件，通知服务器更新状态（无状态架构）
            if (stats.completed > 0) {
                // 通过WebSocket通知服务器文件已上传
                files.forEach(file => {
                    chrome.runtime.sendMessage({
                        type: 'UPDATE_FILE_STATUS',
                        filename: file.name,
                        status: '已同步'
                    });
                });
                
                // 刷新文件树显示
                renderTreeForCurrentTab();
                updateFileCounts();
                
                // 延迟刷新文件树数据，确保服务器状态已更新
                setTimeout(() => {
                    requestFileTree();
                }, 2000);
            }
        } else if (response.status === 'processing' || response.processing === true) {
            // content script返回的处理中状态 - 改进处理
            debug('收到处理中状态，启动智能等待逻辑');
            showStatus('云盘正在处理上传，请耐心等待...', 'info');
            
            // 对于处理中状态，给用户更明确的指导
            setTimeout(() => {
                showStatus('上传可能仍在进行中，请检查云盘页面或稍后刷新文件列表', 'warning');
                
                // 启动后台状态检查（可选）
                setTimeout(() => {
                    debug('自动刷新文件树，检查上传状态');
                    requestFileTree();
                    renderTreeForCurrentTab();
                }, 10000); // 10秒后自动刷新检查
            }, 3000);
            
        } else if (response.partial === true) {
            // 部分成功的情况
            const stats = response.result || {};
            debug('部分上传成功:', stats);
            
            showStatus(
                `部分上传完成。成功: ${stats.completed || 0}, 失败: ${stats.failed || 0}`,
                'warning'
            );
            
            // 通知服务器成功上传的文件状态（无状态架构）
            if (stats.completed > 0) {
                files.forEach(file => {
                    chrome.runtime.sendMessage({
                        type: 'UPDATE_FILE_STATUS',
                        filename: file.name,
                        status: '已同步'
                    });
                });
                
                renderTreeForCurrentTab();
                updateFileCounts();
                setTimeout(() => requestFileTree(), 2000);
            }
        } else {
            // 处理上传失败
            const errorMsg = response.error || '上传失败，未知原因';
            const errorDetails = response.details;
            
            debug('上传失败:', { error: errorMsg, details: errorDetails });
            
            // 构建详细错误信息
            let fullErrorMsg = errorMsg;
            if (errorDetails?.suggestion) {
                fullErrorMsg += `\n建议: ${errorDetails.suggestion}`;
            }
            
            // 对于通信错误，提供额外的诊断信息
            if (errorDetails?.communicationError || errorDetails?.contentScriptError) {
                fullErrorMsg += '\n\n故障排除：\n1. 刷新云盘页面\n2. 重新加载扩展\n3. 检查网络连接';
            }
            
            throw new Error(fullErrorMsg);
        }
        
    } catch (error) {
        debug('上传过程中发生错误:', error);
        
        let errorMsg = '上传失败';
        if (error.message) {
            errorMsg += `: ${error.message}`;
        }
        
        showStatus(errorMsg, 'error');
        throw error; // 重新抛出错误，让上层处理
    } finally {
        setUploadInProgress(false, 'startYunpanUpload-finally');
        updateUploadButtonState();
    }
}

// 检查云盘页面状态
async function checkYunpanPageStatus() {
    try {
        const status = await checkYunpanPageAvailability();
        updateYunpanStatusIndicator(status);
    } catch (error) {
        debug('检查云盘状态失败:', error);
        updateYunpanStatusIndicator({ available: false, message: '检查失败' });
    }
}

// 更新云盘状态指示器
function updateYunpanStatusIndicator(status) {
    let indicator = document.getElementById('yunpan-status');
    
    if (!indicator) {
        // 创建状态指示器
        indicator = document.createElement('div');
        indicator.id = 'yunpan-status';
        indicator.className = 'yunpan-status';
        
        // 插入到上传按钮附近
        const uploadButton = document.getElementById('uploadAll');
        if (uploadButton && uploadButton.parentNode) {
            uploadButton.parentNode.insertBefore(indicator, uploadButton.nextSibling);
        }
    }
    
    indicator.className = `yunpan-status ${status.available ? 'connected' : 'disconnected'}`;
    indicator.textContent = status.available ? '云盘已连接' : '云盘未连接';
    indicator.title = status.message;
}

// 更新上传按钮状态
function updateUploadButtonState() {
    const uploadAllBtn = document.getElementById('uploadAll');
    if (!uploadAllBtn) return;
    
    const unuploadedFiles = getUnuploadedFiles();
    
    if (unuploadedFiles.length > 0) {
        uploadAllBtn.textContent = `上传 ${unuploadedFiles.length} 个文件`;
        uploadAllBtn.disabled = false;
        uploadAllBtn.classList.remove('disabled');
    } else {
        uploadAllBtn.textContent = '无文件待上传';
        uploadAllBtn.disabled = true;
        uploadAllBtn.classList.add('disabled');
    }
}

// 初始化自动上传开关
function initAutoUploadToggle() {
    debug('初始化自动上传开关');
    
    const autoUploadToggle = document.getElementById('autoUploadToggle');
    if (!autoUploadToggle) {
        debug('未找到自动上传开关元素');
        return;
    }
    
    // 从background获取自动上传状态
    chrome.runtime.sendMessage({ type: 'GET_AUTO_UPLOAD_STATUS' }, (response) => {
        if (response && response.success) {
            autoUploadEnabled = response.enabled;
            autoUploadToggle.checked = autoUploadEnabled;
            debug(`从后台获取自动上传状态: ${autoUploadEnabled}`);
        } else {
            // 如果无法从后台获取，尝试从本地存储获取
            chrome.storage.local.get(['autoUploadEnabled'], (result) => {
                autoUploadEnabled = result.autoUploadEnabled || false;
                autoUploadToggle.checked = autoUploadEnabled;
                debug(`从本地存储获取自动上传状态: ${autoUploadEnabled}`);
            });
        }
    });
    
    // 绑定开关变化事件
    autoUploadToggle.addEventListener('change', (e) => {
        const newState = e.target.checked;
        debug(`自动上传开关状态变化: ${autoUploadEnabled} -> ${newState}`);
        
        autoUploadEnabled = newState;
        
        // 保存到本地存储
        chrome.storage.local.set({ autoUploadEnabled: newState });
        
        // 通知background更新后台监控系统
        chrome.runtime.sendMessage({ 
            type: 'UPDATE_AUTO_UPLOAD_CONFIG',
            enabled: newState
        }, (response) => {
            if (response && response.success) {
                debug('后台自动上传配置更新成功');
                showStatus(
                    newState ? '自动上传已开启（后台监控）' : '自动上传已关闭',
                    newState ? 'success' : 'info'
                );
            } else {
                debug('后台自动上传配置更新失败');
                showStatus('自动上传设置更新失败', 'error');
            }
        });
        
        debug(`自动上传状态已更新: ${newState}`);
    });
}

// 检查并触发自动上传
function checkAndTriggerAutoUpload() {
    debug('开始自动上传检查', {
        autoUploadEnabled,
        queueStatus: getUploadQueueStatus(),
        uploadStartTime: uploadStartTime ? new Date(uploadStartTime).toLocaleTimeString() : '无'
    });
    
    if (!autoUploadEnabled) {
        debug('自动上传未开启，跳过检查');
        return;
    }
    
    // 获取未上传文件
    const unuploadedFiles = getUnuploadedFiles();
    const currentFileCount = unuploadedFiles.length;
    
    debug('自动上传文件检查', {
        currentFileCount,
        lastFileCount,
        hasNewFiles: currentFileCount > lastFileCount,
        hasAnyFiles: currentFileCount > 0,
        isFirstLoad: lastFileCount === 0,
        files: unuploadedFiles.map(f => f.name).slice(0, 5) // 只显示前5个文件名
    });
    
    // 改进的自动上传触发逻辑 - 移除uploadInProgress阻塞
    let shouldTriggerUpload = false;
    let triggerReason = '';
    
    if (currentFileCount === 0) {
        // 没有待上传文件
        debug('没有待上传文件，重置计数器');
        lastFileCount = 0;
        return;
    } else if (lastFileCount === 0 && currentFileCount > 0) {
        // 首次检测到待上传文件（从无到有）
        debug(`首次检测到 ${currentFileCount} 个待上传文件，触发自动上传`);
        shouldTriggerUpload = true;
        triggerReason = `首次检测到 ${currentFileCount} 个待上传文件`;
    } else if (currentFileCount > lastFileCount) {
        // 文件数量增加（检测到新文件）
        const newFileCount = currentFileCount - lastFileCount;
        debug(`检测到 ${newFileCount} 个新文件 (${lastFileCount} -> ${currentFileCount})，触发自动上传`);
        shouldTriggerUpload = true;
        triggerReason = `检测到 ${newFileCount} 个新文件`;
    } else if (currentFileCount < lastFileCount) {
        // 文件数量减少（可能有文件被删除或手动上传）
        debug(`文件数量减少 (${lastFileCount} -> ${currentFileCount})，更新计数器但不触发自动上传`);
        lastFileCount = currentFileCount;
        return;
    } else {
        // 文件数量无变化，但检查是否有"僵尸文件"（长时间未上传的文件）
        const currentTime = Date.now();
        const timeSinceLastUpdate = currentTime - (window.lastUploadCheckTime || 0);
        
        // 如果超过5分钟没有文件状态变化，且有待上传文件，考虑重新触发
        if (timeSinceLastUpdate > 300000 && currentFileCount > 0) { // 5分钟 = 300000ms
            debug('检测到长时间无变化的待上传文件，可能需要重新触发');
            shouldTriggerUpload = true;
            triggerReason = `检测到 ${currentFileCount} 个长时间未上传的文件`;
            window.lastUploadCheckTime = currentTime;
        } else {
            debug('文件数量无变化，不触发自动上传');
            return;
        }
    }
    
    if (shouldTriggerUpload) {
        debug(`自动上传触发: ${triggerReason}`);
        showStatus(`自动上传触发：${triggerReason}`, 'info');
        
        // 延迟1秒后创建自动上传任务（减少延迟，提高响应性）
        setTimeout(async () => {
            // 再次检查自动上传是否仍然开启
            if (!autoUploadEnabled) {
                debug('自动上传已被关闭，取消此次任务');
                lastFileCount = currentFileCount; // 更新计数器
                return;
            }
            
            try {
                debug('开始创建自动上传任务');
                
                // 重新获取最新的文件列表（可能在延迟期间有变化）
                const latestUnuploadedFiles = getUnuploadedFiles();
                if (latestUnuploadedFiles.length === 0) {
                    debug('延迟期间文件已被处理，取消自动上传');
                    lastFileCount = 0;
                    return;
                }
                
                await handleAutoUpload(latestUnuploadedFiles);
                
                // 成功创建任务后更新计数器
                lastFileCount = 0; // 重置为0，任务会处理这些文件
            } catch (error) {
                debug('自动上传任务创建失败:', error);
                showStatus(`自动上传任务创建失败: ${error.message}`, 'error');
                // 失败后保持当前计数，下次检查时可能会重试
                lastFileCount = currentFileCount;
            }
        }, 1000); // 减少延迟到1秒，提高响应性
    }
    
    // 如果没有触发上传，更新文件数量记录
    if (!shouldTriggerUpload) {
        lastFileCount = currentFileCount;
        // 更新最后检查时间
        window.lastUploadCheckTime = Date.now();
    }
}

// 设置上传进行状态的安全函数
function setUploadInProgress(inProgress, source = 'unknown') {
    const previousState = uploadInProgress;
    uploadInProgress = inProgress;
    
    if (inProgress) {
        uploadStartTime = Date.now();
        debug(`上传状态设置为进行中 (来源: ${source}), 开始时间: ${new Date(uploadStartTime).toLocaleTimeString()}`);
    } else {
        const duration = uploadStartTime ? Math.round((Date.now() - uploadStartTime) / 1000) : 0;
        debug(`上传状态设置为已结束 (来源: ${source}), 持续时间: ${duration}秒`);
        uploadStartTime = 0;
    }
    
    if (previousState !== inProgress) {
        updateUploadButtonState();
    }
    
    return previousState;
} 

// 实时获取文件状态 - 直接从当前文件树数据获取
function getFileCurrentStatus(fileName) {
    if (!currentAdminTree) return '未知';
    
    // 递归搜索文件树找到对应文件
    function findFileInTree(tree, targetFileName) {
        if (!tree) return null;
        
        // 搜索文件
        if (tree.files) {
            const found = tree.files.find(f => 
                f.name === targetFileName || 
                f.filename === targetFileName ||
                f.path === targetFileName
            );
            if (found) return found;
        }
        
        // 递归搜索子文件夹
        if (tree.folders) {
            for (const folder of tree.folders) {
                const result = findFileInTree(folder.children, targetFileName);
                if (result) return result;
            }
        }
        
        return null;
    }
    
    const file = findFileInTree(currentAdminTree, fileName);
    const status = file ? (file.syncStatus || file.status || '未同步') : '未知';
    
    debug(`获取文件实时状态: ${fileName} -> ${status}`);
    return status;
}

// === 上传队列管理函数 ===

// 创建上传任务
function createUploadTask(files, options = {}) {
    const taskId = ++uploadTaskCounter;
    const task = {
        id: taskId,
        files: Array.isArray(files) ? [...files] : [files],
        options: {
            source: options.source || 'unknown',
            maxRetries: options.maxRetries || 3,
            priority: options.priority || 'normal',
            ...options
        },
        status: 'created',
        createdAt: Date.now(),
        progress: {
            completed: 0,
            failed: 0,
            total: Array.isArray(files) ? files.length : 1
        },
        attempts: 0
    };
    
    debug(`创建上传任务 #${taskId}`, {
        filesCount: task.files.length,
        source: task.options.source,
        maxRetries: task.options.maxRetries
    });
    
    return task;
}

// 将任务加入队列
function enqueueUploadTask(task) {
    if (!task || !task.id) {
        throw new Error('无效的上传任务');
    }
    
    task.status = 'queued';
    task.queuedAt = Date.now();
    
    uploadQueue.push(task);
    debug(`任务 #${task.id} 已加入队列，当前队列长度: ${uploadQueue.length}`);
    
    // 立即尝试处理队列
    processUploadQueue();
    
    return task.id;
}

// 处理上传队列
async function processUploadQueue() {
    // 检查是否有空闲容量
    if (activeUploadTasks.size >= maxConcurrentUploads) {
        debug(`上传队列处理: 达到最大并发数 (${maxConcurrentUploads})，等待中...`);
        return;
    }
    
    // 从队列中取出下一个任务
    const nextTask = uploadQueue.shift();
    if (!nextTask) {
        debug('上传队列处理: 队列为空');
        return;
    }
    
    debug(`开始处理上传任务 #${nextTask.id}`, {
        filesCount: nextTask.files.length,
        source: nextTask.options.source
    });
    
    // 将任务移到活跃列表
    nextTask.status = 'processing';
    nextTask.startedAt = Date.now();
    activeUploadTasks.set(nextTask.id, nextTask);
    
    // 更新全局状态（兼容性）
    setUploadInProgress(true, `queue-task-${nextTask.id}`);
    
    try {
        // 执行上传
        const result = await executeUploadTask(nextTask);
        
        // 标记任务完成
        nextTask.status = result.success ? 'completed' : 'failed';
        nextTask.completedAt = Date.now();
        nextTask.result = result;
        
        debug(`上传任务 #${nextTask.id} 已完成`, {
            success: result.success,
            duration: Math.round((nextTask.completedAt - nextTask.startedAt) / 1000) + 's'
        });
        
    } catch (error) {
        debug(`上传任务 #${nextTask.id} 执行失败:`, error);
        nextTask.status = 'failed';
        nextTask.completedAt = Date.now();
        nextTask.error = error.message;
        
        // 检查是否需要重试
        if (nextTask.attempts < nextTask.options.maxRetries) {
            nextTask.attempts++;
            nextTask.status = 'retry';
            
            debug(`上传任务 #${nextTask.id} 准备重试 (${nextTask.attempts}/${nextTask.options.maxRetries})`);
            
            // 重新加入队列
            setTimeout(() => {
                nextTask.status = 'queued';
                uploadQueue.unshift(nextTask); // 优先处理重试任务
                processUploadQueue();
            }, 5000); // 5秒后重试
        }
    } finally {
        // 从活跃列表移除
        activeUploadTasks.delete(nextTask.id);
        
        // 更新全局状态
        if (activeUploadTasks.size === 0) {
            setUploadInProgress(false, `queue-task-${nextTask.id}-complete`);
        }
        
        // 继续处理队列中的下一个任务
        if (uploadQueue.length > 0) {
            setTimeout(() => processUploadQueue(), 1000);
        }
    }
}

// 执行单个上传任务
async function executeUploadTask(task) {
    debug(`执行上传任务 #${task.id}，文件数: ${task.files.length}`);
    
    try {
        // 使用现有的云盘上传流程
        await startYunpanUpload(task.files);
        
        return {
            success: true,
            completed: task.files.length,
            failed: 0,
            total: task.files.length
        };
        
    } catch (error) {
        debug(`上传任务 #${task.id} 失败:`, error);
        return {
            success: false,
            completed: 0,
            failed: task.files.length,
            total: task.files.length,
            error: error.message
        };
    }
}

// 获取队列状态
function getUploadQueueStatus() {
    return {
        queueLength: uploadQueue.length,
        queuedTasks: uploadQueue.length,
        activeTasksCount: activeUploadTasks.size,
        maxConcurrent: maxConcurrentUploads,
        isProcessing: activeUploadTasks.size > 0,
        totalTasksCreated: uploadTaskCounter,
        activeTasks: Array.from(activeUploadTasks.values()).map(task => ({
            id: task.id,
            status: task.status,
            filesCount: task.files.length,
            source: task.options.source
        }))
    };
}

// 强制重置上传状态
function forceResetUploadState() {
    debug('强制重置所有上传状态');
    
    uploadQueue.length = 0;
    activeUploadTasks.clear();
    uploadInProgress = false;
    uploadStartTime = 0;
    uploadTaskCounter = 0;
    
    debug('上传状态已重置');
    return true;
}

// 临时空函数，保持兼容性（无状态架构中不再需要本地跟踪上传文件）
function addUploadedFile(fileName) {
    // 在无状态架构中，文件状态由服务器管理
    // 这个函数保持为空，只是为了兼容性
    debug(`addUploadedFile调用(已废弃): ${fileName}`);
}

// 加载文件夹状态
function loadFolderStates() {
    chrome.storage.local.get([FOLDER_STATES_KEY], (result) => {
        if (result[FOLDER_STATES_KEY]) {
            folderStates = result[FOLDER_STATES_KEY];
            debug('加载了文件夹状态');
        }
    });
}

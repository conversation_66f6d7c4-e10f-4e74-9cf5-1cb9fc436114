import thumbnailCache from '../utils/thumbnail-cache.js';

class LazyThumbnail {
  constructor(container, options = {}) {
    this.container = container;
    this.options = {
      size: 'medium',
      quality: 'medium',
      format: 'jpeg',
      threshold: 0.1, // 进入视窗多少比例开始加载
      rootMargin: '50px', // 提前加载的边距
      loadingPlaceholder: this.createLoadingPlaceholder(),
      errorPlaceholder: this.createErrorPlaceholder(),
      ...options
    };
    
    this.observer = null;
    this.loadingSet = new Set(); // 正在加载的文件
    this.loadedSet = new Set(); // 已加载的文件
    this.cache = new Map(); // 组件内缓存
    
    this.init();
  }

  init() {
    // 初始化Intersection Observer
    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.loadThumbnail(entry.target);
        }
      });
    }, {
      threshold: this.options.threshold,
      rootMargin: this.options.rootMargin
    });

    // 监听容器内现有的缩略图元素
    this.observeExistingThumbnails();
  }

  // 观察现有的缩略图元素
  observeExistingThumbnails() {
    const thumbnailElements = this.container.querySelectorAll('[data-thumbnail-path]');
    console.log(`[LazyThumbnail] 发现 ${thumbnailElements.length} 个缩略图元素待处理`);
    
    thumbnailElements.forEach(el => {
      const filePath = el.getAttribute('data-thumbnail-path');
      
      // 检查是否已经处理过
      if (!this.loadedSet.has(filePath)) {
        this.observer.observe(el);
      } else {
        console.log(`[LazyThumbnail] 跳过已处理的缩略图: ${filePath}`);
      }
    });
  }

  // 更新容器并重新扫描缩略图元素
  updateContainer(newContainer) {
    console.log('[LazyThumbnail] 更新容器，重新初始化缩略图系统');
    
    if (newContainer && newContainer !== this.container) {
      console.log('[LazyThumbnail] 容器已更新，从', this.container, '到', newContainer);
      // 更新容器引用
      this.container = newContainer;
    }
    
    // 1. 清理现有的观察状态
    if (this.observer) {
      console.log('[LazyThumbnail] 清理现有观察器状态');
      this.observer.disconnect(); // 断开所有观察
      this.loadingSet.clear(); // 清理加载状态
      // 注意：不清理loadedSet，保留已加载状态以避免重复加载
    }
    
    // 2. 重新初始化观察器
    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.loadThumbnail(entry.target);
        }
      });
    }, {
      threshold: this.options.threshold,
      rootMargin: this.options.rootMargin
    });
    
    // 3. 重新扫描并观察新的缩略图元素
    this.observeExistingThumbnails();
    
    // 4. 主动应用已有缓存
    this.applyCachedThumbnails();
    
    console.log('[LazyThumbnail] 容器更新完成，观察器已重新初始化');
  }

  // 直接加载所有缩略图 - 移除缓存应用逻辑
  async applyCachedThumbnails() {
    console.log('[LazyThumbnail] 开始直接从服务器加载所有缩略图');
    
    const thumbnailElements = this.container.querySelectorAll('[data-thumbnail-path]');
    console.log(`[LazyThumbnail] 发现 ${thumbnailElements.length} 个缩略图元素需要加载`);
    
    // 直接触发所有缩略图的加载，不检查缓存
    for (const element of thumbnailElements) {
      const filePath = element.getAttribute('data-thumbnail-path');
      
      if (filePath && !this.loadingSet.has(filePath)) {
        // 直接从服务器加载
        this.loadThumbnail(element);
      }
    }
    
    console.log('[LazyThumbnail] 已触发所有缩略图的服务器加载');
  }

  // 创建缩略图元素
  createThumbnailElement(filePath, options = {}) {
    const element = document.createElement('div');
    element.className = 'lazy-thumbnail';
    element.setAttribute('data-thumbnail-path', filePath);
    
    // 合并选项
    const finalOptions = { ...this.options, ...options };
    element.setAttribute('data-thumbnail-options', JSON.stringify(finalOptions));

    // 设置初始状态
    element.innerHTML = this.options.loadingPlaceholder;
    element.classList.add('loading');

    // 开始观察
    this.observer.observe(element);

    return element;
  }

  // 加载缩略图 - 简化版，直接从服务器获取
  async loadThumbnail(element) {
    const filePath = element.getAttribute('data-thumbnail-path');
    const optionsStr = element.getAttribute('data-thumbnail-options');
    
    if (!filePath || this.loadingSet.has(filePath)) {
      return;
    }

    this.loadingSet.add(filePath);

    try {
      const options = optionsStr ? JSON.parse(optionsStr) : this.options;
      
      console.log(`[LazyThumbnail] 直接从服务器获取缩略图: ${filePath}`);
      
      // 直接从服务器生成缩略图，不检查缓存
      const thumbnailData = await thumbnailCache.generateAndCacheThumbnail(filePath, options);

      if (thumbnailData) {
        // 处理不同格式的缩略图数据
        let imageData;
        if (typeof thumbnailData === 'string') {
          // 直接是 base64 字符串
          imageData = thumbnailData;
        } else if (thumbnailData.data) {
          // 包含 data 属性的对象
          imageData = thumbnailData.data.base64 || thumbnailData.data;
        } else {
          // 其他格式
          imageData = null;
        }

        if (imageData) {
          // 成功加载
          this.renderThumbnail(element, imageData, filePath);
          this.loadedSet.add(filePath);
          
          // 触发加载完成事件
          this.dispatchLoadEvent(element, 'load', { filePath, thumbnailData: imageData });
        } else {
          // 数据格式不正确
          this.renderError(element, filePath);
          this.dispatchLoadEvent(element, 'error', { filePath, error: '缩略图数据格式错误' });
        }
      } else {
        // 加载失败
        this.renderError(element, filePath);
        this.dispatchLoadEvent(element, 'error', { filePath, error: '缩略图生成失败' });
      }
    } catch (error) {
      console.error('缩略图加载失败:', error);
      this.renderError(element, filePath);
      this.dispatchLoadEvent(element, 'error', { filePath, error });
    } finally {
      this.loadingSet.delete(filePath);
      this.observer.unobserve(element); // 停止观察已处理的元素
    }
  }

  // 渲染缩略图
  renderThumbnail(element, imageData, filePath) {
    const img = document.createElement('img');
    img.src = imageData; // imageData 现在是 base64 字符串
    img.alt = `缩略图: ${filePath.split('/').pop()}`;
    img.className = 'thumbnail-image';
    
    // 添加加载动画
    img.style.opacity = '0';
    img.style.transition = 'opacity 0.3s ease';
    
    img.onload = () => {
      element.innerHTML = '';
      element.appendChild(img);
      element.classList.remove('loading');
      element.classList.add('loaded');
      
      // 淡入动画
      requestAnimationFrame(() => {
        img.style.opacity = '1';
      });
    };

    img.onerror = () => {
      this.renderError(element, filePath);
    };

    // 暂时移除元数据显示，因为现在只有 base64 数据
    // 如果需要显示元数据，可以从其他地方获取或者修改缓存结构
  }

  // 渲染错误状态
  renderError(element, filePath) {
    element.innerHTML = this.options.errorPlaceholder;
    element.classList.remove('loading');
    element.classList.add('error');
    
    // 添加重试按钮
    const retryBtn = element.querySelector('.retry-button');
    if (retryBtn) {
      retryBtn.onclick = () => {
        element.classList.remove('error');
        element.classList.add('loading');
        element.innerHTML = this.options.loadingPlaceholder;
        this.loadedSet.delete(filePath);
        this.observer.observe(element);
      };
    }
  }

  // 创建加载占位符
  createLoadingPlaceholder() {
    return `
      <div class="thumbnail-loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中...</div>
      </div>
    `;
  }

  // 创建错误占位符
  createErrorPlaceholder() {
    return `
      <div class="thumbnail-error">
        <div class="error-icon">⚠️</div>
        <div class="error-text">加载失败</div>
        <button class="retry-button">重试</button>
      </div>
    `;
  }

  // 触发自定义事件
  dispatchLoadEvent(element, type, detail) {
    const event = new CustomEvent(`thumbnail-${type}`, {
      detail: detail,
      bubbles: true
    });
    element.dispatchEvent(event);
  }

  // 预加载可见区域的缩略图
  async preloadVisible() {
    console.log('[LazyThumbnail] 开始预加载可见缩略图');
    
    const thumbnailElements = this.container.querySelectorAll('[data-thumbnail-path]');
    const promises = [];
    
    thumbnailElements.forEach(element => {
      // 检查元素是否在视窗内
      const rect = element.getBoundingClientRect();
      const isVisible = (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
      );
      
      if (isVisible) {
        const filePath = element.getAttribute('data-thumbnail-path');
        
        // 先尝试从缓存快速加载
        if (!this.loadedSet.has(filePath) && !this.loadingSet.has(filePath)) {
          promises.push(this.quickLoadFromCache(element));
        }
      }
    });
    
    if (promises.length > 0) {
      console.log(`[LazyThumbnail] 预加载 ${promises.length} 个可见缩略图`);
      await Promise.allSettled(promises);
    }
  }

  // 快速从服务器加载缩略图 - 移除缓存检查
  async quickLoadFromCache(element) {
    const filePath = element.getAttribute('data-thumbnail-path');
    
    if (!filePath) return false;
    
    console.log('[LazyThumbnail] 直接从服务器快速加载:', filePath);
    
    // 直接调用loadThumbnail，不做缓存检查
    await this.loadThumbnail(element);
    return true;
  }

  // 批量添加缩略图
  addThumbnails(fileList, parentElement = this.container) {
    const fragment = document.createDocumentFragment();
    
    fileList.forEach(fileInfo => {
      const thumbnailElement = this.createThumbnailElement(
        fileInfo.path, 
        fileInfo.options || {}
      );
      
      // 添加文件信息
      if (fileInfo.name) {
        thumbnailElement.setAttribute('data-file-name', fileInfo.name);
      }
      if (fileInfo.size) {
        thumbnailElement.setAttribute('data-file-size', fileInfo.size);
      }
      
      fragment.appendChild(thumbnailElement);
    });
    
    parentElement.appendChild(fragment);
    return fragment;
  }

  // 移除缩略图
  removeThumbnail(filePath) {
    const elements = this.container.querySelectorAll(`[data-thumbnail-path="${filePath}"]`);
    elements.forEach(el => {
      this.observer.unobserve(el);
      el.remove();
    });
    
    this.loadingSet.delete(filePath);
    this.loadedSet.delete(filePath);
    this.cache.delete(filePath);
  }

  // 清理所有缩略图
  clear() {
    const elements = this.container.querySelectorAll('[data-thumbnail-path]');
    elements.forEach(el => {
      this.observer.unobserve(el);
      el.remove();
    });
    
    this.loadingSet.clear();
    this.loadedSet.clear();
    this.cache.clear();
  }

  // 刷新缩略图
  async refresh(filePath = null) {
    if (filePath) {
      // 刷新单个文件
      this.loadedSet.delete(filePath);
      await thumbnailCache.deleteFromIndexedDB(
        thumbnailCache.generateCacheKey(filePath, this.options)
      );
      
      const elements = this.container.querySelectorAll(`[data-thumbnail-path="${filePath}"]`);
      elements.forEach(el => {
        el.classList.remove('loaded', 'error');
        el.classList.add('loading');
        el.innerHTML = this.options.loadingPlaceholder;
        this.observer.observe(el);
      });
    } else {
      // 刷新所有缩略图
      await thumbnailCache.clearAllCache();
      this.loadedSet.clear();
      this.cache.clear();
      
      const elements = this.container.querySelectorAll('[data-thumbnail-path]');
      elements.forEach(el => {
        el.classList.remove('loaded', 'error');
        el.classList.add('loading');
        el.innerHTML = this.options.loadingPlaceholder;
        this.observer.observe(el);
      });
    }
  }

  // 获取统计信息
  getStats() {
    return {
      total: this.container.querySelectorAll('[data-thumbnail-path]').length,
      loaded: this.loadedSet.size,
      loading: this.loadingSet.size,
      cached: this.cache.size
    };
  }

  // 设置缩略图大小
  setSize(size) {
    this.options.size = size;
    
    // 更新所有元素的选项
    const elements = this.container.querySelectorAll('[data-thumbnail-path]');
    elements.forEach(el => {
      const options = JSON.parse(el.getAttribute('data-thumbnail-options') || '{}');
      options.size = size;
      el.setAttribute('data-thumbnail-options', JSON.stringify(options));
    });
  }

  // 销毁实例
  destroy() {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    
    this.clear();
    this.container = null;
  }
}

// 创建CSS样式
const style = document.createElement('style');
style.textContent = `
  .lazy-thumbnail {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    background: var(--card-bg);
    border: 1px solid var(--border-light);
    transition: all 0.3s ease;
  }

  .lazy-thumbnail:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .thumbnail-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 150px;
    color: var(--text-secondary);
  }

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: thumbnail-spin 1s linear infinite;
    margin-bottom: 8px;
  }

  @keyframes thumbnail-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .loading-text {
    font-size: 12px;
    opacity: 0.7;
  }

  .thumbnail-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 150px;
    color: var(--error-color);
  }

  .error-icon {
    font-size: 24px;
    margin-bottom: 8px;
  }

  .error-text {
    font-size: 12px;
    margin-bottom: 8px;
  }

  .retry-button {
    padding: 4px 8px;
    font-size: 11px;
    border: 1px solid var(--error-color);
    background: transparent;
    color: var(--error-color);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .retry-button:hover {
    background: var(--error-color);
    color: white;
  }

  .thumbnail-metadata {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    font-size: 10px;
    padding: 8px 6px 4px;
    text-align: center;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .lazy-thumbnail:hover .thumbnail-metadata {
    opacity: 1;
  }

  .lazy-thumbnail.loading {
    background: var(--skeleton-bg);
  }

  .lazy-thumbnail.error {
    background: var(--error-bg);
    border-color: var(--error-color);
  }

  .lazy-thumbnail.loaded {
    background: transparent;
  }
`;

if (!document.head.querySelector('#lazy-thumbnail-styles')) {
  style.id = 'lazy-thumbnail-styles';
  document.head.appendChild(style);
}

export default LazyThumbnail; 
import thumbnailCache from '../utils/thumbnail-cache.js';

class PreviewModal {
  constructor(options = {}) {
    this.options = {
      maxWidth: '90vw',
      maxHeight: '90vh',
      backgroundColor: 'rgba(0, 0, 0, 0.9)',
      showMetadata: true,
      enableKeyboardNavigation: true,
      enableGestures: true,
      preloadNeighbors: true,
      ...options
    };

    this.isOpen = false;
    this.currentIndex = 0;
    this.fileList = [];
    this.modal = null;
    this.content = null;
    this.metadata = null;
    
    this.gestureState = {
      isZooming: false,
      isPanning: false,
      scale: 1,
      translateX: 0,
      translateY: 0,
      lastPinchDistance: 0
    };

    this.init();
  }

  init() {
    this.createModal();
    this.bindEvents();
  }

  createModal() {
    // 创建模态框容器
    this.modal = document.createElement('div');
    this.modal.className = 'preview-modal';
    this.modal.innerHTML = `
      <div class="preview-backdrop"></div>
      <div class="preview-container">
        <div class="preview-header">
          <div class="preview-title"></div>
          <div class="preview-controls">
            <button class="preview-btn zoom-out" title="缩小">-</button>
            <button class="preview-btn zoom-reset" title="重置">1:1</button>
            <button class="preview-btn zoom-in" title="放大">+</button>
            <button class="preview-btn fullscreen" title="全屏">⛶</button>
            <button class="preview-btn close" title="关闭">×</button>
          </div>
        </div>
        
        <div class="preview-content-wrapper">
          <button class="preview-nav prev" title="上一个">‹</button>
          <div class="preview-content"></div>
          <button class="preview-nav next" title="下一个">›</button>
        </div>
        
        <div class="preview-footer">
          <div class="preview-metadata"></div>
          <div class="preview-pagination">
            <span class="current-index">1</span> / <span class="total-count">1</span>
          </div>
        </div>
      </div>
    `;

    // 获取关键元素
    this.content = this.modal.querySelector('.preview-content');
    this.metadata = this.modal.querySelector('.preview-metadata');
    this.title = this.modal.querySelector('.preview-title');
    this.pagination = {
      current: this.modal.querySelector('.current-index'),
      total: this.modal.querySelector('.total-count')
    };

    document.body.appendChild(this.modal);
  }

  bindEvents() {
    // 关闭事件
    this.modal.querySelector('.preview-backdrop').addEventListener('click', () => this.close());
    this.modal.querySelector('.close').addEventListener('click', () => this.close());

    // 导航事件
    this.modal.querySelector('.prev').addEventListener('click', () => this.previous());
    this.modal.querySelector('.next').addEventListener('click', () => this.next());

    // 缩放事件
    this.modal.querySelector('.zoom-in').addEventListener('click', () => this.zoom(1.2));
    this.modal.querySelector('.zoom-out').addEventListener('click', () => this.zoom(0.8));
    this.modal.querySelector('.zoom-reset').addEventListener('click', () => this.resetZoom());

    // 全屏事件
    this.modal.querySelector('.fullscreen').addEventListener('click', () => this.toggleFullscreen());

    // 键盘导航
    if (this.options.enableKeyboardNavigation) {
      document.addEventListener('keydown', (e) => this.handleKeyboard(e));
    }

    // 手势支持
    if (this.options.enableGestures) {
      this.bindGestureEvents();
    }

    // 鼠标滚轮缩放
    this.content.addEventListener('wheel', (e) => this.handleWheel(e));
  }

  bindGestureEvents() {
    let hammer;
    
    // 动态加载Hammer.js（如果可用）
    if (typeof Hammer !== 'undefined') {
      hammer = new Hammer(this.content);
      hammer.get('pinch').set({ enable: true });
      hammer.get('pan').set({ direction: Hammer.DIRECTION_ALL });

      hammer.on('pinchstart', (e) => {
        this.gestureState.isZooming = true;
        this.gestureState.lastPinchDistance = e.distance;
      });

      hammer.on('pinchmove', (e) => {
        if (this.gestureState.isZooming) {
          const scaleFactor = e.distance / this.gestureState.lastPinchDistance;
          this.zoom(scaleFactor);
          this.gestureState.lastPinchDistance = e.distance;
        }
      });

      hammer.on('pinchend', () => {
        this.gestureState.isZooming = false;
      });

      hammer.on('panstart', () => {
        this.gestureState.isPanning = true;
      });

      hammer.on('panmove', (e) => {
        if (this.gestureState.isPanning && this.gestureState.scale > 1) {
          this.gestureState.translateX += e.deltaX;
          this.gestureState.translateY += e.deltaY;
          this.updateTransform();
        }
      });

      hammer.on('panend', () => {
        this.gestureState.isPanning = false;
      });
    }

    // 触摸事件备选方案
    let touchStartDistance = 0;
    
    this.content.addEventListener('touchstart', (e) => {
      if (e.touches.length === 2) {
        touchStartDistance = this.getTouchDistance(e.touches);
        this.gestureState.isZooming = true;
      }
    });

    this.content.addEventListener('touchmove', (e) => {
      if (e.touches.length === 2 && this.gestureState.isZooming) {
        e.preventDefault();
        const currentDistance = this.getTouchDistance(e.touches);
        const scaleFactor = currentDistance / touchStartDistance;
        this.zoom(scaleFactor);
        touchStartDistance = currentDistance;
      }
    });

    this.content.addEventListener('touchend', () => {
      this.gestureState.isZooming = false;
    });
  }

  getTouchDistance(touches) {
    const dx = touches[0].clientX - touches[1].clientX;
    const dy = touches[0].clientY - touches[1].clientY;
    return Math.sqrt(dx * dx + dy * dy);
  }

  async open(fileList, index = 0) {
    this.fileList = Array.isArray(fileList) ? fileList : [fileList];
    this.currentIndex = Math.max(0, Math.min(index, this.fileList.length - 1));
    
    this.modal.classList.add('active');
    this.isOpen = true;
    document.body.style.overflow = 'hidden';
    
    this.updatePagination();
    await this.loadCurrentFile();
    
    // 预加载相邻文件
    if (this.options.preloadNeighbors) {
      this.preloadNeighbors();
    }
  }

  close() {
    this.modal.classList.remove('active');
    this.isOpen = false;
    document.body.style.overflow = '';
    this.resetZoom();
    
    // 清理内容
    this.content.innerHTML = '';
    this.metadata.innerHTML = '';
  }

  async loadCurrentFile() {
    if (!this.fileList[this.currentIndex]) return;
    
    const file = this.fileList[this.currentIndex];
    this.title.textContent = file.name || file.path.split('/').pop();
    
    // 显示加载状态
    this.content.innerHTML = `
      <div class="preview-loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中...</div>
      </div>
    `;

    try {
      const fileType = this.getFileType(file.path);
      
      switch (fileType) {
        case 'image':
          await this.loadImage(file);
          break;
        case 'video':
          await this.loadVideo(file);
          break;
        case 'audio':
          await this.loadAudio(file);
          break;
        case 'document':
          await this.loadDocument(file);
          break;
        case 'text':
          await this.loadText(file);
          break;
        default:
          await this.loadGeneric(file);
      }

      // 加载元数据
      if (this.options.showMetadata) {
        await this.loadMetadata(file);
      }

    } catch (error) {
      console.error('文件加载失败:', error);
      this.showError('文件加载失败: ' + error.message);
    }
  }

  async loadImage(file) {
    // 先尝试从缓存获取高质量缩略图
    let thumbnailData = await thumbnailCache.getThumbnail(file.path, { 
      size: 'large', 
      quality: 'high' 
    });
    
    if (!thumbnailData) {
      thumbnailData = await thumbnailCache.generateAndCacheThumbnail(file.path, { 
        size: 'large', 
        quality: 'high' 
      });
    }

    const img = document.createElement('img');
    img.className = 'preview-image';
    
    if (thumbnailData) {
      // 处理不同格式的缩略图数据
      if (typeof thumbnailData === 'string') {
        // 直接是 base64 字符串
        img.src = thumbnailData;
      } else if (thumbnailData.data) {
        // 包含 data 属性的对象
        img.src = thumbnailData.data.base64 || thumbnailData.data;
      } else {
        // 如果缩略图数据无效，尝试直接加载原图
        img.src = file.path;
      }
    } else {
      // 如果缩略图生成失败，尝试直接加载原图
      img.src = file.path;
    }
    
    img.alt = file.name || '图片预览';
    
    await new Promise((resolve, reject) => {
      img.onload = resolve;
      img.onerror = reject;
    });

    this.content.innerHTML = '';
    this.content.appendChild(img);
  }

  async loadVideo(file) {
    const video = document.createElement('video');
    video.className = 'preview-video';
    video.controls = true;
    video.preload = 'metadata';
    video.src = file.path;

    this.content.innerHTML = '';
    this.content.appendChild(video);
  }

  async loadAudio(file) {
    const audio = document.createElement('audio');
    audio.className = 'preview-audio';
    audio.controls = true;
    audio.preload = 'metadata';
    audio.src = file.path;

    // 创建音频可视化界面
    const audioContainer = document.createElement('div');
    audioContainer.className = 'preview-audio-container';
    audioContainer.innerHTML = `
      <div class="audio-artwork">
        <div class="audio-icon">🎵</div>
      </div>
      <div class="audio-info">
        <div class="audio-title">${file.name || '音频文件'}</div>
        <div class="audio-controls"></div>
      </div>
    `;

    audioContainer.querySelector('.audio-controls').appendChild(audio);

    this.content.innerHTML = '';
    this.content.appendChild(audioContainer);
  }

  async loadDocument(file) {
    const extension = file.path.split('.').pop().toLowerCase();
    
    if (extension === 'pdf') {
      // PDF预览
      const iframe = document.createElement('iframe');
      iframe.className = 'preview-document';
      iframe.src = file.path;
      
      this.content.innerHTML = '';
      this.content.appendChild(iframe);
    } else {
      // 其他文档类型显示信息
      this.content.innerHTML = `
        <div class="preview-document-info">
          <div class="document-icon">📄</div>
          <div class="document-details">
            <h3>${file.name || '文档'}</h3>
            <p>文件类型: ${extension.toUpperCase()}</p>
            <p>大小: ${this.formatFileSize(file.size || 0)}</p>
            <button class="download-btn" onclick="window.open('${file.path}', '_blank')">
              下载文件
            </button>
          </div>
        </div>
      `;
    }
  }

  async loadText(file) {
    try {
      const response = await fetch(file.path);
      const text = await response.text();
      
      const pre = document.createElement('pre');
      pre.className = 'preview-text';
      pre.textContent = text;

      this.content.innerHTML = '';
      this.content.appendChild(pre);
    } catch (error) {
      this.showError('文本文件加载失败');
    }
  }

  async loadGeneric(file) {
    const extension = file.path.split('.').pop().toLowerCase();
    
    this.content.innerHTML = `
      <div class="preview-generic">
        <div class="file-icon">${this.getFileIcon(extension)}</div>
        <div class="file-details">
          <h3>${file.name || '文件'}</h3>
          <p>文件类型: ${extension.toUpperCase()}</p>
          <p>大小: ${this.formatFileSize(file.size || 0)}</p>
          <button class="download-btn" onclick="window.open('${file.path}', '_blank')">
            打开文件
          </button>
        </div>
      </div>
    `;
  }

  async loadMetadata(file) {
    // 暂时禁用元数据加载，因为服务端 API 不可用
    // 可以根据文件信息显示基本元数据
    try {
      const basicMetadata = {
        size: file.size,
        format: file.path.split('.').pop().toUpperCase(),
        mtime: file.modified || file.modifiedAt
      };
      
      this.displayMetadata(basicMetadata);
    } catch (error) {
      console.warn('显示基本元数据失败:', error);
    }
  }

  displayMetadata(metadata) {
    const metadataHTML = [];
    
    if (metadata.dimensions) {
      metadataHTML.push(`尺寸: ${metadata.dimensions.width} × ${metadata.dimensions.height}`);
    }
    
    if (metadata.size) {
      metadataHTML.push(`大小: ${this.formatFileSize(metadata.size)}`);
    }
    
    if (metadata.format) {
      metadataHTML.push(`格式: ${metadata.format.toUpperCase()}`);
    }
    
    if (metadata.duration) {
      metadataHTML.push(`时长: ${this.formatDuration(metadata.duration)}`);
    }

    if (metadata.mtime) {
      metadataHTML.push(`修改时间: ${new Date(metadata.mtime).toLocaleString()}`);
    }

    this.metadata.innerHTML = metadataHTML.join(' • ');
  }

  showError(message) {
    this.content.innerHTML = `
      <div class="preview-error">
        <div class="error-icon">⚠️</div>
        <div class="error-message">${message}</div>
      </div>
    `;
  }

  previous() {
    if (this.currentIndex > 0) {
      this.currentIndex--;
      this.updatePagination();
      this.loadCurrentFile();
    }
  }

  next() {
    if (this.currentIndex < this.fileList.length - 1) {
      this.currentIndex++;
      this.updatePagination();
      this.loadCurrentFile();
    }
  }

  updatePagination() {
    this.pagination.current.textContent = this.currentIndex + 1;
    this.pagination.total.textContent = this.fileList.length;
    
    // 更新导航按钮状态
    const prevBtn = this.modal.querySelector('.prev');
    const nextBtn = this.modal.querySelector('.next');
    
    prevBtn.style.display = this.currentIndex > 0 ? 'block' : 'none';
    nextBtn.style.display = this.currentIndex < this.fileList.length - 1 ? 'block' : 'none';
  }

  zoom(factor) {
    this.gestureState.scale = Math.max(0.1, Math.min(5, this.gestureState.scale * factor));
    this.updateTransform();
  }

  resetZoom() {
    this.gestureState.scale = 1;
    this.gestureState.translateX = 0;
    this.gestureState.translateY = 0;
    this.updateTransform();
  }

  updateTransform() {
    const content = this.content.querySelector('img, video, .preview-content > *');
    if (content) {
      content.style.transform = `scale(${this.gestureState.scale}) translate(${this.gestureState.translateX}px, ${this.gestureState.translateY}px)`;
    }
  }

  handleKeyboard(e) {
    if (!this.isOpen) return;
    
    switch (e.key) {
      case 'Escape':
        e.preventDefault();
        this.close();
        break;
      case 'ArrowLeft':
        e.preventDefault();
        this.previous();
        break;
      case 'ArrowRight':
        e.preventDefault();
        this.next();
        break;
      case '+':
      case '=':
        e.preventDefault();
        this.zoom(1.2);
        break;
      case '-':
        e.preventDefault();
        this.zoom(0.8);
        break;
      case '0':
        e.preventDefault();
        this.resetZoom();
        break;
    }
  }

  handleWheel(e) {
    if (e.ctrlKey || e.metaKey) {
      e.preventDefault();
      const factor = e.deltaY > 0 ? 0.9 : 1.1;
      this.zoom(factor);
    }
  }

  toggleFullscreen() {
    if (!document.fullscreenElement) {
      this.modal.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
  }

  preloadNeighbors() {
    const preloadIndices = [];
    
    if (this.currentIndex > 0) {
      preloadIndices.push(this.currentIndex - 1);
    }
    if (this.currentIndex < this.fileList.length - 1) {
      preloadIndices.push(this.currentIndex + 1);
    }

    preloadIndices.forEach(index => {
      const file = this.fileList[index];
      if (this.getFileType(file.path) === 'image') {
        thumbnailCache.generateAndCacheThumbnail(file.path, { 
          size: 'large', 
          quality: 'high' 
        });
      }
    });
  }

  getFileType(filePath) {
    const extension = filePath.split('.').pop().toLowerCase();
    const types = {
      image: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'],
      video: ['mp4', 'avi', 'mov', 'mkv', 'webm'],
      audio: ['mp3', 'wav', 'flac', 'aac', 'ogg'],
      document: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
      text: ['txt', 'md', 'json', 'xml', 'css', 'js', 'html']
    };
    
    for (const [type, extensions] of Object.entries(types)) {
      if (extensions.includes(extension)) {
        return type;
      }
    }
    
    return 'generic';
  }

  getFileIcon(extension) {
    const icons = {
      pdf: '📄', doc: '📝', docx: '📝',
      xls: '📊', xlsx: '📊',
      ppt: '📎', pptx: '📎',
      zip: '🗜️', rar: '🗜️',
      mp3: '🎵', wav: '🎵',
      mp4: '🎬', avi: '🎬'
    };
    return icons[extension] || '📁';
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatDuration(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }

  destroy() {
    if (this.modal) {
      this.modal.remove();
    }
    
    document.removeEventListener('keydown', this.handleKeyboard);
  }
}

// 创建CSS样式
const style = document.createElement('style');
style.textContent = `
  .preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: none;
    font-family: system-ui, -apple-system, sans-serif;
  }

  .preview-modal.active {
    display: block;
  }

  .preview-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
  }

  .preview-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    z-index: 1;
  }

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    color: white;
  }

  .preview-title {
    font-size: 16px;
    font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 50%;
  }

  .preview-controls {
    display: flex;
    gap: 10px;
  }

  .preview-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: background 0.2s ease;
  }

  .preview-btn:hover {
    background: rgba(255, 255, 255, 0.2);
  }

  .preview-content-wrapper {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }

  .preview-content {
    max-width: 90vw;
    max-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .preview-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.6);
    color: white;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 2;
  }

  .preview-nav.prev {
    left: 20px;
  }

  .preview-nav.next {
    right: 20px;
  }

  .preview-nav:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: translateY(-50%) scale(1.1);
  }

  .preview-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
  }

  .preview-video {
    max-width: 100%;
    max-height: 100%;
  }

  .preview-audio-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: white;
    background: rgba(0, 0, 0, 0.5);
    padding: 40px;
    border-radius: 12px;
  }

  .audio-artwork {
    margin-bottom: 20px;
  }

  .audio-icon {
    font-size: 80px;
  }

  .audio-title {
    font-size: 18px;
    margin-bottom: 20px;
    text-align: center;
  }

  .preview-audio {
    width: 300px;
  }

  .preview-document {
    width: 80vw;
    height: 80vh;
    border: none;
    background: white;
    border-radius: 8px;
  }

  .preview-document-info,
  .preview-generic {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: white;
    background: rgba(0, 0, 0, 0.5);
    padding: 40px;
    border-radius: 12px;
    text-align: center;
  }

  .document-icon,
  .file-icon {
    font-size: 80px;
    margin-bottom: 20px;
  }

  .download-btn {
    margin-top: 20px;
    padding: 10px 20px;
    border: 2px solid white;
    background: transparent;
    color: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
  }

  .download-btn:hover {
    background: white;
    color: black;
  }

  .preview-text {
    max-width: 80vw;
    max-height: 80vh;
    background: rgba(255, 255, 255, 0.95);
    color: black;
    padding: 20px;
    border-radius: 8px;
    overflow: auto;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
  }

  .preview-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: white;
    font-size: 16px;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: preview-spin 1s linear infinite;
    margin-bottom: 15px;
  }

  @keyframes preview-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .preview-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #ff6b6b;
    font-size: 16px;
  }

  .error-icon {
    font-size: 48px;
    margin-bottom: 15px;
  }

  .preview-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    color: white;
    font-size: 14px;
  }

  .preview-pagination {
    font-weight: 600;
  }

  @media (max-width: 768px) {
    .preview-nav {
      width: 40px;
      height: 40px;
      font-size: 20px;
    }
    
    .preview-nav.prev {
      left: 10px;
    }
    
    .preview-nav.next {
      right: 10px;
    }
    
    .preview-content {
      max-width: 95vw;
      max-height: 75vh;
    }
  }
`;

if (!document.head.querySelector('#preview-modal-styles')) {
  style.id = 'preview-modal-styles';
  document.head.appendChild(style);
}

export default PreviewModal; 
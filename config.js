// 项目配置相关的变量
let projects = [];
let currentProject = null;
let currentSelectingType = null;

// 调试函数
function debug(message) {
    const timestamp = new Date().toLocaleString();
    console.log(`[${timestamp}] ${message}`);
}

// 项目类
class Project {
    constructor(name = '') {
        this.id = Date.now().toString();
        this.name = name;
        this.condition = {
            type: 'url',
            value: ''
        };
        this.elements = [];
        this.steps = [];
    }
}

// 元素类
class Element {
    constructor(type = 'input', name = '') {
        this.id = Date.now().toString();
        this.type = type;
        this.name = name;
        this.selector = null;
        this.value = '';
    }
}

// 步骤类
class Step {
    constructor(elementId = '', action = 'click', value = '') {
        this.id = Date.now().toString();
        this.elementId = elementId;
        this.action = action;
        this.value = value;
    }
}

// 初始化标签页
function initializeTabs() {
    debug('初始化标签页');
    const monitorTab = document.getElementById('monitorTab');
    const configTab = document.getElementById('configTab');
    const monitorContent = document.getElementById('monitorContent');
    const configContent = document.getElementById('configContent');

    if (!monitorTab || !configTab || !monitorContent || !configContent) {
        debug('错误: 未找到必要的标签页元素');
        return;
    }

    function setActiveTab(tab, content) {
        debug(`切换到标签: ${tab.textContent}`);
        // 移除所有活动状态
        [monitorTab, configTab].forEach(t => {
            t.classList.remove('bg-blue-500', 'text-white');
            t.classList.add('bg-gray-200', 'text-gray-700');
        });
        [monitorContent, configContent].forEach(c => c.classList.add('hidden'));

        // 设置新的活动状态
        tab.classList.remove('bg-gray-200', 'text-gray-700');
        tab.classList.add('bg-blue-500', 'text-white');
        content.classList.remove('hidden');
    }

    // 绑定点击事件
    monitorTab.addEventListener('click', () => setActiveTab(monitorTab, monitorContent));
    configTab.addEventListener('click', () => setActiveTab(configTab, configContent));

    // 检查是否是独立窗口
    chrome.windows.getCurrent(window => {
        if (window.type === 'popup' && window.id !== chrome.windows.WINDOW_ID_NONE) {
            debug('检测到独立窗口，切换到配置标签');
            setActiveTab(configTab, configContent);
        } else {
            debug('默认显示监控标签');
            setActiveTab(monitorTab, monitorContent);
        }
    });
}

// 初始化配置界面
function initializeConfig() {
    debug('初始化配置界面');
    
    // 初始化标签页
    initializeTabs();
    
    // 加载保存的项目
    loadProjects();

    // 绑定新建项目按钮
    const addProjectBtn = document.getElementById('addProject');
    if (addProjectBtn) {
        addProjectBtn.addEventListener('click', () => {
            const project = new Project('新项目');
            projects.push(project);
            currentProject = project;
            renderProjects();
        });
    }

    // 绑定打开独立窗口按钮
    const openWindowBtn = document.getElementById('openWindow');
    if (openWindowBtn) {
        openWindowBtn.addEventListener('click', () => {
            debug('打开独立窗口');
            chrome.windows.create({
                url: chrome.runtime.getURL('popup.html'),
                type: 'popup',
                width: 480,
                height: 600
            });
        });
    }
}

// 加载项目
async function loadProjects() {
    debug('加载项目配置');
    try {
        const result = await chrome.storage.local.get('projects');
        if (result.projects) {
            projects = result.projects;
            renderProjects();
        }
    } catch (error) {
        debug(`加载项目失败: ${error.message}`);
    }
}

// 保存项目
async function saveProjects() {
    debug('保存项目配置');
    try {
        await chrome.storage.local.set({ projects });
        debug('项目保存成功');
    } catch (error) {
        debug(`保存项目失败: ${error.message}`);
        alert('保存失败，请重试');
    }
}

// 渲染项目列表
function renderProjects() {
    debug('渲染项目列表');
    const projectList = document.getElementById('projectList');
    if (!projectList) return;

    projectList.innerHTML = '';
    projects.forEach(project => {
        const projectElement = createProjectElement(project);
        projectList.appendChild(projectElement);
    });
}

// 创建项目元素
function createProjectElement(project) {
    const template = document.createElement('div');
    template.className = 'project-item border rounded-lg p-4';
    template.innerHTML = `
        <div class="flex justify-between items-center mb-3">
            <input type="text" class="project-name px-2 py-1 border rounded" value="${project.name}" placeholder="项目名称">
            <div class="space-x-2">
                <button class="edit-project px-2 py-1 text-blue-500 hover:text-blue-600">编辑</button>
                <button class="delete-project px-2 py-1 text-red-500 hover:text-red-600">删除</button>
            </div>
        </div>
        
        <!-- 执行条件 -->
        <div class="mb-3">
            <div class="font-medium mb-2">执行条件</div>
            <div class="flex items-center space-x-2">
                <select class="condition-type px-2 py-1 border rounded">
                    <option value="url" ${project.condition.type === 'url' ? 'selected' : ''}>URL包含</option>
                    <option value="element" ${project.condition.type === 'element' ? 'selected' : ''}>元素存在</option>
                    <option value="text" ${project.condition.type === 'text' ? 'selected' : ''}>页面包含文本</option>
                </select>
                <input type="text" class="condition-value flex-1 px-2 py-1 border rounded" 
                    value="${project.condition.value}" placeholder="条件值">
            </div>
        </div>

        <!-- 元素列表 -->
        <div class="mb-3">
            <div class="flex justify-between items-center mb-2">
                <div class="font-medium">基本元素</div>
                <button class="add-element px-2 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600">添加元素</button>
            </div>
            <div class="element-list space-y-2">
                ${project.elements.map(element => createElementHTML(element)).join('')}
            </div>
        </div>

        <!-- 执行步骤 -->
        <div>
            <div class="flex justify-between items-center mb-2">
                <div class="font-medium">执行步骤</div>
                <button class="add-step px-2 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600">添加步骤</button>
            </div>
            <div class="step-list space-y-2">
                ${project.steps.map((step, index) => createStepHTML(step, index, project.elements)).join('')}
            </div>
        </div>

        <!-- 测试和保存按钮 -->
        <div class="flex justify-end space-x-2 mt-4">
            <button class="test-project px-3 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600">测试</button>
            <button class="save-project px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600">保存</button>
        </div>
    `;

    // 绑定事件
    bindProjectEvents(template, project);
    
    return template;
}

// 创建元素HTML
function createElementHTML(element) {
    return `
        <div class="element-item flex items-center space-x-2 p-2 border rounded" data-id="${element.id}">
            <select class="element-type px-2 py-1 border rounded">
                <option value="input" ${element.type === 'input' ? 'selected' : ''}>输入框</option>
                <option value="button" ${element.type === 'button' ? 'selected' : ''}>按钮</option>
                <option value="captcha" ${element.type === 'captcha' ? 'selected' : ''}>验证码</option>
            </select>
            <input type="text" class="element-name flex-1 px-2 py-1 border rounded" 
                value="${element.name}" placeholder="元素名称">
            <button class="select-element px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600">选择</button>
            <button class="delete-element px-2 py-1 text-red-500 hover:text-red-600">删除</button>
        </div>
    `;
}

// 创建步骤HTML
function createStepHTML(step, index, elements) {
    return `
        <div class="step-item flex items-center space-x-2 p-2 border rounded" data-id="${step.id}">
            <div class="step-number w-6 text-center">${index + 1}</div>
            <select class="step-element px-2 py-1 border rounded">
                <option value="">选择元素</option>
                ${elements.map(element => `
                    <option value="${element.id}" ${step.elementId === element.id ? 'selected' : ''}>
                        ${element.name}
                    </option>
                `).join('')}
            </select>
            <select class="step-action px-2 py-1 border rounded">
                <option value="click" ${step.action === 'click' ? 'selected' : ''}>点击</option>
                <option value="input" ${step.action === 'input' ? 'selected' : ''}>输入</option>
                <option value="wait" ${step.action === 'wait' ? 'selected' : ''}>等待</option>
            </select>
            <input type="text" class="step-value flex-1 px-2 py-1 border rounded" 
                value="${step.value}" placeholder="操作值">
            <button class="delete-step px-2 py-1 text-red-500 hover:text-red-600">删除</button>
        </div>
    `;
}

// 绑定项目事件
function bindProjectEvents(projectElement, project) {
    // 项目名称变更
    const nameInput = projectElement.querySelector('.project-name');
    nameInput?.addEventListener('change', () => {
        project.name = nameInput.value;
        saveProjects();
    });

    // 删除项目
    const deleteBtn = projectElement.querySelector('.delete-project');
    deleteBtn?.addEventListener('click', () => {
        if (confirm('确定要删除此项目吗？')) {
            projects = projects.filter(p => p.id !== project.id);
            renderProjects();
            saveProjects();
        }
    });

    // 条件配置变更
    const conditionType = projectElement.querySelector('.condition-type');
    const conditionValue = projectElement.querySelector('.condition-value');
    
    conditionType?.addEventListener('change', () => {
        project.condition.type = conditionType.value;
        saveProjects();
    });
    
    conditionValue?.addEventListener('change', () => {
        project.condition.value = conditionValue.value;
        saveProjects();
    });

    // 添加元素
    const addElementBtn = projectElement.querySelector('.add-element');
    addElementBtn?.addEventListener('click', () => {
        const element = new Element();
        project.elements.push(element);
        const elementList = projectElement.querySelector('.element-list');
        if (elementList) {
            const elementElement = createElementElement(element, project);
            elementList.appendChild(elementElement);
        }
        saveProjects();
    });

    // 添加步骤
    const addStepBtn = projectElement.querySelector('.add-step');
    addStepBtn?.addEventListener('click', () => {
        const step = new Step();
        project.steps.push(step);
        const stepList = projectElement.querySelector('.step-list');
        if (stepList) {
            const stepElement = createStepElement(step, project.steps.length - 1, project);
            stepList.appendChild(stepElement);
        }
        saveProjects();
    });

    // 测试项目
    const testBtn = projectElement.querySelector('.test-project');
    testBtn?.addEventListener('click', () => {
        testProject(project);
    });
}

// 创建元素DOM元素
function createElementElement(element, project) {
    const div = document.createElement('div');
    div.innerHTML = createElementHTML(element);
    const elementElement = div.firstElementChild;
    
    if (elementElement) {
        // 绑定元素事件
        const typeSelect = elementElement.querySelector('.element-type');
        const nameInput = elementElement.querySelector('.element-name');
        const selectBtn = elementElement.querySelector('.select-element');
        const deleteBtn = elementElement.querySelector('.delete-element');

        typeSelect?.addEventListener('change', () => {
            element.type = typeSelect.value;
            saveProjects();
        });

        nameInput?.addEventListener('change', () => {
            element.name = nameInput.value;
            saveProjects();
            // 更新所有使用此元素的步骤显示
            updateStepElements(project);
        });

        selectBtn?.addEventListener('click', () => {
            startElementSelection(element, project);
        });

        deleteBtn?.addEventListener('click', () => {
            if (confirm('确定要删除此元素吗？')) {
                project.elements = project.elements.filter(e => e.id !== element.id);
                elementElement.remove();
                // 更新步骤中的元素选择
                updateStepElements(project);
                saveProjects();
            }
        });
    }

    return elementElement;
}

// 创建步骤DOM元素
function createStepElement(step, index, project) {
    const div = document.createElement('div');
    div.innerHTML = createStepHTML(step, index, project.elements);
    const stepElement = div.firstElementChild;
    
    if (stepElement) {
        // 绑定步骤事件
        const elementSelect = stepElement.querySelector('.step-element');
        const actionSelect = stepElement.querySelector('.step-action');
        const valueInput = stepElement.querySelector('.step-value');
        const deleteBtn = stepElement.querySelector('.delete-step');

        elementSelect?.addEventListener('change', () => {
            step.elementId = elementSelect.value;
            saveProjects();
        });

        actionSelect?.addEventListener('change', () => {
            step.action = actionSelect.value;
            saveProjects();
        });

        valueInput?.addEventListener('change', () => {
            step.value = valueInput.value;
            saveProjects();
        });

        deleteBtn?.addEventListener('click', () => {
            if (confirm('确定要删除此步骤吗？')) {
                project.steps = project.steps.filter(s => s.id !== step.id);
                stepElement.remove();
                // 更新步骤编号
                updateStepNumbers(project);
                saveProjects();
            }
        });
    }

    return stepElement;
}

// 更新步骤中的元素选择
function updateStepElements(project) {
    const projectElement = document.querySelector(`.project-item[data-id="${project.id}"]`);
    if (!projectElement) return;

    const stepElements = projectElement.querySelectorAll('.step-item');
    stepElements.forEach((stepElement, index) => {
        const elementSelect = stepElement.querySelector('.step-element');
        if (elementSelect) {
            const currentValue = elementSelect.value;
            elementSelect.innerHTML = `
                <option value="">选择元素</option>
                ${project.elements.map(element => `
                    <option value="${element.id}" ${currentValue === element.id ? 'selected' : ''}>
                        ${element.name}
                    </option>
                `).join('')}
            `;
        }
    });
}

// 更新步骤编号
function updateStepNumbers(project) {
    const projectElement = document.querySelector(`.project-item[data-id="${project.id}"]`);
    if (!projectElement) return;

    const stepNumbers = projectElement.querySelectorAll('.step-number');
    stepNumbers.forEach((numberElement, index) => {
        numberElement.textContent = (index + 1).toString();
    });
}

// 开始元素选择
async function startElementSelection(element, project) {
    debug(`开始选择元素: ${element.name}`);
    currentSelectingType = element.id;

    try {
        // 获取当前窗口信息
        const currentWindow = await chrome.windows.getCurrent();
        const isPopup = currentWindow.type === 'popup';
        debug(`当前窗口类型: ${currentWindow.type}`);

        // 获取所有窗口
        const windows = await chrome.windows.getAll({ populate: true });
        debug(`找到 ${windows.length} 个窗口`);

        // 在所有窗口中查找可用的网页标签
        let targetTab = null;
        let targetWindow = null;

        if (!isPopup) {
            // 如果是插件弹出窗口，直接使用当前活动标签
            const [activeTab] = await chrome.tabs.query({ 
                active: true, 
                currentWindow: true 
            });
            
            if (activeTab && !activeTab.url.startsWith('chrome-extension://')) {
                targetTab = activeTab;
                targetWindow = currentWindow;
                debug(`使用当前活动标签: ${targetTab.url}`);
            }
        } else {
            // 如果是独立窗口，查找其他窗口中的标签
            for (const window of windows) {
                if (window.id === currentWindow.id) continue;
                
                const [activeTab] = await chrome.tabs.query({ 
                    active: true, 
                    windowId: window.id 
                });

                if (activeTab && !activeTab.url.startsWith('chrome-extension://') && 
                    !activeTab.url.startsWith('chrome:') && 
                    !activeTab.url.startsWith('edge:')) {
                    targetTab = activeTab;
                    targetWindow = window;
                    debug(`在窗口 ${window.id} 中找到活动标签: ${targetTab.url}`);
                    break;
                }
            }
        }

        if (!targetTab || !targetWindow) {
            throw new Error('未找到可用的网页标签页，请先打开一个网页');
        }

        // 注入内容脚本
        try {
            await chrome.scripting.executeScript({
                target: { tabId: targetTab.id },
                files: ['content-script.js']
            });
            debug('内容脚本注入成功');
        } catch (error) {
            // 如果脚本已经注入，忽略错误
            if (!error.message.includes('already exists')) {
                debug(`注入脚本失败: ${error.message}`);
                throw error;
            }
        }

        // 发送开始选择消息
        const response = await chrome.tabs.sendMessage(targetTab.id, { 
            type: 'start_selecting',
            elementType: element.id
        });

        debug(`发送选择消息响应: ${JSON.stringify(response)}`);

        // 如果是插件弹出窗口则关闭
        if (!isPopup && window.innerWidth < 600) {
            window.close();
        }

        // 如果是独立窗口，切换到目标窗口
        if (isPopup && targetWindow.id !== currentWindow.id) {
            debug(`切换到窗口: ${targetWindow.id}`);
            await chrome.windows.update(targetWindow.id, { focused: true });
            await chrome.tabs.update(targetTab.id, { active: true });
        }

    } catch (error) {
        debug(`选择元素失败: ${error.message}`);
        alert('请先打开要选择元素的目标网页，然后再试一次。');
    }
}

// 测试项目
async function testProject(project) {
    debug(`开始测试项目: ${project.name}`);
    
    try {
        // 获取当前标签页
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        if (!tab) throw new Error('未找到当前标签页');

        // 检查执行条件
        const conditionMet = await checkCondition(project.condition, tab);
        if (!conditionMet) {
            alert('当前页面不满足执行条件');
            return;
        }

        // 注入内容脚本
        try {
            await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                files: ['content-script.js']
            });
        } catch (error) {
            if (!error.message.includes('already exists')) {
                throw error;
            }
        }

        // 执行步骤
        for (const step of project.steps) {
            const element = project.elements.find(e => e.id === step.elementId);
            if (!element || !element.selector) {
                throw new Error(`步骤 ${step.id} 的元素未找到或未选择`);
            }

            await executeStep(tab.id, step, element);
        }

        alert('测试完成');
    } catch (error) {
        debug(`测试失败: ${error.message}`);
        alert(`测试失败: ${error.message}`);
    }
}

// 检查执行条件
async function checkCondition(condition, tab) {
    switch (condition.type) {
        case 'url':
            return tab.url.includes(condition.value);
        
        case 'element':
            try {
                const [{result}] = await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    func: (selector) => !!document.querySelector(selector),
                    args: [condition.value]
                });
                return result;
            } catch (error) {
                return false;
            }
        
        case 'text':
            try {
                const [{result}] = await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    func: (text) => document.body.innerText.includes(text),
                    args: [condition.value]
                });
                return result;
            } catch (error) {
                return false;
            }
        
        default:
            return false;
    }
}

// 执行步骤
async function executeStep(tabId, step, element) {
    debug(`执行步骤: ${step.action} ${element.name}`);

    switch (step.action) {
        case 'click':
            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                func: (selector) => {
                    const element = document.querySelector(selector);
                    if (element) element.click();
                },
                args: [element.selector]
            });
            break;

        case 'input':
            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                func: (selector, value) => {
                    const element = document.querySelector(selector);
                    if (element) {
                        element.value = value;
                        element.dispatchEvent(new Event('input', { bubbles: true }));
                        element.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                },
                args: [element.selector, step.value]
            });
            break;

        case 'wait':
            await new Promise(resolve => setTimeout(resolve, parseInt(step.value) || 1000));
            break;
    }
}

// 处理来自content-script的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.type === 'element_selected') {
        debug(`收到元素选择消息: ${message.elementType}`);
        
        // 查找对应的项目和元素
        for (const project of projects) {
            const element = project.elements.find(e => e.id === message.elementType);
            if (element) {
                element.selector = message.element.cssSelector;
                saveProjects();
                
                // 更新显示
                const elementItem = document.querySelector(`.element-item[data-id="${element.id}"]`);
                if (elementItem) {
                    elementItem.classList.add('border-green-500');
                    setTimeout(() => {
                        elementItem.classList.remove('border-green-500');
                    }, 2000);
                }

                debug('元素选择成功，已更新配置');
                sendResponse({ success: true });
                return true;
            }
        }

        debug(`未找到对应的元素: ${message.elementType}`);
        sendResponse({ success: false, error: '未找到对应的元素' });
        return true;
    }
});

// 初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeConfig);
} else {
    initializeConfig();
} 
// 文件云流转助手 - 简化版页面注入面板内容脚本
// 用于调试和测试基本注入功能

(function() {
    'use strict';

    console.log('[YLZ简化面板] 开始执行');
    console.log('[YLZ简化面板] 当前URL:', window.location.href);
    console.log('[YLZ简化面板] 文档状态:', document.readyState);

    // 防止重复注入
    if (window.ylzSimplePanelInjected) {
        console.log('[YLZ简化面板] 已注入，跳过重复注入');
        return;
    }
    window.ylzSimplePanelInjected = true;

    // 检查是否为目标网站
    function isTargetSite() {
        const url = window.location.href;
        const isTarget = url.includes('yunpan.gdcourts.gov.cn') || 
                        url.includes('injected-panel-test.html') ||
                        url.includes('simple-panel-test.html');
        
        console.log('[YLZ简化面板] 网站检测:', { url, isTarget });
        return isTarget;
    }

    // 创建简化面板
    function createSimplePanel() {
        console.log('[YLZ简化面板] 开始创建简化面板');

        // 检查是否已存在
        if (document.getElementById('ylz-simple-panel')) {
            console.log('[YLZ简化面板] 面板已存在');
            return;
        }

        const panelHTML = `
            <div id="ylz-simple-panel" style="
                position: fixed;
                top: 20px;
                right: 20px;
                width: 320px;
                background: white;
                border: 2px solid #007bff;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 2147483647;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 14px;
            ">
                <div style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 12px 16px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    cursor: move;
                    border-radius: 6px 6px 0 0;
                " id="ylz-simple-header">
                    <h3 style="margin: 0; font-size: 16px;">🚀 文件云流转助手</h3>
                    <div>
                        <button id="ylz-simple-minimize" style="
                            background: rgba(255,255,255,0.2);
                            border: none;
                            color: white;
                            width: 24px;
                            height: 24px;
                            border-radius: 4px;
                            cursor: pointer;
                            margin-left: 4px;
                        ">−</button>
                        <button id="ylz-simple-close" style="
                            background: rgba(255,255,255,0.2);
                            border: none;
                            color: white;
                            width: 24px;
                            height: 24px;
                            border-radius: 4px;
                            cursor: pointer;
                            margin-left: 4px;
                        ">×</button>
                    </div>
                </div>
                <div id="ylz-simple-content" style="padding: 16px;">
                    <div style="margin-bottom: 12px;">
                        <div style="display: flex; align-items: center; margin-bottom: 8px;">
                            <div id="ylz-simple-status-dot" style="
                                width: 8px;
                                height: 8px;
                                border-radius: 50%;
                                background: #ffc107;
                                margin-right: 8px;
                            "></div>
                            <span id="ylz-simple-status-text" style="font-size: 13px; color: #666;">检测中...</span>
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 16px;">
                        <button id="ylz-simple-test-connection" style="
                            background: #007bff;
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 4px;
                            cursor: pointer;
                            margin-right: 8px;
                            font-size: 13px;
                        ">测试连接</button>
                        <button id="ylz-simple-refresh" style="
                            background: #28a745;
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 13px;
                        ">刷新</button>
                    </div>
                    
                    <div style="
                        background: #f8f9fa;
                        padding: 12px;
                        border-radius: 4px;
                        font-size: 12px;
                        color: #666;
                        max-height: 150px;
                        overflow-y: auto;
                    " id="ylz-simple-log">
                        面板已成功注入！<br>
                        当前时间: ${new Date().toLocaleTimeString()}<br>
                        页面URL: ${window.location.href}<br>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', panelHTML);
        console.log('[YLZ简化面板] 面板HTML已插入');

        // 绑定事件
        bindEvents();
        
        // 测试连接
        setTimeout(testConnection, 1000);
    }

    // 绑定事件
    function bindEvents() {
        console.log('[YLZ简化面板] 开始绑定事件');

        const panel = document.getElementById('ylz-simple-panel');
        const header = document.getElementById('ylz-simple-header');
        const minimizeBtn = document.getElementById('ylz-simple-minimize');
        const closeBtn = document.getElementById('ylz-simple-close');
        const testBtn = document.getElementById('ylz-simple-test-connection');
        const refreshBtn = document.getElementById('ylz-simple-refresh');

        if (!panel) {
            console.error('[YLZ简化面板] 面板元素未找到');
            return;
        }

        // 最小化功能
        if (minimizeBtn) {
            minimizeBtn.addEventListener('click', () => {
                const content = document.getElementById('ylz-simple-content');
                if (content) {
                    const isMinimized = content.style.display === 'none';
                    content.style.display = isMinimized ? 'block' : 'none';
                    minimizeBtn.textContent = isMinimized ? '−' : '+';
                    log(isMinimized ? '面板已展开' : '面板已最小化');
                }
            });
        }

        // 关闭功能
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                panel.style.display = 'none';
                log('面板已关闭');
            });
        }

        // 测试连接
        if (testBtn) {
            testBtn.addEventListener('click', testConnection);
        }

        // 刷新
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                log('刷新按钮被点击');
                testConnection();
            });
        }

        // 拖拽功能
        if (header) {
            let isDragging = false;
            let dragOffset = { x: 0, y: 0 };

            header.addEventListener('mousedown', (e) => {
                if (e.target.tagName === 'BUTTON') return;
                
                isDragging = true;
                const rect = panel.getBoundingClientRect();
                dragOffset = {
                    x: e.clientX - rect.left,
                    y: e.clientY - rect.top
                };
                e.preventDefault();
                log('开始拖拽');
            });

            document.addEventListener('mousemove', (e) => {
                if (!isDragging) return;

                const x = e.clientX - dragOffset.x;
                const y = e.clientY - dragOffset.y;

                const maxX = window.innerWidth - panel.offsetWidth;
                const maxY = window.innerHeight - panel.offsetHeight;

                const constrainedX = Math.max(0, Math.min(x, maxX));
                const constrainedY = Math.max(0, Math.min(y, maxY));

                panel.style.left = constrainedX + 'px';
                panel.style.top = constrainedY + 'px';
                panel.style.right = 'auto';
            });

            document.addEventListener('mouseup', () => {
                if (isDragging) {
                    isDragging = false;
                    log('拖拽结束');
                }
            });
        }

        console.log('[YLZ简化面板] 事件绑定完成');
    }

    // 测试连接
    function testConnection() {
        log('开始测试连接...');
        
        const statusDot = document.getElementById('ylz-simple-status-dot');
        const statusText = document.getElementById('ylz-simple-status-text');

        if (chrome && chrome.runtime) {
            chrome.runtime.sendMessage({ type: 'GET_CONNECTION_STATUS' }, (response) => {
                if (chrome.runtime.lastError) {
                    log(`连接错误: ${chrome.runtime.lastError.message}`);
                    updateStatus('error', '连接失败');
                } else {
                    const connected = response?.connected || false;
                    log(`连接状态: ${connected ? '已连接' : '未连接'}`);
                    updateStatus(connected ? 'success' : 'warning', connected ? '已连接' : '未连接');
                }
            });
        } else {
            log('Chrome扩展API不可用');
            updateStatus('error', 'API不可用');
        }
    }

    // 更新状态
    function updateStatus(type, message) {
        const statusDot = document.getElementById('ylz-simple-status-dot');
        const statusText = document.getElementById('ylz-simple-status-text');

        if (statusDot && statusText) {
            const colors = {
                success: '#28a745',
                warning: '#ffc107',
                error: '#dc3545'
            };
            
            statusDot.style.background = colors[type] || '#6c757d';
            statusText.textContent = message;
        }
    }

    // 日志函数
    function log(message) {
        console.log('[YLZ简化面板]', message);
        
        const logArea = document.getElementById('ylz-simple-log');
        if (logArea) {
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `[${timestamp}] ${message}<br>`;
            logArea.scrollTop = logArea.scrollHeight;
        }
    }

    // 初始化
    function initialize() {
        console.log('[YLZ简化面板] 开始初始化');

        if (!isTargetSite()) {
            console.log('[YLZ简化面板] 非目标网站，跳过注入');
            return;
        }

        // 等待DOM准备就绪
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(createSimplePanel, 1000);
            });
        } else {
            setTimeout(createSimplePanel, 1000);
        }
    }

    // 开始初始化
    initialize();

})();

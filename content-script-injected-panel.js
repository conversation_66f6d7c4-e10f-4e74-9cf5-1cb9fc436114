// 文件云流转助手 - 页面注入面板内容脚本
// 专门用于在 https://yunpan.gdcourts.gov.cn:82/ 页面注入面板

(function() {
    'use strict';

    // 防止重复注入
    if (window.ylzPanelInjected) {
        console.log('[YLZ面板注入] 已注入，跳过重复注入');
        return;
    }
    window.ylzPanelInjected = true;

    console.log('[YLZ面板注入] 开始注入页面面板');
    console.log('[YLZ面板注入] 当前URL:', window.location.href);
    console.log('[YLZ面板注入] 文档状态:', document.readyState);
    console.log('[YLZ面板注入] Chrome扩展API可用:', !!(chrome && chrome.runtime));

    // 检查是否为目标网站
    function isTargetSite() {
        const url = window.location.href;
        const isTarget = url.includes('yunpan.gdcourts.gov.cn:82') ||
                        url.includes('yunpan.gdcourts.gov.cn') ||
                        url.includes('injected-panel-test.html'); // 包含测试页面

        console.log('[YLZ面板注入] 网站检测:', {
            url: url,
            isTarget: isTarget
        });

        return isTarget;
    }

    // 等待页面加载完成
    function waitForPageReady() {
        return new Promise((resolve) => {
            if (document.readyState === 'complete') {
                resolve();
            } else {
                window.addEventListener('load', resolve);
            }
        });
    }

    // 注入面板HTML和CSS
    async function injectPanel() {
        try {
            console.log('[YLZ面板注入] 开始注入面板HTML');

            // 检查是否已存在面板
            if (document.getElementById('ylz-injected-panel')) {
                console.log('[YLZ面板注入] 面板已存在，跳过注入');
                return document.getElementById('ylz-injected-panel');
            }

            // 获取面板HTML内容
            const response = await fetch(chrome.runtime.getURL('injected-panel.html'));
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const htmlText = await response.text();
            console.log('[YLZ面板注入] HTML内容获取成功，长度:', htmlText.length);

            // 解析HTML
            const parser = new DOMParser();
            const doc = parser.parseFromString(htmlText, 'text/html');

            // 提取样式
            const styles = doc.querySelectorAll('style');
            console.log('[YLZ面板注入] 找到样式数量:', styles.length);
            styles.forEach((style, index) => {
                const newStyle = style.cloneNode(true);
                newStyle.id = `ylz-injected-style-${index}`;
                document.head.appendChild(newStyle);
            });

            // 提取面板元素
            const panelElement = doc.getElementById('ylz-injected-panel');
            if (panelElement) {
                // 确保面板在页面最上层
                panelElement.style.zIndex = '2147483647';
                panelElement.style.position = 'fixed';

                // 添加到页面
                document.body.appendChild(panelElement);

                console.log('[YLZ面板注入] 面板HTML注入成功');
                return panelElement;
            } else {
                throw new Error('无法找到面板元素');
            }
        } catch (error) {
            console.error('[YLZ面板注入] 注入面板HTML失败:', error);
            return createFallbackPanel();
        }
    }

    // 创建备用面板
    function createFallbackPanel() {
        const panelHTML = `
            <div id="ylz-injected-panel" style="
                position: fixed;
                top: 20px;
                right: 20px;
                width: 480px;
                max-height: 80vh;
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.15);
                z-index: 2147483647;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 14px;
                overflow: hidden;
            ">
                <div style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 12px 16px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    cursor: move;
                " id="ylz-panel-header">
                    <h1 style="margin: 0; font-size: 16px; font-weight: 600;">文件云流转助手</h1>
                    <div>
                        <button id="ylz-minimize-btn" style="
                            background: rgba(255,255,255,0.2);
                            border: none;
                            color: white;
                            width: 24px;
                            height: 24px;
                            border-radius: 4px;
                            cursor: pointer;
                            margin-left: 8px;
                        ">−</button>
                        <button id="ylz-close-btn" style="
                            background: rgba(255,255,255,0.2);
                            border: none;
                            color: white;
                            width: 24px;
                            height: 24px;
                            border-radius: 4px;
                            cursor: pointer;
                            margin-left: 8px;
                        ">×</button>
                    </div>
                </div>
                <div style="padding: 16px;">
                    <div style="text-align: center; padding: 20px; color: #666;">
                        <p>🚀 文件云流转助手已激活</p>
                        <p>正在初始化完整功能...</p>
                        <div style="margin-top: 16px;">
                            <button id="ylz-simple-upload" style="
                                background: #007bff;
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 6px;
                                cursor: pointer;
                                margin-right: 8px;
                            ">开始上传</button>
                            <button id="ylz-simple-refresh" style="
                                background: #6c757d;
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 6px;
                                cursor: pointer;
                            ">刷新列表</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', panelHTML);
        const panel = document.getElementById('ylz-injected-panel');
        
        // 绑定基本事件
        bindFallbackEvents(panel);
        
        console.log('[YLZ面板注入] 备用面板创建成功');
        return panel;
    }

    // 绑定备用面板事件
    function bindFallbackEvents(panel) {
        // 最小化按钮
        const minimizeBtn = panel.querySelector('#ylz-minimize-btn');
        if (minimizeBtn) {
            let isMinimized = false;
            minimizeBtn.addEventListener('click', () => {
                isMinimized = !isMinimized;
                const content = panel.querySelector('div:last-child');
                if (content) {
                    content.style.display = isMinimized ? 'none' : 'block';
                }
                minimizeBtn.textContent = isMinimized ? '+' : '−';
            });
        }

        // 关闭按钮
        const closeBtn = panel.querySelector('#ylz-close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                panel.style.display = 'none';
            });
        }

        // 简单上传按钮
        const uploadBtn = panel.querySelector('#ylz-simple-upload');
        if (uploadBtn) {
            uploadBtn.addEventListener('click', async () => {
                try {
                    uploadBtn.textContent = '上传中...';
                    uploadBtn.disabled = true;
                    
                    const response = await chrome.runtime.sendMessage({ 
                        type: 'START_YUNPAN_UPLOAD' 
                    });
                    
                    if (response?.success) {
                        showSimpleNotification('开始上传文件...', 'success');
                    } else {
                        showSimpleNotification('上传启动失败', 'error');
                    }
                } catch (error) {
                    console.error('[YLZ面板注入] 上传失败:', error);
                    showSimpleNotification('上传启动失败', 'error');
                } finally {
                    uploadBtn.textContent = '开始上传';
                    uploadBtn.disabled = false;
                }
            });
        }

        // 简单刷新按钮
        const refreshBtn = panel.querySelector('#ylz-simple-refresh');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', async () => {
                try {
                    refreshBtn.textContent = '刷新中...';
                    refreshBtn.disabled = true;
                    
                    await chrome.runtime.sendMessage({ 
                        type: 'SEND_WS_MESSAGE', 
                        wsMessage: { type: 'get_full_admin_file_tree' }
                    });
                    
                    showSimpleNotification('正在刷新文件列表...', 'info');
                } catch (error) {
                    console.error('[YLZ面板注入] 刷新失败:', error);
                    showSimpleNotification('刷新失败', 'error');
                } finally {
                    refreshBtn.textContent = '刷新列表';
                    refreshBtn.disabled = false;
                }
            });
        }

        // 拖拽功能
        makeDraggable(panel);
    }

    // 使面板可拖拽
    function makeDraggable(panel) {
        const header = panel.querySelector('#ylz-panel-header');
        if (!header) return;

        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };

        header.addEventListener('mousedown', (e) => {
            isDragging = true;
            const rect = panel.getBoundingClientRect();
            dragOffset = {
                x: e.clientX - rect.left,
                y: e.clientY - rect.top
            };
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;

            const x = e.clientX - dragOffset.x;
            const y = e.clientY - dragOffset.y;

            // 限制拖拽范围
            const maxX = window.innerWidth - panel.offsetWidth;
            const maxY = window.innerHeight - panel.offsetHeight;

            const constrainedX = Math.max(0, Math.min(x, maxX));
            const constrainedY = Math.max(0, Math.min(y, maxY));

            panel.style.left = constrainedX + 'px';
            panel.style.top = constrainedY + 'px';
            panel.style.right = 'auto';
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
        });
    }

    // 显示简单通知
    function showSimpleNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#007bff'};
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            z-index: 2147483648;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: ylz-slide-in 0.3s ease;
        `;
        
        // 添加动画样式
        if (!document.getElementById('ylz-notification-styles')) {
            const style = document.createElement('style');
            style.id = 'ylz-notification-styles';
            style.textContent = `
                @keyframes ylz-slide-in {
                    from { transform: translateX(-50%) translateY(-20px); opacity: 0; }
                    to { transform: translateX(-50%) translateY(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }
        
        notification.textContent = message;
        document.body.appendChild(notification);

        // 3秒后自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'ylz-slide-in 0.3s ease reverse';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }, 3000);
    }

    // 注入完整的面板JavaScript
    async function injectPanelScript() {
        try {
            console.log('[YLZ面板注入] 开始注入面板脚本');

            // 创建script标签加载面板逻辑
            const script = document.createElement('script');
            script.src = chrome.runtime.getURL('injected-panel.js');
            script.onload = () => {
                console.log('[YLZ面板注入] 面板脚本加载成功');

                // 延迟设置通信，确保脚本完全加载
                setTimeout(() => {
                    console.log('[YLZ面板注入] 开始设置面板通信');
                    setupPanelCommunication();
                }, 500);
            };
            script.onerror = () => {
                console.error('[YLZ面板注入] 面板脚本加载失败');
            };

            document.head.appendChild(script);

            // 等待脚本加载
            await new Promise((resolve) => {
                script.onload = resolve;
                script.onerror = resolve;
            });

        } catch (error) {
            console.error('[YLZ面板注入] 注入面板脚本失败:', error);
        }
    }

    // 设置面板通信
    function setupPanelCommunication() {
        console.log('[YLZ面板注入] 设置面板通信');

        // 监听来自注入面板的消息
        window.addEventListener('ylz_panel_message', async (event) => {
            const { messageId, message } = event.detail;
            console.log('[YLZ面板注入] 收到面板消息:', message);

            try {
                // 转发消息到background script
                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage(message, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(chrome.runtime.lastError);
                        } else {
                            resolve(response);
                        }
                    });
                });

                // 发送响应回面板
                const responseEvent = new CustomEvent('ylz_panel_response_' + messageId, {
                    detail: response
                });
                window.dispatchEvent(responseEvent);

            } catch (error) {
                console.error('[YLZ面板注入] 消息转发失败:', error);

                // 发送错误响应
                const errorEvent = new CustomEvent('ylz_panel_response_' + messageId, {
                    detail: { error: error.message }
                });
                window.dispatchEvent(errorEvent);
            }
        });

        // 监听来自background的消息并转发给面板
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            console.log('[YLZ面板注入] 收到background消息，转发给面板:', message);

            // 转发给面板
            const panelEvent = new CustomEvent('ylz_background_message', {
                detail: message
            });
            window.dispatchEvent(panelEvent);

            return false; // 不需要异步响应
        });
    }

    // 监听来自background的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        switch (message.type) {
            case 'TOGGLE_INJECTED_PANEL':
                togglePanel();
                sendResponse({ success: true });
                break;
            case 'SHOW_INJECTED_PANEL':
                showPanel();
                sendResponse({ success: true });
                break;
            case 'HIDE_INJECTED_PANEL':
                hidePanel();
                sendResponse({ success: true });
                break;
            default:
                // 转发消息给面板
                if (window.ylzInjectedPanel) {
                    window.ylzInjectedPanel.handleMessage(message, sender, sendResponse);
                }
        }
    });

    // 面板控制函数
    function togglePanel() {
        const panel = document.getElementById('ylz-injected-panel');
        if (panel) {
            const isHidden = panel.style.display === 'none' || panel.classList.contains('hidden');
            if (isHidden) {
                showPanel();
            } else {
                hidePanel();
            }
        }
    }

    function showPanel() {
        const panel = document.getElementById('ylz-injected-panel');
        if (panel) {
            panel.style.display = 'block';
            panel.classList.remove('hidden');
        }
    }

    function hidePanel() {
        const panel = document.getElementById('ylz-injected-panel');
        if (panel) {
            panel.classList.add('hidden');
        }
    }

    // 主初始化函数
    async function initialize() {
        try {
            console.log('[YLZ面板注入] 开始初始化');

            // 检查是否为目标网站
            if (!isTargetSite()) {
                console.log('[YLZ面板注入] 非目标网站，跳过注入');
                return;
            }

            console.log('[YLZ面板注入] 目标网站确认，开始初始化');

            // 立即设置通信监听器
            console.log('[YLZ面板注入] 预先设置通信监听器');
            setupPanelCommunication();

            // 等待页面加载完成
            await waitForPageReady();
            console.log('[YLZ面板注入] 页面加载完成');

            // 延迟一段时间确保页面完全稳定
            setTimeout(async () => {
                try {
                    console.log('[YLZ面板注入] 开始注入面板');

                    // 注入面板
                    const panel = await injectPanel();

                    if (panel) {
                        console.log('[YLZ面板注入] 面板注入成功，开始注入脚本');

                        // 注入面板脚本（如果需要完整功能）
                        await injectPanelScript();

                        console.log('[YLZ面板注入] 初始化完成');

                        // 等待面板脚本加载完成后显示通知
                        setTimeout(() => {
                            showSimpleNotification('文件云流转助手已激活', 'success');

                            // 检查面板是否正确初始化
                            setTimeout(() => {
                                const panel = document.getElementById('ylz-injected-panel');
                                if (panel && window.ylzInjectedPanel) {
                                    console.log('[YLZ面板注入] 完整面板初始化成功');
                                } else {
                                    console.warn('[YLZ面板注入] 面板初始化可能有问题');
                                }
                            }, 2000);
                        }, 500);
                    } else {
                        console.error('[YLZ面板注入] 面板注入失败');
                        showSimpleNotification('面板加载失败', 'error');
                    }
                } catch (error) {
                    console.error('[YLZ面板注入] 延迟初始化失败:', error);
                    showSimpleNotification('初始化失败: ' + error.message, 'error');
                }
            }, 2000); // 增加延迟时间，确保页面完全稳定

        } catch (error) {
            console.error('[YLZ面板注入] 初始化失败:', error);
            showSimpleNotification('初始化失败: ' + error.message, 'error');
        }
    }

    // 开始初始化
    initialize();

})();
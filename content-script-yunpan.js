// content-script-yunpan.js - 云盘页面内容脚本
// 专用于与政府云盘系统进行DOM交互和文件上传操作

class YunpanController {
    constructor() {
        this.debug = true;
        this.uploadQueue = [];
        this.currentPath = [];
        this.isUploading = false;
        this.uploadStats = {
            total: 0,
            completed: 0,
            failed: 0
        };
        
        this.init();
    }

    // 调试日志
    log(message, data = null) {
        if (this.debug) {
            console.log(`[云盘助手] ${message}`, data || '');
        }
    }

    // 初始化
    init() {
        this.log('云盘页面内容脚本已加载');
        this.setupMessageListener();
        this.waitForPageReady();
    }

    // 等待页面加载完成
    async waitForPageReady() {
        this.log('开始等待页面准备就绪');
        
        return new Promise((resolve) => {
            let checkCount = 0;
            const maxChecks = 40; // 最多检查20秒
            
            const checkReady = () => {
                checkCount++;
                this.log(`页面准备检查第 ${checkCount}/${maxChecks} 次`);
                
                // 检查关键DOM元素是否存在
                const indicators = [
                    // 文件列表容器
                    { selector: '.list-container', name: '文件列表容器' },
                    { selector: '.file-list', name: '文件列表' },
                    { selector: '.file-list-content', name: '文件列表内容' },
                    { selector: '.main-content', name: '主内容区' },
                    
                    // 上传相关元素
                    { selector: '.upload-box', name: '上传区域' },
                    { selector: '.upload-btn', name: '上传按钮' },
                    { selector: '.upload-area', name: '上传区域' },
                    
                    // 操作按钮
                    { selector: '.new-btn', name: '新建按钮' },
                    { selector: '.create-btn', name: '创建按钮' },
                    
                    // 导航元素  
                    { selector: '.breadcrumb', name: '面包屑导航' },
                    { selector: '.nav-path', name: '导航路径' }
                ];
                
                let foundElements = [];
                let criticalElements = [];
                
                for (const indicator of indicators) {
                    const element = document.querySelector(indicator.selector);
                    if (element) {
                        foundElements.push(indicator.name);
                        
                        // 关键元素：文件列表和上传功能
                        if (indicator.selector.includes('list') || indicator.selector.includes('upload') || indicator.selector.includes('new')) {
                            criticalElements.push(indicator.name);
                        }
                    }
                }
                
                this.log(`找到页面元素: ${foundElements.join(', ')}`);
                this.log(`关键元素: ${criticalElements.join(', ')}`);
                
                // 检查页面是否处于加载状态
                const loadingIndicators = [
                    '.loading',
                    '.spinner', 
                    '.loading-mask',
                    '.loading-overlay',
                    '[data-loading="true"]'
                ];
                
                let isLoading = false;
                for (const loadingSelector of loadingIndicators) {
                    const loadingElement = document.querySelector(loadingSelector);
                    if (loadingElement && loadingElement.offsetParent !== null) {
                        isLoading = true;
                        this.log(`检测到加载指示器: ${loadingSelector}`);
                        break;
                    }
                }
                
                // 页面准备条件：
                // 1. 至少找到2个关键元素，或者找到文件列表容器
                // 2. 没有加载指示器
                // 3. document.readyState === 'complete'
                const hasEnoughElements = criticalElements.length >= 2 || foundElements.some(name => name.includes('文件列表'));
                const docReady = document.readyState === 'complete';
                
                this.log(`页面准备状态检查: 元素足够=${hasEnoughElements}, 文档就绪=${docReady}, 正在加载=${isLoading}`);
                
                if (hasEnoughElements && !isLoading && docReady) {
                    this.log('云盘页面已准备就绪');
                    
                    // 记录最终的页面状态
                    this.log('页面准备完成时的状态:');
                    this.log('- 页面标题:', document.title);
                    this.log('- 页面URL:', window.location.href);
                    this.log('- 找到的元素:', foundElements);
                    
                    resolve();
                } else if (checkCount >= maxChecks) {
                    this.log('页面准备检查超时，强制继续');
                    this.log('最终状态:', {
                        foundElements,
                        criticalElements,
                        isLoading,
                        docReady
                    });
                    resolve();
                } else {
                    setTimeout(checkReady, 500);
                }
            };
            
            checkReady();
        });
    }

    // 设置消息监听器
    setupMessageListener() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.log('收到消息', request);
            this.log('消息类型:', request.type);
            this.log('消息数据:', request.data);

            switch (request.type) {
                case 'START_UPLOAD':
                    this.log('收到上传请求，开始处理');
                    this.handleStartUpload(request.data)
                        .then(result => {
                            this.log('上传处理完成，发送成功响应');
                            sendResponse({ success: true, result });
                        })
                        .catch(error => {
                            this.log('上传处理失败:', error);
                            sendResponse({ success: false, error: error.message });
                        });
                    return true; // 保持消息通道开放

                case 'GET_UPLOAD_STATUS':
                    sendResponse({
                        success: true,
                        status: {
                            isUploading: this.isUploading,
                            stats: this.uploadStats,
                            currentPath: this.currentPath
                        }
                    });
                    return true;

                case 'PING':
                    this.log('收到PING请求，响应正常');
                    sendResponse({ success: true, message: '云盘脚本运行正常' });
                    return true;

                case 'RESET_UPLOAD_STATE':
                    this.log('收到重置上传状态请求');
                    this.isUploading = false;
                    this.uploadStats = { total: 0, completed: 0, failed: 0 };
                    this.uploadQueue = [];
                    this.currentPath = [];
                    sendResponse({ success: true, message: '上传状态已重置' });
                    return true;

                default:
                    this.log('未处理的消息类型:', request.type);
                    sendResponse({ success: false, error: '未知的消息类型' });
                    return true;
            }
        });

        // 监听来自面板的消息
        this.setupPanelMessageListener();

        // 监听强制重置事件
        this.setupForceResetListener();
    }

    // 设置面板消息监听器
    setupPanelMessageListener() {
        window.addEventListener('ylz_panel_message', async (event) => {
            this.log('收到面板消息', event.detail);

            const { messageId, message } = event.detail;

            // 详细调试消息内容
            this.log('消息详细信息:');
            this.log('  messageId:', messageId);
            this.log('  message:', message);
            this.log('  message.type:', message?.type);
            this.log('  message.data:', message?.data);
            this.log('  message.data.files:', message?.data?.files);
            this.log('  message序列化测试:', JSON.stringify(message));

            try {
                // 转发消息到background script
                this.log('准备转发消息到background...');
                const response = await new Promise((resolve) => {
                    chrome.runtime.sendMessage(message, resolve);
                });

                this.log('转发响应', response);

                // 发送响应回面板
                const responseEvent = new CustomEvent('ylz_panel_response_' + messageId, {
                    detail: response
                });

                window.dispatchEvent(responseEvent);

            } catch (error) {
                this.log('处理面板消息失败', error);

                // 发送错误响应
                const errorResponse = new CustomEvent('ylz_panel_response_' + messageId, {
                    detail: {
                        success: false,
                        error: error.message
                    }
                });

                window.dispatchEvent(errorResponse);
            }
        });
    }

    // 设置强制重置监听器
    setupForceResetListener() {
        window.addEventListener('ylz_force_reset_upload', (event) => {
            this.log('收到强制重置上传状态事件', event.detail);

            // 强制重置所有状态
            this.isUploading = false;
            this.uploadStats = { total: 0, completed: 0, failed: 0 };
            this.uploadQueue = [];
            this.currentPath = [];

            this.log('上传状态已强制重置');

            // 发送确认事件
            const confirmEvent = new CustomEvent('ylz_upload_reset_confirmed', {
                detail: {
                    timestamp: Date.now(),
                    resetBy: event.detail?.timestamp || 'unknown'
                }
            });
            window.dispatchEvent(confirmEvent);
        });
    }

    // 处理开始上传请求
    async handleStartUpload(uploadData) {
        this.log('开始处理上传请求', uploadData);
        
        if (this.isUploading) {
            throw new Error('上传正在进行中，请稍后再试');
        }

        try {
            this.isUploading = true;
            
            // ===== 强制重置所有状态，确保不使用之前的缓存 =====
            this.log('=== 重置上传状态，清理之前的缓存数据 ===');
            this.currentPath = []; // 强制清空路径状态
            this.uploadStats = {
                total: 0,
                completed: 0,
                failed: 0
            };
            
            // 重新初始化上传队列和统计
            this.uploadQueue = uploadData.files || [];
            this.uploadStats = {
                total: this.uploadQueue.length,
                completed: 0,
                failed: 0
            };

            this.log(`=== 开始新的上传流程，共 ${this.uploadStats.total} 个文件 ===`);
            
            // === 立即发送上传开始通知 ===
            chrome.runtime.sendMessage({
                type: 'UPLOAD_START',
                total: this.uploadStats.total,
                files: this.uploadQueue.map(f => ({
                    name: f.fileName || f.name,
                    path: f.path,
                    size: f.size
                })),
                timestamp: Date.now()
            });

            // 0. 强制重新同步当前位置状态（不使用缓存）
            this.log('步骤0：强制重新同步当前位置状态（清理缓存）');
            this.currentPath = []; // 再次确保路径为空
            await this.syncCurrentPath();
            await this.showCurrentLocationInfo();
            
            // 验证当前位置确实被重置了
            this.log(`状态重置完成，当前路径: [${this.currentPath.join(' / ')}]`);

            // 1. 确保在首页 - 增强版导航
            this.log('第一步：强制导航到首页并验证');
            await this.navigateToHome();
            
            // 2. 验证是否真的在首页，如果不是则重试
            let homeVerified = false;
            let verifyAttempts = 0;
            const maxVerifyAttempts = 3;
            
            while (!homeVerified && verifyAttempts < maxVerifyAttempts) {
                verifyAttempts++;
                this.log(`验证首页状态，第 ${verifyAttempts}/${maxVerifyAttempts} 次`);
                
                if (await this.isAtHomePage()) {
                    homeVerified = true;
                    this.log('首页验证成功');
                    // 首页验证成功后同步路径
                    await this.syncCurrentPath();
                } else {
                    this.log(`首页验证失败，重新导航 (第 ${verifyAttempts} 次)`);
                    await this.navigateToHome();
                    await this.sleep(2000); // 额外等待时间
                }
            }
            
            if (!homeVerified) {
                throw new Error('无法确保在首页状态，上传流程终止');
            }
            
            // 3. 等待页面完全稳定
            this.log('等待页面完全稳定');
            await this.sleep(2000);
            await this.waitForFileList();
            
            // 4. 创建或进入当日文件夹
            const todayFolder = this.getTodayFolderName();
            this.log(`第二步：确保当日文件夹存在: ${todayFolder}`);
            await this.ensureTodayFolder(todayFolder);
            
            // 5. 分析文件结构，按文件夹分组
            this.log('第三步：分析文件结构');
            const filesByFolder = this.groupFilesByFolder(this.uploadQueue);
            this.log('文件夹分组结果:', filesByFolder);
            
            // 6. 按文件夹结构上传文件
            this.log('第四步：开始按文件夹结构上传');
            await this.processFilesByFolderStructure(filesByFolder);
            
            // 7. 上传完成，返回首页
            await this.navigateToHome();
            
            this.log('上传流程完成', this.uploadStats);
            
            // === 发送最终完成通知 ===
            chrome.runtime.sendMessage({
                type: 'UPLOAD_COMPLETE',
                total: this.uploadStats.total,
                completed: this.uploadStats.completed,
                failed: this.uploadStats.failed,
                success: this.uploadStats.failed === 0,
                timestamp: Date.now()
            });
            
            // 直接返回成功结果，不再嵌套success字段
            return {
                total: this.uploadStats.total,
                completed: this.uploadStats.completed,
                failed: this.uploadStats.failed,
                uploadStats: this.uploadStats,
                message: `上传流程已完成，成功: ${this.uploadStats.completed}, 失败: ${this.uploadStats.failed}`
            };
            
        } catch (error) {
            this.log('上传失败', error);
            
            // === 发送失败通知 ===
            chrome.runtime.sendMessage({
                type: 'UPLOAD_FAILED',
                error: error.message,
                stats: this.uploadStats,
                timestamp: Date.now()
            });
            
            // 抛出错误，让消息监听器的catch处理
            throw new Error(`上传失败: ${error.message}`);
        } finally {
            this.isUploading = false;
            
            // ===== 最终清理：确保状态完全重置，为下次上传做准备 =====
            this.log('=== 上传流程结束，进行最终状态清理 ===');
            this.currentPath = []; // 清空路径状态
            this.uploadQueue = []; // 清空上传队列
            this.log('状态清理完成，系统已准备好进行下次上传');
        }
    }

    // 按文件夹分组文件
    groupFilesByFolder(files) {
        const groups = {};
        
        for (const file of files) {
            const pathParts = file.path.split('/');
            const fileName = pathParts[pathParts.length - 1];
            const folderPath = pathParts.slice(0, -1).join('/');
            
            if (!groups[folderPath]) {
                groups[folderPath] = [];
            }
            
            groups[folderPath].push({
                ...file,
                fileName: fileName,
                folderPath: folderPath
            });
        }
        
        this.log(`文件分组完成，共 ${Object.keys(groups).length} 个文件夹`);
        return groups;
    }

    // 按文件夹结构处理上传 - 修复版：移除不必要的根目录返回逻辑
    async processFilesByFolderStructure(filesByFolder) {
        // 按文件夹深度排序，确保先创建父文件夹
        const sortedFolders = Object.keys(filesByFolder).sort((a, b) => {
            const depthA = a.split('/').filter(Boolean).length;
            const depthB = b.split('/').filter(Boolean).length;
            return depthA - depthB;
        });
        
        this.log(`文件夹处理顺序（按深度排序）:`, sortedFolders);
        
        for (let folderIndex = 0; folderIndex < sortedFolders.length; folderIndex++) {
            const folderPath = sortedFolders[folderIndex];
            const files = filesByFolder[folderPath];
            
            this.log(`\n=== 处理文件夹 ${folderIndex + 1}/${sortedFolders.length} ===`);
            this.log(`文件夹路径: "${folderPath}"`);
            this.log(`包含文件数: ${files.length}`);
            this.log(`文件列表: ${files.map(f => f.fileName).join(', ')}`);
            
            // === 继续文件夹处理逻辑 ===
            try {
                // 如果是根目录文件（folderPath为空字符串）
                if (folderPath === '') {
                    this.log('处理根目录文件（在今日文件夹内）');
                    // 确保在今日文件夹
                    const todayFolder = this.getTodayFolderName();
                    await this.ensureTodayFolder(todayFolder);
                } else {
                    // 确保在目标文件夹（今日文件夹内的子文件夹）
                    this.log(`确保在目标文件夹: ${folderPath}`);
                    await this.ensureInTargetFolder(folderPath);
                }
                
                // 上传该文件夹中的所有文件
                this.log(`开始上传文件夹中的 ${files.length} 个文件`);
                
                for (let fileIndex = 0; fileIndex < files.length; fileIndex++) {
                    const file = files[fileIndex];
                    this.log(`\n📁 文件夹: ${folderPath || '今日文件夹根目录'} - 上传文件 ${fileIndex + 1}/${files.length}: ${file.fileName}`);
                    
                    try {
                        // === 立即开始上传文件 ===
                        await this.uploadFile(file);
                        this.log(`✅ 文件上传成功: ${file.fileName}`);
                        
                        // === 发送成功通知（可选，用于UI反馈） ===
                        chrome.runtime.sendMessage({
                            type: 'FILE_UPLOAD_SUCCESS',
                            filename: file.fileName,
                            filePath: file.path || file.fileName,
                            folderPath: folderPath,
                            currentIndex: fileIndex + 1,
                            totalInFolder: files.length,
                            timestamp: Date.now()
                        });
                        
                    } catch (uploadError) {
                        this.log(`❌ 文件上传失败: ${file.fileName}, 错误: ${uploadError.message}`);
                        
                        // === 发送失败通知（可选，用于UI反馈） ===
                        chrome.runtime.sendMessage({
                            type: 'FILE_UPLOAD_ERROR',
                            filename: file.fileName,
                            filePath: file.path || file.fileName,
                            folderPath: folderPath,
                            error: uploadError.message,
                            currentIndex: fileIndex + 1,
                            totalInFolder: files.length,
                            timestamp: Date.now()
                        });
                        
                        // 继续处理下一个文件，而不是中断整个流程
                        continue;
                    }
                    
                    // 文件间的短暂间隔，让状态更新有时间处理
                    await this.sleep(300);
                }
                
                this.log(`文件夹 ${folderIndex + 1}/${sortedFolders.length} 处理完成: "${folderPath}"`);
                
            } catch (folderError) {
                this.log(`处理文件夹失败: "${folderPath}", 错误: ${folderError.message}`);
                
                // 记录错误但继续处理下一个文件夹
                if (this.onFolderProcessError) {
                    this.onFolderProcessError(folderPath, folderError.message);
                }
                
                // 继续处理下一个文件夹
                continue;
            }
        }
        
        this.log('\n=== 所有文件夹处理完成 ===');
        
        // === 修复：最终返回根目录，确保下次操作的正确起点 ===
        this.log('最终返回根目录...');
        try {
            await this.returnToRoot();
            this.currentPath = [];
            this.log('已返回根目录，上传流程完成');
        } catch (error) {
            this.log(`最终返回根目录失败: ${error.message}`);
            // 不抛出错误，因为上传已经完成
        }
    }

    // 确保在目标文件夹中 - 优化版：明确所有操作都在今日文件夹内部
    async ensureInTargetFolder(targetFolderPath) {
        this.log(`确保在目标文件夹: ${targetFolderPath}（位于今日文件夹内）`);
        
        // ===== 强制重新同步路径状态 =====
        this.log('重新同步路径状态（确保目标文件夹前）');
        await this.syncCurrentPath();
        
        // 获取今日文件夹名称
        const todayFolder = this.getTodayFolderName();
        
        if (!targetFolderPath || targetFolderPath === '') {
            this.log('目标位置：今日文件夹根目录');
            
            // 检查是否已在今日文件夹
            if (this.isAtExpectedPath([todayFolder])) {
                this.log('已在今日文件夹根目录');
                return;
            }
            
            // 确保进入今日文件夹
            await this.ensureTodayFolder(todayFolder);
            return;
        }
        
        // 构建完整的期待路径：今日文件夹 + 用户的目标路径
        const pathParts = targetFolderPath.split('/').filter(part => part);
        const fullExpectedPath = [todayFolder, ...pathParts];
        
        this.log(`完整期待路径: [${fullExpectedPath.join(' / ')}]`);
        this.log(`路径组成: 今日文件夹"${todayFolder}" + 用户文件夹"${targetFolderPath}"`);
        
        // 检查是否已经在正确位置
        if (this.isAtExpectedPath(fullExpectedPath)) {
            this.log('已在目标文件夹位置');
            this.currentPath = [...fullExpectedPath];
            return;
        }
        
        // 确保从今日文件夹开始
        this.log(`第一步：确保在今日文件夹 "${todayFolder}"`);
        await this.ensureTodayFolder(todayFolder);
        
        // 逐级创建和进入用户的文件夹结构
        this.log(`第二步：在今日文件夹内创建用户文件夹结构`);
        for (let i = 0; i < pathParts.length; i++) {
            const folderName = pathParts[i];
            const currentExpectedPath = [todayFolder, ...pathParts.slice(0, i + 1)];
            
            this.log(`处理用户文件夹层级 ${i + 1}/${pathParts.length}: "${folderName}"`);
            this.log(`期待到达路径: [${currentExpectedPath.join(' / ')}]`);
            
            // 检查是否已在目标层级
            if (this.isAtExpectedPath(currentExpectedPath)) {
                this.log(`已在第${i + 1}层目标位置`);
                continue;
            }
            
            // 检查文件夹是否存在
            const exists = await this.checkFolderExists(folderName);
            
            if (!exists) {
                this.log(`用户文件夹不存在，创建: "${folderName}"`);
                await this.createFolder(folderName);
                await this.sleep(1000);
            }
            
            // 进入文件夹
            this.log(`进入用户文件夹: "${folderName}"`);
            await this.enterFolder(folderName);
            
            // 等待到达期待路径
            const reached = await this.waitForExpectedPath(currentExpectedPath);
            if (!reached) {
                throw new Error(`无法到达期待路径: ${currentExpectedPath.join(' / ')}`);
            }
            
            this.log(`成功到达用户文件夹第${i + 1}层: "${folderName}"`);
        }
        
        // 更新内部路径状态
        this.currentPath = [...fullExpectedPath];
        this.log(`目标文件夹确保完成，当前路径: [${this.currentPath.join(' / ')}]`);
    }
    
    // 验证文件夹进入是否成功 - 新方法
    async verifyFolderEntered(folderName, beforeUrl) {
        this.log(`验证是否成功进入文件夹: ${folderName}`);
        
        let verificationAttempts = 0;
        const maxVerificationAttempts = 10;
        
        while (verificationAttempts < maxVerificationAttempts) {
            verificationAttempts++;
            
            // 检查URL是否发生变化（表明导航确实发生了）
            const currentUrl = window.location.href;
            const urlChanged = currentUrl !== beforeUrl;
            
            // 检查面包屑或路径指示器
            const breadcrumbIndicatesCorrectLocation = await this.checkBreadcrumbContains(folderName);
            
            // 检查文件列表是否已更新（与进入前不同）
            const fileListUpdated = await this.checkFileListUpdated();
            
            // 检查页面是否不再显示加载状态
            const notLoading = !this.hasLoadingIndicators();
            
            this.log(`文件夹进入验证 ${verificationAttempts}/${maxVerificationAttempts}:`);
            this.log(`- URL变化: ${urlChanged}`);
            this.log(`- 面包屑正确: ${breadcrumbIndicatesCorrectLocation}`);
            this.log(`- 文件列表已更新: ${fileListUpdated}`);
            this.log(`- 非加载状态: ${notLoading}`);
            
            // 成功条件：至少满足2个条件
            const successConditions = [urlChanged, breadcrumbIndicatesCorrectLocation, fileListUpdated, notLoading];
            const successCount = successConditions.filter(Boolean).length;
            
            if (successCount >= 2) {
                this.log(`文件夹进入验证成功 (满足${successCount}/4个条件)`);
                return;
            }
            
            this.log(`文件夹进入验证未通过，继续等待... (满足${successCount}/4个条件)`);
            await this.sleep(800);
        }
        
        this.log(`警告：文件夹进入验证超时，但继续执行 (文件夹: ${folderName})`);
    }
    
    // 检查面包屑是否包含指定文件夹名 - 新方法
    async checkBreadcrumbContains(folderName) {
        const breadcrumbSelectors = [
            '.breadcrumb',
            '.nav-path', 
            '.file-path',
            '.path-nav',
            '[class*="breadcrumb"]',
            '[class*="path"]',
            '.crumb-path',
            '.location-path'
        ];
        
        for (const selector of breadcrumbSelectors) {
            const breadcrumb = document.querySelector(selector);
            if (breadcrumb) {
                const breadcrumbText = breadcrumb.textContent.trim();
                if (breadcrumbText.includes(folderName)) {
                    this.log(`面包屑包含文件夹名: ${breadcrumbText}`);
                    return true;
                }
            }
        }
        
        return false;
    }
    
    // 检查文件列表是否已更新 - 新方法
    async checkFileListUpdated() {
        // 简单检查：确保文件列表不为空或有空状态提示
        const fileItems = document.querySelectorAll('.file-item, .list-item, .file-card');
        const emptyState = document.querySelector('.empty-state, .no-files, [class*="empty"]');
        
        return fileItems.length > 0 || emptyState !== null;
    }
    
    // 检查是否有加载指示器 - 新方法  
    hasLoadingIndicators() {
        const loadingSelectors = [
            '.loading',
            '.spinner',
            '.skeleton',
            '[class*="loading"]',
            '[class*="spinner"]',
            '[class*="skeleton"]'
        ];
        
        for (const selector of loadingSelectors) {
            const loadingElements = document.querySelectorAll(selector);
            if (Array.from(loadingElements).some(el => el.offsetParent !== null)) {
                return true;
            }
        }
        
        return false;
    }
    
    // 验证最终位置是否正确 - 新方法
    async verifyFinalLocation(expectedPath) {
        this.log(`验证最终位置是否正确: ${expectedPath}`);
        
        // 构建预期的完整路径（包括今日文件夹）
        const todayFolder = this.getTodayFolderName();
        const fullExpectedPath = expectedPath ? `${todayFolder}/${expectedPath}` : todayFolder;
        const currentPathString = this.currentPath.join('/');
        
        this.log(`预期路径: ${fullExpectedPath}`);
        this.log(`当前路径: ${currentPathString}`);
        
        if (currentPathString === fullExpectedPath) {
            this.log('最终位置验证成功');
            return true;
        } else {
            this.log('警告：最终位置可能不正确，但继续执行');
            return false;
        }
    }

    // 获取今日文件夹名称（格式：YYYY-M-D）
    getTodayFolderName() {
        const today = new Date();
        const year = today.getFullYear();
        const month = today.getMonth() + 1; // 月份从0开始
        const day = today.getDate();
        return `${year}-${month}-${day}`;
    }

    // 导航到首页 - 增强版，强制导航
    async navigateToHome() {
        this.log('开始强制导航到首页');
        
        // ===== 强制重置路径状态 =====
        this.log('重置路径状态为空（导航到首页）');
        this.currentPath = [];
        
        const currentUrl = window.location.href;
        this.log(`当前URL: ${currentUrl}`);
        
        // 强制导航策略：不依赖复杂判断，直接执行导航
        this.log('执行强制首页导航');
        
        // 方法1: 直接URL导航到根目录
        try {
            // 构建根目录URL的多种可能格式
            const baseUrl = currentUrl.split('#')[0].split('?')[0];
            const possibleHomeUrls = [
                `${baseUrl}#/home/<USER>
                `${baseUrl}#/home/<USER>
                `${baseUrl}?id=0`,
                `${baseUrl}#/file`,
                baseUrl
            ];
            
            for (const homeUrl of possibleHomeUrls) {
                this.log(`尝试导航到: ${homeUrl}`);
                
                try {
                    window.location.href = homeUrl;
                    await this.sleep(2000);
                    
                    // 检查导航是否成功
                    if (await this.isAtHomePage()) {
                        this.log('URL导航成功');
                        return;
                    }
                } catch (urlError) {
                    this.log(`URL ${homeUrl} 导航失败: ${urlError.message}`);
                    continue;
                }
            }
        } catch (error) {
            this.log(`URL导航方法失败: ${error.message}`);
        }
        
        // 方法2: 查找并点击首页相关元素
        this.log('尝试通过点击元素导航到首页');
        
        // 更全面的首页元素选择器
        const homeElementSelectors = [
            // 面包屑中的根目录链接
            '.breadcrumb a:first-child',
            '.breadcrumb span:first-child',
            '.nav-path a:first-child',
            '.file-path a:first-child',
            
            // 首页按钮
            '[title="首页"]',
            '[title="全部文件"]',
            '[aria-label*="首页"]',
            '[aria-label*="全部文件"]',
            '.home-btn',
            '.all-files-btn',
            
            // 侧边栏导航
            '.sidebar [data-id="0"]',
            '.nav-menu [data-type="home"]',
            '.file-nav .home',
            
            // 其他可能的首页元素
            '[data-action="home"]',
            '[data-route="home"]',
            '.nav-home'
        ];
        
        for (const selector of homeElementSelectors) {
            const elements = document.querySelectorAll(selector);
            for (const element of elements) {
                if (element && element.offsetParent !== null && !element.disabled) {
                    this.log(`找到首页元素: ${selector}, 文本: "${element.textContent.trim()}"`);
                    
                    try {
                        element.click();
                        await this.sleep(1500);
                        
                        if (await this.isAtHomePage()) {
                            this.log('通过点击元素导航成功');
                            return;
                        }
                    } catch (clickError) {
                        this.log(`点击元素失败: ${clickError.message}`);
                    }
                }
            }
        }
        
        // 方法3: 通过文本内容查找首页链接
        this.log('通过文本内容查找首页链接');
        const homeTexts = ['首页', '全部文件', '根目录', '我的文件', 'Home', 'All Files'];
        const clickableElements = document.querySelectorAll('a, button, span[onclick], [role="button"]');
        
        for (const element of clickableElements) {
            const text = element.textContent.trim();
            if (homeTexts.some(homeText => text.includes(homeText)) && 
                element.offsetParent !== null) {
                this.log(`通过文本找到首页元素: "${text}"`);
                
                try {
                    element.click();
                    await this.sleep(1500);
                    
                    if (await this.isAtHomePage()) {
                        this.log('通过文本导航成功');
                        return;
                    }
                } catch (textClickError) {
                    this.log(`通过文本点击失败: ${textClickError.message}`);
                }
            }
        }
        
        // 方法4: 键盘快捷键（如果支持）
        this.log('尝试键盘快捷键');
        try {
            // 尝试Ctrl+Home或其他可能的快捷键
            document.dispatchEvent(new KeyboardEvent('keydown', {
                key: 'Home',
                ctrlKey: true,
                bubbles: true
            }));
            await this.sleep(1000);
            
            if (await this.isAtHomePage()) {
                this.log('键盘快捷键导航成功');
                return;
            }
        } catch (keyError) {
            this.log(`键盘快捷键失败: ${keyError.message}`);
        }
        
        // 方法5: 最后的强制刷新
        this.log('所有方法失败，执行强制页面刷新');
        try {
            const baseUrl = window.location.origin + window.location.pathname;
            window.location.href = baseUrl;
            await this.sleep(3000);
            this.log('强制刷新完成');
        } catch (refreshError) {
            this.log(`强制刷新失败: ${refreshError.message}`);
            throw new Error('无法导航到首页，所有方法都失败了');
        }
    }
    
    // 检查是否在首页 - 简化版，使用新的路径检测
    async isAtHomePage() {
        // 等待页面稳定
        await this.sleep(500);
        
        const currentPath = this.getCurrentSimplePath();
        
        // 首页判断：路径为空或者只包含根目录标识
        const isHome = currentPath.length === 0;
        
        this.log(`首页检查结果: 当前路径=[${currentPath.join(' / ')}], 是否首页=${isHome}`);
        
        return isHome;
    }

    // 确保当日文件夹存在 - 简化版，使用路径比对
    async ensureTodayFolder(folderName) {
        this.log(`确保文件夹存在: ${folderName}`);
        
        // ===== 强制重新同步路径状态 =====
        this.log('重新同步路径状态（确保当日文件夹前）');
        await this.syncCurrentPath();
        
        // === 修复：检查是否在正确的当日文件夹层级 ===
        const currentPath = this.getCurrentSimplePath();
        
        // 检查是否已在正确的当日文件夹（一级目录）
        if (currentPath.length === 1 && currentPath[0] === folderName) {
            this.log(`已在正确的当日文件夹层级: ${folderName}`);
            this.currentPath = [folderName];
            return;
        }
        
        // === 修复：检查是否在错误的层级（如二级或更深层级） ===
        if (currentPath.length > 1 && currentPath[0] === folderName) {
            this.log(`检测到在错误的层级: [${currentPath.join(' / ')}]，需要返回到正确层级`);
            this.log(`当前在 ${currentPath.length} 级目录，期望在 1 级目录`);
            
            // 通过面包屑导航返回到正确的层级
            try {
                this.log('尝试通过面包屑导航返回到一级当日文件夹');
                
                // 查找面包屑中的当日文件夹链接（第一级）
                const breadcrumbItems = document.querySelectorAll('nz-breadcrumb-item');
                
                for (const item of breadcrumbItems) {
                    const nameElement = item.querySelector('.breadcrumb-name');
                    if (nameElement && nameElement.textContent.trim() === folderName) {
                        this.log(`找到当日文件夹面包屑链接: ${folderName}`);
                        
                        // 点击该面包屑项目返回到一级目录
                        nameElement.click();
                        this.log('已点击当日文件夹面包屑，等待导航');
                        
                        // 等待导航完成
                        await this.sleep(1500);
                        await this.waitForFileList();
                        
                        // 重新同步路径
                        await this.syncCurrentPath();
                        
                        // 验证是否到达正确层级
                        const newPath = this.getCurrentSimplePath();
                        if (newPath.length === 1 && newPath[0] === folderName) {
                            this.log(`成功返回到正确的当日文件夹层级: [${newPath.join(' / ')}]`);
                            this.currentPath = [folderName];
                            return; // === 修复：成功后直接返回，避免重复操作 ===
                        } else {
                            this.log(`面包屑导航后路径仍不正确: [${newPath.join(' / ')}]`);
                        }
                        break;
                    }
                }
                
                // 如果面包屑导航失败，尝试返回根目录重新进入
                this.log('面包屑导航失败，尝试返回根目录重新进入');
                await this.returnToRoot();
                
                // 重新进入当日文件夹
                if (await this.checkFolderExists(folderName)) {
                    await this.enterFolder(folderName);
                    
                    // 等待并验证
                    const reached = await this.waitForExpectedPath([folderName]);
                    if (reached) {
                        this.currentPath = [folderName];
                        this.log(`通过重新进入成功到达当日文件夹: ${folderName}`);
                        return; // === 修复：成功后直接返回，避免重复操作 ===
                    }
                }
                
            } catch (error) {
                this.log(`层级修正失败: ${error.message}`);
                // === 修复：层级修正失败时，不再继续执行原有逻辑，直接抛出错误 ===
                throw new Error(`无法修正当日文件夹层级: ${error.message}`);
            }
        }
        
        // === 原有逻辑：文件夹不存在或其他情况的处理 ===
        // === 修复：执行前再次确认是否真的需要处理 ===
        this.log('执行原有逻辑前，最终确认当前状态');
        await this.syncCurrentPath();
        const finalCurrentPath = this.getCurrentSimplePath();
        
        if (finalCurrentPath.length === 1 && finalCurrentPath[0] === folderName) {
            this.log(`最终确认：已在正确的当日文件夹位置: [${finalCurrentPath.join(' / ')}]`);
            this.currentPath = [folderName];
            return; // === 修复：如果已在正确位置，直接返回，避免重复操作 ===
        }
        
        this.log(`最终确认：需要执行原有逻辑，当前路径: [${finalCurrentPath.join(' / ')}]`);
        
        try {
            // 检查文件夹是否已存在
            if (await this.checkFolderExists(folderName)) {
                this.log(`文件夹 ${folderName} 已存在，直接进入`);
                await this.enterFolder(folderName);
                
                // === 修复：进入文件夹后，增加严格的路径验证和纠正 ===
                this.log('进入文件夹后，开始路径验证和纠正');
                
                // 等待导航完成
                await this.sleep(1500);
                await this.waitForFileList();
                
                // 强制重新同步路径状态
                await this.syncCurrentPath();
                let verifyPath = this.getCurrentSimplePath();
                
                this.log(`进入后的初始路径: [${verifyPath.join(' / ')}]`);
                
                // 如果路径出现重复，进行纠正
                if (verifyPath.length > 1 && verifyPath[0] === folderName && verifyPath[1] === folderName) {
                    this.log(`检测到路径重复: [${verifyPath.join(' / ')}]，进行纠正`);
                    
                    // 方法1：通过面包屑点击纠正到正确层级
                    const breadcrumbItems = document.querySelectorAll('nz-breadcrumb-item');
                    let corrected = false;
                    
                    for (let i = 0; i < breadcrumbItems.length; i++) {
                        const nameElement = breadcrumbItems[i].querySelector('.breadcrumb-name');
                        if (nameElement && nameElement.textContent.trim() === folderName) {
                            // 点击第一个匹配的面包屑项（正确的层级）
                            this.log(`点击第${i+1}个面包屑项进行路径纠正: ${folderName}`);
                            nameElement.click();
                            
                            await this.sleep(1500);
                            await this.waitForFileList();
                            await this.syncCurrentPath();
                            
                            verifyPath = this.getCurrentSimplePath();
                            this.log(`纠正后的路径: [${verifyPath.join(' / ')}]`);
                            
                            if (verifyPath.length === 1 && verifyPath[0] === folderName) {
                                this.log('路径纠正成功');
                                corrected = true;
                                break;
                            }
                        }
                    }
                    
                    // 如果面包屑纠正失败，强制设置正确路径
                    if (!corrected) {
                        this.log('面包屑纠正失败，强制设置正确路径');
                        this.currentPath = [folderName];
                        verifyPath = [folderName];
                    }
                }
                
                // 最终验证
                if (verifyPath.length === 1 && verifyPath[0] === folderName) {
                    this.currentPath = [folderName];
                    this.log(`成功进入当日文件夹，最终路径: [${this.currentPath.join(' / ')}]`);
                } else {
                    this.log(`警告：路径可能不正确，但继续执行。当前路径: [${verifyPath.join(' / ')}]`);
                    this.currentPath = [folderName]; // 强制设置为期望路径
                }
                
            } else {
                this.log(`文件夹 ${folderName} 不存在，开始创建`);
                await this.createFolder(folderName);
                await this.sleep(1000);
                
                // 进入创建的文件夹
                await this.enterFolder(folderName);
                
                // === 修复：创建文件夹后，同样增加严格的路径验证和纠正 ===
                this.log('创建文件夹后，开始路径验证和纠正');
                
                // 等待导航完成
                await this.sleep(1500);
                await this.waitForFileList();
                
                // 强制重新同步路径状态
                await this.syncCurrentPath();
                let verifyPath = this.getCurrentSimplePath();
                
                this.log(`创建并进入后的初始路径: [${verifyPath.join(' / ')}]`);
                
                // 如果路径出现重复，进行纠正
                if (verifyPath.length > 1 && verifyPath[0] === folderName && verifyPath[1] === folderName) {
                    this.log(`检测到路径重复: [${verifyPath.join(' / ')}]，进行纠正`);
                    
                    // 方法1：通过面包屑点击纠正到正确层级
                    const breadcrumbItems = document.querySelectorAll('nz-breadcrumb-item');
                    let corrected = false;
                    
                    for (let i = 0; i < breadcrumbItems.length; i++) {
                        const nameElement = breadcrumbItems[i].querySelector('.breadcrumb-name');
                        if (nameElement && nameElement.textContent.trim() === folderName) {
                            // 点击第一个匹配的面包屑项（正确的层级）
                            this.log(`点击第${i+1}个面包屑项进行路径纠正: ${folderName}`);
                            nameElement.click();
                            
                            await this.sleep(1500);
                            await this.waitForFileList();
                            await this.syncCurrentPath();
                            
                            verifyPath = this.getCurrentSimplePath();
                            this.log(`纠正后的路径: [${verifyPath.join(' / ')}]`);
                            
                            if (verifyPath.length === 1 && verifyPath[0] === folderName) {
                                this.log('路径纠正成功');
                                corrected = true;
                                break;
                            }
                        }
                    }
                    
                    // 如果面包屑纠正失败，强制设置正确路径
                    if (!corrected) {
                        this.log('面包屑纠正失败，强制设置正确路径');
                        this.currentPath = [folderName];
                        verifyPath = [folderName];
                    }
                }
                
                // 最终验证
                if (verifyPath.length === 1 && verifyPath[0] === folderName) {
                    this.currentPath = [folderName];
                    this.log(`成功创建并进入当日文件夹，最终路径: [${this.currentPath.join(' / ')}]`);
                } else {
                    this.log(`警告：路径可能不正确，但继续执行。当前路径: [${verifyPath.join(' / ')}]`);
                    this.currentPath = [folderName]; // 强制设置为期望路径
                }
            }
            
        } catch (error) {
            this.log(`当日文件夹处理失败: ${error.message}`);
            throw error;
        }
    }

    // 检查文件夹是否存在
    async checkFolderExists(folderName) {
        this.log(`检查文件夹是否存在: ${folderName}`);
        
        await this.waitForFileList();
        
        // 多种文件夹元素选择器
        const itemSelectors = [
            '.file-item',
            '.list-item', 
            '.file-card',
            '.folder-item',
            '.item',
            '[data-type="folder"]',
            '[data-item-type="folder"]',
            '.file-row',
            '.grid-item'
        ];
        
        let allItems = [];
        
        // 收集所有可能的文件项
        for (const selector of itemSelectors) {
            const items = document.querySelectorAll(selector);
            if (items.length > 0) {
                allItems = allItems.concat(Array.from(items));
                this.log(`通过选择器 ${selector} 找到 ${items.length} 个项目`);
            }
        }
        
        // 去重
        allItems = Array.from(new Set(allItems));
        this.log(`总共找到 ${allItems.length} 个唯一项目`);
        
        if (allItems.length === 0) {
            this.log('未找到任何文件项，可能是空目录或页面未加载完成');
            return false;
        }
        
        // 检查每个项目
        for (const element of allItems) {
            // 多种名称元素选择器
            const nameSelectors = [
                '.file-name',
                '.item-name',
                '.name',
                '.filename',
                '.title',
                '.file-title',
                '[data-name]',
                '.name-text',
                '.file-name-text'
            ];
            
            let nameElement = null;
            let nameText = '';
            
            // 查找名称元素
            for (const nameSelector of nameSelectors) {
                nameElement = element.querySelector(nameSelector);
                if (nameElement) {
                    nameText = nameElement.textContent.trim();
                    if (nameText) {
                        break;
                    }
                }
            }
            
            // 如果没找到名称元素，尝试从元素自身获取
            if (!nameText) {
                nameText = element.textContent.trim();
                // 取第一行或前50个字符作为名称
                const lines = nameText.split('\n');
                nameText = lines[0].substring(0, 50).trim();
            }
            
            // 从data属性获取名称
            if (!nameText && element.dataset.name) {
                nameText = element.dataset.name.trim();
            }
            
            this.log(`检查项目: "${nameText}"`);
            
            // 名称匹配检查
            if (nameText === folderName) {
                // 检查是否为文件夹类型
                const folderIndicators = [
                    // 类名检查
                    () => element.classList.contains('folder'),
                    () => element.classList.contains('directory'),
                    () => element.classList.contains('dir'),
                    () => element.classList.contains('folder-item'),
                    
                    // 图标检查
                    () => element.querySelector('.folder-icon, .icon-folder, .fa-folder, .folder'),
                    () => element.querySelector('[class*="folder"]'),
                    () => element.querySelector('svg[data-icon*="folder"]'),
                    
                    // 属性检查
                    () => element.dataset.type === 'folder',
                    () => element.dataset.itemType === 'folder',
                    () => element.getAttribute('data-file-type') === 'folder',
                    
                    // 文本内容检查（某些系统会显示"文件夹"字样）
                    () => element.textContent.includes('文件夹') && !element.textContent.includes('个文件'),
                    
                    // 没有文件扩展名（简单的启发式检查）
                    () => !nameText.includes('.') || nameText.endsWith('.'),
                ];
                
                let isFolderCount = 0;
                let folderReasons = [];
                
                for (let i = 0; i < folderIndicators.length; i++) {
                    try {
                        if (folderIndicators[i]()) {
                            isFolderCount++;
                            folderReasons.push(`指示器${i + 1}`);
                        }
                    } catch (e) {
                        // 忽略检查错误
                    }
                }
                
                this.log(`项目 "${nameText}" 的文件夹指示器数量: ${isFolderCount}, 原因: ${folderReasons.join(', ')}`);
                
                // 如果有任何文件夹指示器，或者名称不包含扩展名，认为是文件夹
                if (isFolderCount > 0 || (!nameText.includes('.') && nameText.length > 0)) {
                    this.log(`找到文件夹: ${folderName}`);
                    return true;
                }
            }
        }
        
        this.log(`未找到文件夹: ${folderName}`);
        
        // 记录所有找到的项目名称（用于调试）
        const allNames = allItems.map(item => {
            const nameElement = item.querySelector('.file-name, .item-name, .name, .filename, .title');
            return nameElement ? nameElement.textContent.trim() : item.textContent.trim().split('\n')[0].substring(0, 30);
        }).filter(name => name);
        
        this.log(`当前目录中的所有项目: ${allNames.join(', ')}`);
        
        return false;
    }

    // 创建文件夹
    async createFolder(folderName) {
        this.log(`=== 开始创建文件夹: ${folderName} ===`);
        
        // 找到新建按钮
        this.log('步骤1: 查找新建按钮');
        const newButtonSelectors = [
            '.new-btn',
            '.create-folder-btn',
            '.create-btn',
            '.add-btn',
            '.new-folder',
            'button[title*="新建"]',
            'button[aria-label*="新建"]',
            '[data-action="new"]',
            '[data-action="create"]'
        ];
        
        let newButton = null;
        for (const selector of newButtonSelectors) {
            const buttons = document.querySelectorAll(selector);
            this.log(`检查选择器 ${selector}: 找到 ${buttons.length} 个按钮`);
            
            for (const button of buttons) {
                if (button && button.offsetParent !== null && !button.disabled) {
                    newButton = button;
                    this.log(`✓ 找到可用的新建按钮: ${selector}, 文本: "${button.textContent.trim()}"`);
                    break;
                }
            }
            if (newButton) break;
        }
        
        if (!newButton) {
            // 尝试通过文本内容查找
            this.log('通过选择器未找到，尝试通过文本内容查找新建按钮');
            const buttons = Array.from(document.querySelectorAll('button, [role="button"]'));
            this.log(`页面共有 ${buttons.length} 个按钮元素`);
            
            for (const btn of buttons) {
                const text = btn.textContent?.trim() || '';
                const isVisible = btn.offsetParent !== null;
                const isEnabled = !btn.disabled;
                this.log(`检查按钮: "${text}", 可见: ${isVisible}, 启用: ${isEnabled}`);
                
                if (text.includes('新建') && isVisible && isEnabled) {
                    newButton = btn;
                    this.log(`✓ 通过文本找到新建按钮: "${text}"`);
                    break;
                }
            }
        }
        
        if (!newButton) {
            this.log('❌ 新建按钮查找失败');
            this.log('页面中的所有按钮:', Array.from(document.querySelectorAll('button')).map(btn => ({
                text: btn.textContent.trim(),
                className: btn.className,
                id: btn.id,
                visible: btn.offsetParent !== null,
                disabled: btn.disabled
            })));
            throw new Error('未找到新建文件夹按钮');
        }

        // 步骤2: 点击新建按钮
        this.log('步骤2: 点击新建按钮');
        newButton.click();
        this.log('✓ 新建按钮已点击');
        
        // 等待新建菜单出现 - 改善版，避免页面显示问题
        this.log('等待新建菜单出现');
        await this.sleep(300); // 减少初始等待时间
        
        // 轻量级的hover触发，避免过度操作导致页面异常
        this.log('尝试hover触发菜单显示');
        try {
            // 使用更温和的hover触发方式
            const mouseOverEvent = new MouseEvent('mouseover', {
                view: window,
                bubbles: true,
                cancelable: true
            });
            newButton.dispatchEvent(mouseOverEvent);
            
            // 短暂等待，让菜单有时间显示
            await this.sleep(200);
            
            // 检查菜单是否已经显示
            const menuItems = document.querySelectorAll('.select-list-item');
            if (menuItems.length === 0) {
                // 如果菜单未显示，尝试额外的触发方式
                const mouseEnterEvent = new MouseEvent('mouseenter', {
                    view: window,
                    bubbles: true,
                    cancelable: true
                });
                newButton.dispatchEvent(mouseEnterEvent);
                await this.sleep(200);
            }
        } catch (error) {
            this.log(`菜单触发遇到问题: ${error.message}，继续执行`);
        }
        
        // 步骤3: 查找新建文件夹选项 - 简化回归版
        this.log('步骤3: 查找新建文件夹选项');
        
        // 基于实际HTML结构的简单有效选择器
        const createFolderSelectors = [
            // 最直接的选择器 - 基于实际HTML结构
            '.select-list-item', // 主要选择器
            '.add-select-list .select-list-item', // 更具体的路径
            '.new-btn .select-list-item', // 在新建按钮内的选项
            
            // 备用选择器
            '.menu-item',
            '.dropdown-item',
            '.option'
        ];
        
        let createFolderOption = null;
        
        // 简单直接的查找逻辑
        for (const selector of createFolderSelectors) {
            const elements = document.querySelectorAll(selector);
            this.log(`检查选择器 ${selector}: 找到 ${elements.length} 个元素`);
            
            for (const element of elements) {
                const text = (element.textContent || '').trim();
                this.log(`检查元素文本: "${text}"`);
                
                // 简单的文本匹配 - 包含"文件夹"且不是明显的现有文件夹
                if (text.includes('文件夹') && !text.includes('测试文件夹')) {
                    // 找到了文件夹选项，无论是否可见都尝试点击
                    const isVisible = element.offsetParent !== null;
                    this.log(`找到文件夹选项: "${text}", 可见=${isVisible}`);
                    
                    if (isVisible) {
                        createFolderOption = element;
                        this.log(`✓ 找到可见的新建文件夹选项: ${selector}, 文本: "${text}"`);
                        break;
                    } else {
                        // 元素不可见，但我们知道它存在，尝试温和的显示方式
                        this.log(`文件夹选项不可见，尝试温和的显示方式`);
                        
                        // 不修改页面结构，直接使用不可见的元素
                        createFolderOption = element;
                        this.log(`✓ 使用不可见的文件夹选项: "${text}"`);
                        break;
                    }
                }
            }
            
            if (createFolderOption) break;
        }
        
        if (!createFolderOption) {
            this.log('简单选择器未找到，尝试备用方法');
            
            // 备用：查找所有包含"文件夹"的可见元素
            const allElements = document.querySelectorAll('*');
            for (const element of allElements) {
                if (element.offsetParent !== null) {
                    const text = (element.textContent || '').trim();
                    if (text === '文件夹' || text === ' 文件夹 ') { // 精确匹配
                        // 检查是否是可点击元素或在可点击容器中
                        const isClickable = element.tagName === 'BUTTON' || 
                                          element.tagName === 'A' ||
                                          element.onclick ||
                                          element.parentElement?.onclick ||
                                          element.classList.contains('select-list-item');
                        
                        if (isClickable) {
                            createFolderOption = element;
                            this.log(`✓ 通过备用方法找到新建文件夹选项: "${text}"`);
                            break;
                        }
                    }
                }
            }
        }
        
        if (!createFolderOption) {
            this.log('❌ 完全无法找到新建文件夹选项');
            this.log('调试信息: 当前页面的所有 .select-list-item 元素:');
            const debugItems = document.querySelectorAll('.select-list-item');
            Array.from(debugItems).forEach((item, index) => {
                this.log(`  项目${index}: "${item.textContent}", 可见=${item.offsetParent !== null}`);
            });
            
            throw new Error('无法找到新建文件夹选项');
        } else {
            // 步骤4: 点击新建文件夹选项
            this.log('步骤4: 点击新建文件夹选项');
            
            const isVisible = createFolderOption.offsetParent !== null;
            this.log(`准备点击文件夹选项，可见性: ${isVisible}`);
            
            try {
                // 先稍微等待一下，确保菜单完全加载
                await this.sleep(100);
                
                // 检查并确保菜单项是稳定的
                let stableCheck = 0;
                while (stableCheck < 3) {
                    await this.sleep(50);
                    if (createFolderOption.offsetParent !== null || 
                        document.body.contains(createFolderOption)) {
                        stableCheck++;
                    } else {
                        stableCheck = 0;
                    }
                }
                
                if (isVisible) {
                    // 如果可见，使用温和的正常点击
                    this.log('使用正常点击方式');
                    createFolderOption.click();
                    this.log('✓ 正常点击文件夹选项');
                } else {
                    // 如果不可见，尝试优化的强制点击方式
                    this.log('使用强制点击方式（元素不可见）');
                    
                    // 方法1: 尝试让元素可见后再点击
                    try {
                        // createFolderOption.style.visibility = 'visible';
                        // createFolderOption.style.display = 'block';
                        // createFolderOption.style.opacity = '1';
                        await this.sleep(50);
                        
                        if (createFolderOption.offsetParent !== null) {
                            createFolderOption.click();
                            this.log('✓ 元素已可见，正常点击');
                        } else {
                            // 元素不可见，直接尝试点击
                            createFolderOption.click();
                            this.log('✓ 元素不可见，直接点击');
                        }
                    } catch (visibilityError) {
                        this.log(`强制显示失败: ${visibilityError.message}，使用事件派发`);
                        
                        // 方法2: 直接调用click()
                        createFolderOption.click();
                        this.log('已调用click()方法');
                        
                        // 方法3: 派发click事件作为备用
                        const clickEvent = new MouseEvent('click', {
                            view: window,
                            bubbles: true,
                            cancelable: true
                        });
                        createFolderOption.dispatchEvent(clickEvent);
                        this.log('已派发click事件作为备用');
                        
                    }
                }
                
                this.log('✓ 新建文件夹选项已点击');
                
                // 等待文件夹创建对话框出现，使用合理的等待时间
                await this.sleep(400);
                
            } catch (clickError) {
                this.log(`点击文件夹选项失败: ${clickError.message}`);
                throw new Error(`无法点击新建文件夹选项: ${clickError.message}`);
            }
        }

        // 输入文件夹名称 - 增强的输入框查找逻辑
        this.log('步骤5: 查找文件夹名称输入框');
        
        // 优先查找对话框/模态框中的输入框
        const inputSelectors = [
            // 对话框中的输入框 - 优先级最高
            '.modal input[type="text"]',
            '.dialog input[type="text"]',
            '.popup input[type="text"]',
            '.ant-modal input[type="text"]',
            '.el-dialog input[type="text"]',
            
            // 文件夹相关的输入框
            'input[placeholder*="文件夹"]',
            'input[placeholder*="名称"]',
            '.folder-name-input',
            '.name-input',
            
            // 通用文本输入框 - 但排除搜索框
            'input[type="text"]:not([placeholder*="搜索"]):not([placeholder*="search"])'
        ];
        
        let nameInput = null;
        
        // 首先尝试在可见的模态框/对话框中查找
        const activeModalElements = document.querySelectorAll('.modal, .dialog, .popup, [role="dialog"]');
        for (const modal of activeModalElements) {
            if (modal.offsetParent !== null) { // 可见的模态框
                this.log(`在活跃模态框中查找输入框: ${modal.className}`);
                const modalInputs = modal.querySelectorAll('input[type="text"]');
                
                for (const input of modalInputs) {
                    if (input.offsetParent !== null && !input.disabled) {
                        // 排除明确的搜索框
                        const placeholder = (input.placeholder || '').toLowerCase();
                        if (!placeholder.includes('搜索') && !placeholder.includes('search')) {
                            nameInput = input;
                            this.log(`✓ 在模态框中找到输入框: placeholder="${input.placeholder}"`);
                            break;
                        }
                    }
                }
                
                if (nameInput) break;
            }
        }
        
        // 如果模态框中没找到，使用选择器查找
        if (!nameInput) {
            for (const selector of inputSelectors) {
                const inputs = document.querySelectorAll(selector);
                this.log(`检查输入框选择器 ${selector}: 找到 ${inputs.length} 个输入框`);
                
                for (const input of inputs) {
                    if (input.offsetParent !== null && !input.disabled) {
                        // 再次确认不是搜索框
                        const placeholder = (input.placeholder || '').toLowerCase();
                        if (!placeholder.includes('搜索') && !placeholder.includes('search')) {
                            nameInput = input;
                            this.log(`✓ 找到合适的输入框: ${selector}, placeholder="${input.placeholder}"`);
                            break;
                        } else {
                            this.log(`跳过搜索框: placeholder="${input.placeholder}"`);
                        }
                    }
                }
                
                if (nameInput) break;
            }
        }
        
        // 最后备用方案：查找所有可见的文本输入框，但严格排除搜索框
        if (!nameInput) {
            this.log('选择器查找失败，使用备用方案');
            const allInputs = document.querySelectorAll('input[type="text"]');
            
            for (const input of allInputs) {
                if (input.offsetParent !== null && !input.disabled) {
                    const placeholder = (input.placeholder || '').toLowerCase();
                    const parentText = (input.parentElement?.textContent || '').toLowerCase();
                    
                    // 严格排除搜索框
                    const isSearchBox = placeholder.includes('搜索') || 
                                       placeholder.includes('search') ||
                                       parentText.includes('搜索') ||
                                       input.className.includes('search');
                    
                    if (!isSearchBox) {
                        nameInput = input;
                        this.log(`✓ 备用方案找到输入框: placeholder="${input.placeholder}"`);
                        break;
                    }
                }
            }
        }
        
        if (!nameInput) {
            this.log('❌ 输入框查找完全失败');
            
            // 调试信息
            this.log('调试信息: 当前页面的所有可见输入框:');
            const allInputs = document.querySelectorAll('input[type="text"]');
            Array.from(allInputs).forEach((input, index) => {
                if (input.offsetParent !== null) {
                    this.log(`  输入框${index}: placeholder="${input.placeholder}", className="${input.className}"`);
                }
            });
            
            throw new Error('无法找到文件夹名称输入框');
        }

        // 设置文件夹名称
        this.log(`步骤6: 设置文件夹名称: ${folderName}`);
        
        try {
            // 验证输入框是否真的适合输入文件夹名称
            const isValidInput = this.validateFolderNameInput(nameInput);
            if (!isValidInput) {
                this.log('❌ 所选输入框可能不是文件夹名称输入框');
                
                // 尝试再次查找更合适的输入框
                const betterInput = this.findBetterFolderNameInput();
                if (betterInput) {
                    this.log('✓ 找到更合适的输入框');
                    nameInput = betterInput;
                } else {
                    this.log('⚠️ 无法找到更合适的输入框，使用当前输入框');
                }
            }
            
            // 清空并聚焦输入框
            nameInput.value = '';
            nameInput.focus();
            this.log('✓ 输入框已清空并聚焦');
            
            // 设置值
            nameInput.value = folderName;
            this.log(`✓ 文件夹名称已设置: "${folderName}"`);
            
            // 触发多种事件确保值被正确设置
            const events = ['input', 'change', 'keyup', 'blur'];
            for (const eventType of events) {
                nameInput.dispatchEvent(new Event(eventType, { bubbles: true }));
            }
            this.log('✓ 输入事件已触发');
            
        } catch (inputError) {
            this.log(`❌ 设置文件夹名称失败: ${inputError.message}`);
            throw new Error(`设置文件夹名称失败: ${inputError.message}`);
        }

        await this.sleep(500);

        // 确认创建 - 增强的确认按钮查找逻辑
        this.log('步骤7: 查找并点击确认按钮');
        
        const confirmSelectors = [
            // 基本的确认按钮选择器
            '.confirm-btn',
            '.ok-btn', 
            '.submit-btn',
            '.save-btn',
            '.create-btn',
            'button[type="submit"]',
            
            // UI框架的确认按钮
            '.ant-btn-primary',
            '.el-button--primary',
            '.btn-primary'
        ];
        
        let confirmButton = null;
        
        // 首先在可见的模态框中查找确认按钮
        const confirmModalElements = document.querySelectorAll('.modal, .dialog, .popup, [role="dialog"]');
        for (const modal of confirmModalElements) {
            if (modal.offsetParent !== null) {
                this.log(`在活跃模态框中查找确认按钮: ${modal.className}`);
                
                for (const selector of confirmSelectors) {
                    const buttons = modal.querySelectorAll(selector);
                    for (const button of buttons) {
                        if (button.offsetParent !== null && !button.disabled) {
                            confirmButton = button;
                            this.log(`✓ 在模态框中找到确认按钮: ${selector}`);
                            break;
                        }
                    }
                    if (confirmButton) break;
                }
                
                if (confirmButton) break;
            }
        }
        
        // 如果模态框中没找到，尝试全局查找
        if (!confirmButton) {
            for (const selector of confirmSelectors) {
                const buttons = document.querySelectorAll(selector);
                this.log(`检查确认按钮选择器 ${selector}: 找到 ${buttons.length} 个按钮`);
                
                for (const button of buttons) {
                    if (button.offsetParent !== null && !button.disabled) {
                        confirmButton = button;
                        this.log(`✓ 找到确认按钮: ${selector}, 文本: "${button.textContent}"`);
                        break;
                    }
                }
                
                if (confirmButton) break;
            }
        }
        
        // 最后备用方案：查找包含确认文本的按钮
        if (!confirmButton) {
            this.log('选择器查找失败，尝试文本匹配');
            const allButtons = document.querySelectorAll('button');
            
            for (const button of allButtons) {
                if (button.offsetParent !== null && !button.disabled) {
                    const text = (button.textContent || '').trim();
                    if (text === '确认' || text === '确定' || text === '创建' || text === 'OK' || text === 'Create') {
                        confirmButton = button;
                        this.log(`✓ 通过文本找到确认按钮: "${text}"`);
                        break;
                    }
                }
            }
        }
        
        if (!confirmButton) {
            this.log('❌ 无法找到确认按钮');
            
            // 调试信息
            this.log('调试信息: 当前页面的所有可见按钮:');
            const allButtons = document.querySelectorAll('button');
            Array.from(allButtons).slice(0, 10).forEach((button, index) => {
                if (button.offsetParent !== null) {
                    this.log(`  按钮${index}: 文本="${button.textContent}", className="${button.className}"`);
                }
            });
            
            throw new Error('无法找到确认按钮');
        } else {
            this.log('点击确认按钮');
            confirmButton.click();
            this.log('✓ 确认按钮已点击');
        }

        await this.sleep(800);

        await this.waitForFolderCreation(folderName);
        this.log(`文件夹 ${folderName} 创建成功`);
    }
    
    // 验证输入框是否适合输入文件夹名称 - 新增辅助方法
    validateFolderNameInput(input) {
        // 检查placeholder是否相关
        const placeholder = (input.placeholder || '').toLowerCase();
        const relevantPlaceholders = ['文件夹', 'folder', '名称', 'name', '新建', 'create'];
        const hasRelevantPlaceholder = relevantPlaceholders.some(keyword => placeholder.includes(keyword));
        
        // 检查是否为搜索框（应该排除）
        const isSearchBox = placeholder.includes('搜索') || 
                           placeholder.includes('search') ||
                           input.name?.includes('search') ||
                           input.id?.includes('search');
        
        // 检查输入框所在的容器上下文
        const isInDialog = this.isElementInDialog(input);
        
        this.log(`输入框验证: placeholder="${placeholder}", 相关=${hasRelevantPlaceholder}, 搜索框=${isSearchBox}, 在对话框=${isInDialog}`);
        
        // 验证逻辑：必须在对话框中，有相关placeholder，且不是搜索框
        return isInDialog && (hasRelevantPlaceholder || !isSearchBox) && !isSearchBox;
    }
    
    // 检查元素是否在对话框中 - 新增辅助方法
    isElementInDialog(element) {
        const dialogSelectors = [
            '.modal', '.dialog', '.popup', '.overlay',
            '[role="dialog"]', '.ant-modal', '.el-dialog'
        ];
        
        for (const selector of dialogSelectors) {
            const dialog = document.querySelector(selector);
            if (dialog && dialog.offsetParent !== null && dialog.contains(element)) {
                return true;
            }
        }
        
        return false;
    }
    
    // 查找更合适的文件夹名称输入框 - 新增辅助方法
    findBetterFolderNameInput() {
        // 在活跃的对话框中查找最合适的输入框
        const activeDialogs = Array.from(document.querySelectorAll('.modal, .dialog, .popup, [role="dialog"]'))
            .filter(dialog => dialog.offsetParent !== null);
        
        for (const dialog of activeDialogs) {
            const inputs = dialog.querySelectorAll('input[type="text"], input:not([type])');
            
            for (const input of inputs) {
                if (input.offsetParent !== null && 
                    !input.disabled && 
                    !input.readOnly &&
                    this.validateFolderNameInput(input)) {
                    return input;
                }
            }
        }
        
        return null;
    }
    
    // 验证是否为确认按钮 - 新增辅助方法
    isConfirmButton(button, text) {
        const confirmTexts = [
            '确认', '确定', 'OK', 'ok', '创建', '保存', 
            '提交', 'Submit', 'Create', 'Save', 'Confirm'
        ];
        
        // 检查按钮文本
        const hasConfirmText = confirmTexts.some(confirmText => 
            text.includes(confirmText) || text.toLowerCase().includes(confirmText.toLowerCase())
        );
        
        // 检查按钮类型
        const isSubmitType = button.type === 'submit';
        
        // 检查按钮样式类（通常确认按钮有特殊样式）
        const className = button.className || '';
        const hasPrimaryStyle = className.includes('primary') || 
                               className.includes('confirm') ||
                               className.includes('ok') ||
                               className.includes('submit');
        
        // 排除取消类按钮
        const isCancelButton = text.includes('取消') || 
                              text.includes('Cancel') ||
                              className.includes('cancel');
        
        return (hasConfirmText || isSubmitType || hasPrimaryStyle) && !isCancelButton;
    }

    // 等待文件夹创建完成 - 增强日志版
    async waitForFolderCreation(folderName) {
        this.log(`开始等待文件夹创建完成: ${folderName}`);
        
        let attempts = 0;
        const maxAttempts = 20;
        
        while (attempts < maxAttempts) {
            attempts++;
            this.log(`文件夹创建检查第 ${attempts}/${maxAttempts} 次`);
            
            // 检查文件夹是否已经出现
            if (await this.checkFolderExists(folderName)) {
                this.log(`✓ 文件夹 ${folderName} 已成功创建并出现在列表中`);
                return;
            }
            
            // 检查是否有错误提示
            const errorSelectors = [
                '.error-message',
                '.alert-danger',
                '.notification-error',
                '.message-error',
                '[class*="error"]'
            ];
            
            for (const selector of errorSelectors) {
                const errorElement = document.querySelector(selector);
                if (errorElement && errorElement.offsetParent !== null) {
                    const errorText = errorElement.textContent.trim();
                    if (errorText) {
                        this.log(`❌ 检测到错误提示: ${errorText}`);
                        throw new Error(`文件夹创建失败: ${errorText}`);
                    }
                }
            }
            
            // 检查是否还有对话框开着（可能创建还在进行中）
            const modals = document.querySelectorAll('.modal, .dialog, .popup, [role="dialog"]');
            const openModals = Array.from(modals).filter(modal => modal.offsetParent !== null);
            
            if (openModals.length > 0) {
                this.log(`仍有 ${openModals.length} 个对话框开着，文件夹创建可能还在进行中`);
            } else {
                this.log('所有对话框已关闭，但文件夹还未出现');
            }
            
            this.log(`等待文件夹创建... (${attempts}/${maxAttempts})`);
            await this.sleep(500);
        }
        
        // 最终检查失败，提供详细信息
        this.log(`❌ 文件夹创建超时，最后状态检查:`);
        
        // 记录当前目录中的所有项目
        const currentItems = await this.getCurrentDirectoryItems();
        this.log('当前目录中的所有项目:', currentItems);
        
        throw new Error(`文件夹 ${folderName} 创建超时`);
    }
    
    // 获取当前目录中的所有项目 - 新增辅助方法
    async getCurrentDirectoryItems() {
        try {
            await this.waitForFileList();
            
            const itemSelectors = [
                '.file-item',
                '.list-item', 
                '.file-card',
                '.folder-item',
                '.item'
            ];
            
            let allItems = [];
            
            for (const selector of itemSelectors) {
                const items = document.querySelectorAll(selector);
                if (items.length > 0) {
                    allItems = allItems.concat(Array.from(items));
                }
            }
            
            // 去重并获取名称
            const uniqueItems = Array.from(new Set(allItems));
            const itemNames = uniqueItems.map(item => {
                const nameElement = item.querySelector('.file-name, .item-name, .name, .filename, .title');
                return nameElement ? nameElement.textContent.trim() : item.textContent.trim().split('\n')[0].substring(0, 30);
            }).filter(name => name);
            
            return itemNames;
        } catch (error) {
            this.log(`获取当前目录项目失败: ${error.message}`);
            return [];
        }
    }

    // 进入文件夹
    async enterFolder(folderName) {
        this.log(`进入文件夹: ${folderName}`);
        
        const folderElements = document.querySelectorAll('.file-item, .list-item');
        for (const element of folderElements) {
            const nameElement = element.querySelector('.file-name, .item-name');
            if (nameElement && nameElement.textContent.trim() === folderName) {
                // 使用双击进入文件夹（单击是勾选）
                this.log(`进入文件夹: ${folderName}`);
                element.dispatchEvent(new MouseEvent('dblclick', { bubbles: true }));
                await this.waitForNavigation();
                return;
            }
        }
        
        throw new Error(`无法找到文件夹: ${folderName}`);
    }

    // 等待导航完成 - 增强版，更好地处理路径同步
    async waitForNavigation() {
        this.log('开始等待导航完成');
        
        // 1. 基础等待，让页面开始加载
        await this.sleep(1500); // 增加基础等待时间，让面包屑有足够时间更新
        
        // 2. 等待文件列表基本加载
        await this.waitForFileList();
        
        // 3. 基础的交互就绪检测（简化版）
        await this.waitForInteractionReady();
        
        // 4. 路径同步 - 增加更多尝试次数和等待时间
        let pathSyncAttempts = 0;
        const maxPathSyncAttempts = 8; // 增加到8次尝试
        let lastPath = null;
        
        while (pathSyncAttempts < maxPathSyncAttempts) {
            pathSyncAttempts++;
            
            // 强制重新同步路径
            await this.syncCurrentPath();
            
            // 等待面包屑更新 - 增加等待时间
            await this.sleep(500);
            
            // 检查路径是否稳定 - 连续两次检查
            const path1 = this.getCurrentSimplePath();
            await this.sleep(300);
            const path2 = this.getCurrentSimplePath();
            await this.sleep(300);
            const path3 = this.getCurrentSimplePath();
            
            // 如果连续三次路径都一样，认为稳定
            const path1Str = JSON.stringify(path1);
            const path2Str = JSON.stringify(path2);
            const path3Str = JSON.stringify(path3);
            
            if (path1Str === path2Str && path2Str === path3Str) {
                this.log(`路径同步成功，第${pathSyncAttempts}次尝试，最终路径: [${path3.join(' / ')}]`);
                break;
            }
            
            // 如果路径相比上次有变化，说明还在更新中
            if (lastPath !== null && lastPath !== path3Str) {
                this.log(`路径仍在变化，第${pathSyncAttempts}次尝试: ${lastPath} -> ${path3Str}`);
            } else {
                this.log(`路径不稳定，重试第${pathSyncAttempts}次，当前: [${path3.join(' / ')}]`);
            }
            
            lastPath = path3Str;
            
            // 额外等待时间
            await this.sleep(200);
        }
        
        // 5. 显示当前位置信息（仅在调试模式）
        if (this.debug) {
            await this.showCurrentLocationInfo();
        }
        
        this.log('导航等待完成（增强版）');
    }
    
    // 等待页面交互就绪 - 简化版，避免过度等待
    async waitForInteractionReady() {
        this.log('开始等待页面交互功能就绪');
        
        let attempts = 0;
        const maxAttempts = 10; // 减少到5秒最大等待时间
        
        while (attempts < maxAttempts) {
            attempts++;
            
            // 简化的交互元素检查，只要找到任何一个就认为可以交互
            const basicInteractionSelectors = [
                '.new-btn', '.create-btn', '.add-btn',
                'button[title*="新建"]', '[aria-label*="新建"]',
                '.upload-btn', '.upload-area',
                '.file-item', '.list-item'
            ];
            
            let foundInteraction = false;
            for (const selector of basicInteractionSelectors) {
                const elements = document.querySelectorAll(selector);
                const readyElements = Array.from(elements).filter(el => 
                    el.offsetParent !== null && !el.disabled
                );
                
                if (readyElements.length > 0) {
                    this.log(`找到可用的交互元素: ${selector} (${readyElements.length}个)`);
                    foundInteraction = true;
                    break;
                }
            }
            
            // 只要找到任何一个交互元素就认为页面可以交互
            if (foundInteraction) {
                this.log('页面交互功能已基本就绪');
                return;
            }
            
            this.log(`等待页面交互就绪... (第${attempts}/${maxAttempts}次)`);
            await this.sleep(500);
        }
        
        this.log('页面交互等待超时，但继续执行（简化版策略）');
    }
    
    // 获取DOM结构的简单哈希 - 新方法
    getDomHash() {
        try {
            // 获取文件列表区域的关键信息
            const fileItems = document.querySelectorAll('.file-item, .list-item, .file-card');
            const itemCount = fileItems.length;
            
            // 获取按钮状态
            const buttons = document.querySelectorAll('button:not([style*="display: none"])');
            const buttonCount = buttons.length;
            
            // 获取页面主要内容区域的文本长度
            const mainContent = document.querySelector('.main-content, .content, .file-content');
            const contentLength = mainContent ? mainContent.textContent.length : 0;
            
            // 组合成简单的"哈希"
            return `${itemCount}-${buttonCount}-${contentLength}`;
        } catch (error) {
            this.log(`获取DOM哈希失败: ${error.message}`);
            return Date.now().toString(); // 返回时间戳作为后备
        }
    }

    // 等待文件列表加载 - 修复版，找到容器立即返回
    async waitForFileList() {
        this.log('开始等待文件列表加载完成');
        
        let attempts = 0;
        const maxAttempts = 5;
        
        while (attempts < maxAttempts) {
            attempts++;
            this.log(`文件列表加载检查第 ${attempts}/${maxAttempts} 次`);
            
            // 简化的文件列表容器检查
            const listSelectors = [
                '.list-container', '.file-list', '.main-content', '.content-list'
            ];
            
            let listContainer = null;
            for (const selector of listSelectors) {
                const container = document.querySelector(selector);
                if (container && container.offsetParent !== null) {
                    listContainer = container;
                    this.log(`找到文件列表容器: ${selector}`);
                    break;
                }
            }
            
            // 如果找到了容器，立即返回
            if (listContainer) {
                this.log('✓ 文件列表容器已找到，立即返回');
                return;
            }
            
            // 简化的加载状态检查
            const loadingIndicators = document.querySelectorAll('.loading, .spinner');
            const hasLoadingIndicators = Array.from(loadingIndicators).some(
                indicator => indicator.offsetParent !== null
            );
            
            if (hasLoadingIndicators) {
                this.log('检测到加载指示器，继续等待');
                await this.sleep(500);
                continue;
            }
            
            // 如果没有找到容器也没有加载指示器，继续等待
            this.log('未找到文件列表容器，继续等待');
            await this.sleep(500);
        }
        
        this.log('文件列表等待超时，但继续执行');
    }

    // 上传单个文件 - 增强状态更新版本
    async uploadFile(fileItem) {
        const fileName = fileItem.fileName || fileItem.name;
        const filePath = fileItem.path || fileName;
        
        this.log(`\n=== 开始上传文件 ===`);
        this.log(`文件名: ${fileName}`);
        this.log(`文件路径: ${filePath}`);
        this.log(`文件大小: ${fileItem.size || '未知'}`);
        
        // === 立即标记文件为开始上传状态 ===
        this.log(`🔄 立即标记文件为开始上传状态: ${fileName}`);
        try {
            chrome.runtime.sendMessage({
                type: 'UPDATE_FILE_STATUS',
                filename: fileName,
                filePath: filePath,
                status: 'uploading',
                timestamp: Date.now()
            });
            this.log(`✓ 文件状态已标记为uploading: ${fileName}`);
        } catch (statusError) {
            this.log(`⚠️ 状态更新失败但继续上传: ${statusError.message}`);
        }
        
        try {
            // === 新增：文件存在性检查 ===
            this.log(`步骤0: 检查文件是否已存在于目标路径 - ${fileName}`);
            const fileAlreadyExists = await this.checkFileExists(fileName);
            
            if (fileAlreadyExists) {
                this.log(`✓ 文件已存在，跳过上传: ${fileName}`);
                
                // === 立即通知后端文件已存在，直接标记为已上传 ===
                this.log(`📤 立即标记已存在文件为uploaded状态: ${fileName}`);
                chrome.runtime.sendMessage({
                    type: 'UPDATE_FILE_STATUS',
                    filename: fileName,
                    filePath: filePath,
                    status: 'uploaded',
                    skipReason: 'file_already_exists',
                    timestamp: Date.now()
                });
                
                // === 更新上传统计 ===
                this.uploadStats.completed++;
                this.log(`📊 上传统计更新: ${this.uploadStats.completed}/${this.uploadStats.total} 完成`);
                
                this.log(`=== 文件跳过完成（已存在）: ${fileName} ===\n`);
                return; // 直接返回，不执行实际上传
            } else {
                this.log(`✓ 文件不存在，继续上传流程 - ${fileName}`);
            }
            
            // === 原有上传流程（仅在文件不存在时执行）===
            
            // 步骤1: 获取文件数据
            this.log(`步骤1: 获取文件数据 - ${fileName}`);
            const fileData = await this.getFileData(fileItem);
            this.log(`✓ 文件数据获取成功，大小: ${fileData.content?.length || 0} 字节`);
            
            // 步骤2: 模拟文件上传
            this.log(`步骤2: 开始模拟文件上传 - ${fileName}`);
            await this.simulateFileUpload(fileData);
            this.log(`✓ 文件上传模拟完成 - ${fileName}`);
            
            // 步骤3: 等待上传完成
            this.log(`步骤3: 等待上传完成验证 - ${fileName}`);
            await this.waitForUploadComplete(fileName);
            this.log(`✓ 文件上传验证成功 - ${fileName}`);
            
            // === 步骤4: 立即通知后端上传成功 ===
            this.log(`📤 立即标记文件上传成功: ${fileName}`);
            chrome.runtime.sendMessage({
                type: 'UPDATE_FILE_STATUS',
                filename: fileName,
                filePath: filePath,
                status: 'uploaded',
                timestamp: Date.now()
            });
            this.log(`✓ 文件状态已更新为uploaded: ${fileName}`);
            
            // === 更新上传统计 ===
            this.uploadStats.completed++;
            this.log(`📊 上传统计更新: ${this.uploadStats.completed}/${this.uploadStats.total} 完成`);
            
            // === 可选：发送进度更新 ===
            chrome.runtime.sendMessage({
                type: 'UPLOAD_PROGRESS',
                completed: this.uploadStats.completed,
                total: this.uploadStats.total,
                currentFile: fileName,
                timestamp: Date.now()
            });
            
            this.log(`=== 文件上传流程完成: ${fileName} ===\n`);
            
        } catch (error) {
            this.log(`=== 文件上传失败: ${fileName} ===`);
            this.log(`错误信息: ${error.message}`);
            this.log(`错误堆栈: ${error.stack}`);
            
            // === 立即通知后端上传失败 ===
            this.log(`❌ 立即标记文件上传失败: ${fileName}`);
            try {
                chrome.runtime.sendMessage({
                    type: 'UPDATE_FILE_STATUS',
                    filename: fileName,
                    filePath: filePath,
                    status: 'error',
                    error: error.message,
                    timestamp: Date.now()
                });
                this.log(`✓ 文件状态已更新为error: ${fileName}`);
            } catch (statusError) {
                this.log(`❌ 状态更新失败: ${statusError.message}`);
            }
            
            // === 更新失败统计 ===
            this.uploadStats.failed++;
            this.log(`📊 失败统计更新: ${this.uploadStats.failed} 个文件失败`);
            
            throw error;
        }
    }

    // 获取文件数据 - 增强日志版
    async getFileData(fileItem) {
        const fileName = fileItem.name || fileItem.fileName;
        const filePath = fileItem.path || fileName;
        
        this.log(`开始获取文件数据: ${fileName} (路径: ${filePath})`);
        
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                this.log(`❌ 获取文件数据超时: ${fileName}`);
                reject(new Error(`获取文件数据超时: ${fileName}`));
            }, 15000); // 15秒超时
            
            this.log(`向后台发送GET_FILE_DATA请求: ${fileName}`);
            
            // === 发送文件数据请求状态更新 ===
            chrome.runtime.sendMessage({
                type: 'UPDATE_FILE_STATUS',
                filename: fileName,
                filePath: filePath,
                status: 'fetching_data',
                timestamp: Date.now()
            });
            
            chrome.runtime.sendMessage({
                type: 'GET_FILE_DATA',
                filename: fileName,
                path: filePath
            }, (response) => {
                clearTimeout(timeout);
                
                this.log(`收到后台响应 (${fileName}):`, response);
                
                if (chrome.runtime.lastError) {
                    const error = `Chrome通信错误: ${chrome.runtime.lastError.message}`;
                    this.log(`❌ ${error}`);
                    
                    // 标记文件为错误状态
                    chrome.runtime.sendMessage({
                        type: 'UPDATE_FILE_STATUS',
                        filename: fileName,
                        filePath: filePath,
                        status: 'error',
                        error: error,
                        timestamp: Date.now()
                    });
                    
                    reject(new Error(error));
                    return;
                }
                
                if (!response) {
                    const error = '没有收到文件数据响应';
                    this.log(`❌ ${error}`);
                    
                    // 标记文件为错误状态
                    chrome.runtime.sendMessage({
                        type: 'UPDATE_FILE_STATUS',
                        filename: fileName,
                        filePath: filePath,
                        status: 'error',
                        error: error,
                        timestamp: Date.now()
                    });
                    
                    reject(new Error(error));
                    return;
                }
                
                if (response.success && response.data) {
                    this.log(`✓ 文件数据获取成功: ${fileName}`);
                    this.log(`- 文件大小: ${response.data.content?.length || 0} 字节`);
                    this.log(`- MIME类型: ${response.data.mimeType || '未知'}`);
                    
                    // 验证文件数据
                    if (!response.data.content) {
                        const error = `文件内容为空: ${fileName}`;
                        this.log(`❌ ${error}`);
                        
                        // 标记文件为错误状态
                        chrome.runtime.sendMessage({
                            type: 'UPDATE_FILE_STATUS',
                            filename: fileName,
                            filePath: filePath,
                            status: 'error',
                            error: error,
                            timestamp: Date.now()
                        });
                        
                        reject(new Error(error));
                        return;
                    }
                    
                    // === 标记文件数据获取成功 ===
                    chrome.runtime.sendMessage({
                        type: 'UPDATE_FILE_STATUS',
                        filename: fileName,
                        filePath: filePath,
                        status: 'data_ready',
                        dataSize: response.data.content?.length || 0,
                        timestamp: Date.now()
                    });
                    
                    resolve(response.data);
                } else {
                    const error = response.error || '未知的文件数据获取错误';
                    this.log(`❌ 文件数据获取失败: ${fileName} - ${error}`);
                    
                    // 标记文件为错误状态
                    chrome.runtime.sendMessage({
                        type: 'UPDATE_FILE_STATUS',
                        filename: fileName,
                        filePath: filePath,
                        status: 'error',
                        error: error,
                        timestamp: Date.now()
                    });
                    
                    reject(new Error(`获取文件数据失败: ${error}`));
                }
            });
        });
    }

    // 模拟文件上传 - 增强日志版
    async simulateFileUpload(fileData) {
        this.log(`开始模拟文件上传: ${fileData.name}`);
        
        try {
            // 确保文件数据是正确的格式
            let fileContent;
            if (typeof fileData.content === 'string') {
                this.log(`文件数据格式: 字符串 (长度: ${fileData.content.length})`);
                // 如果是base64字符串，需要转换
                if (fileData.content.startsWith('data:')) {
                    // 去掉data URL前缀
                    const base64Data = fileData.content.split(',')[1];
                    fileContent = Uint8Array.from(atob(base64Data), c => c.charCodeAt(0));
                    this.log(`转换data URL为二进制数据，大小: ${fileContent.length} 字节`);
                } else {
                    // 直接的base64字符串
                    fileContent = Uint8Array.from(atob(fileData.content), c => c.charCodeAt(0));
                    this.log(`转换base64为二进制数据，大小: ${fileContent.length} 字节`);
                }
            } else if (fileData.content instanceof ArrayBuffer) {
                fileContent = new Uint8Array(fileData.content);
                this.log(`文件数据格式: ArrayBuffer，大小: ${fileContent.length} 字节`);
            } else {
                // 其他情况，尝试直接使用
                fileContent = fileData.content;
                this.log(`文件数据格式: 其他类型，直接使用`);
            }
            
            // 创建文件对象
            const file = new File([fileContent], fileData.name, {
                type: fileData.mimeType || 'application/octet-stream',
                lastModified: Date.now()
            });
            
            this.log(`✓ 文件对象创建成功: ${file.name} (大小: ${file.size} 字节, 类型: ${file.type})`);
            
            // 尝试多种上传方法
            this.log(`方法1: 尝试文件输入框上传`);
            await this.tryFileInputUpload(file);
            
            this.log(`方法2: 尝试拖放上传`);
            await this.tryDragDropUpload(file);
            
            this.log(`方法3: 尝试点击上传按钮`);
            await this.tryClickUpload(file);
            
            this.log(`✓ 文件上传模拟完成: ${fileData.name}`);
            
        } catch (error) {
            this.log(`❌ 文件上传模拟失败: ${fileData.name} - ${error.message}`);
            throw error;
        }
    }
    
    // 尝试文件输入框上传
    async tryFileInputUpload(file) {
        this.log('尝试文件输入框上传');
        
        let fileInput = document.querySelector('input[type="file"]');
        if (!fileInput) {
            this.log('未找到现有文件输入框，创建新的隐藏输入框');
            fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.style.display = 'none';
            fileInput.setAttribute('accept', '*/*');
            document.body.appendChild(fileInput);
        }

        try {
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            fileInput.files = dataTransfer.files;

            // 触发各种事件
            const events = ['change', 'input'];
            for (const eventType of events) {
                fileInput.dispatchEvent(new Event(eventType, { bubbles: true }));
                await this.sleep(100);
            }
            
            this.log('文件输入框事件已触发');
        } catch (error) {
            this.log(`文件输入框上传失败: ${error.message}`);
        }
    }
    
    // 尝试拖放上传
    async tryDragDropUpload(file) {
        this.log('尝试拖放上传');
        
        // 查找可能的拖放目标
        const dropTargets = [
            '.upload-box',
            '.upload-area', 
            '.drop-zone',
            '.file-drop',
            '[data-upload]',
            '.upload-btn'
        ];
        
        let targetElement = null;
        for (const selector of dropTargets) {
            targetElement = document.querySelector(selector);
            if (targetElement) {
                this.log(`找到拖放目标: ${selector}`);
                break;
            }
        }
        
        if (!targetElement) {
            this.log('未找到拖放目标，使用document.body');
            targetElement = document.body;
        }

        try {
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);

            const events = [
                { type: 'dragenter', cancelable: true },
                { type: 'dragover', cancelable: true },
                { type: 'drop', cancelable: false }
            ];
            
            for (const eventInfo of events) {
                const event = new DragEvent(eventInfo.type, {
                    bubbles: true,
                    cancelable: eventInfo.cancelable,
                    dataTransfer: dataTransfer
                });
                
                if (eventInfo.cancelable) {
                    event.preventDefault();
                }
                
                targetElement.dispatchEvent(event);
                await this.sleep(200);
            }
            
            this.log('拖放事件已触发');
        } catch (error) {
            this.log(`拖放上传失败: ${error.message}`);
        }
    }
    
    // 尝试点击上传
    async tryClickUpload(file) {
        this.log('尝试点击上传');
        
        const uploadButtons = [
            '.upload-btn',
            '.upload-button', 
            '.btn-upload',
            '[title*="上传"]',
            '[aria-label*="上传"]'
        ];
        
        for (const selector of uploadButtons) {
            const button = document.querySelector(selector);
            if (button) {
                this.log(`找到上传按钮: ${selector}`);
                try {
                    button.click();
                    await this.sleep(500);
                    
                    // 再次尝试文件输入框
                    await this.tryFileInputUpload(file);
                    break;
                } catch (error) {
                    this.log(`点击上传按钮失败: ${error.message}`);
                }
            }
        }
    }

    // 等待上传完成 - 优化版，减少等待时间
    async waitForUploadComplete(fileName) {
        this.log(`开始等待文件上传完成: ${fileName}`);
        
        let attempts = 0;
        const maxAttempts = 20; // 从60次减少到20次，最多等待10秒
        
        while (attempts < maxAttempts) {
            attempts++;
            this.log(`上传完成检查第 ${attempts}/${maxAttempts} 次 - ${fileName}`);
            
            // 检查文件是否出现在文件列表中
            if (await this.checkFileExists(fileName)) {
                this.log(`✓ 文件 ${fileName} 已出现在文件列表中，上传完成`);
                return;
            }
            
            // 检查是否有上传进度或错误提示
            const progressElement = document.querySelector('.upload-progress, .progress-bar');
            const errorElement = document.querySelector('.upload-error, .error-message');
            
            if (progressElement) {
                this.log(`检测到上传进度指示器: ${progressElement.textContent}`);
            }
            
            if (errorElement && errorElement.textContent.includes(fileName)) {
                const errorMsg = `文件 ${fileName} 上传失败: ${errorElement.textContent}`;
                this.log(`❌ ${errorMsg}`);
                throw new Error(errorMsg);
            }
            
            await this.sleep(500);
        }
        
        this.log(`⚠️ 文件 ${fileName} 上传验证超时（10秒），但可能已成功上传`);
        // 不再抛出错误，而是给出警告继续执行
        // throw new Error(`文件 ${fileName} 上传超时`);
    }

    // 检查文件是否存在 - 增强版，支持更多文件项选择器
    async checkFileExists(fileName) {
        this.log(`检查文件是否存在: ${fileName}`);
        await this.waitForFileList();
        
        // 扩展的文件元素选择器列表
        const fileSelectors = [
            '.file-item',
            '.list-item',
            '.file-card',
            '.file-row',
            '.grid-item',
            '.item',
            '.document-item',
            '[data-type="file"]',
            '[data-item-type="file"]',
            '.file-list-item',
            '.file-entry'
        ];
        
        let allFileElements = [];
        
        // 收集所有可能的文件元素
        for (const selector of fileSelectors) {
            const elements = document.querySelectorAll(selector);
            if (elements.length > 0) {
                allFileElements = allFileElements.concat(Array.from(elements));
                this.log(`通过选择器 ${selector} 找到 ${elements.length} 个元素`);
            }
        }
        
        // 去重
        allFileElements = Array.from(new Set(allFileElements));
        this.log(`总共找到 ${allFileElements.length} 个唯一文件元素`);
        
        if (allFileElements.length === 0) {
            this.log('未找到任何文件元素，可能是空目录或页面未加载完成');
            return false;
        }
        
        // 检查每个元素是否匹配目标文件名
        for (const element of allFileElements) {
            // 多种文件名获取策略
            const nameStrategies = [
                // 通过特定的文件名类选择器
                () => element.querySelector('.file-name, .item-name, .name, .filename, .title')?.textContent?.trim(),
                
                // 通过data属性
                () => element.dataset.name || element.dataset.filename || element.dataset.title,
                
                // 通过title属性
                () => element.title,
                
                // 通过aria-label
                () => element.getAttribute('aria-label'),
                
                // 直接从元素文本内容中提取（作为最后的备选）
                () => {
                    const text = element.textContent?.trim();
                    // 简单启发式：如果文本看起来像文件名（包含扩展名），使用它
                    if (text && text.includes('.') && text.length < 200) {
                        return text;
                    }
                    return null;
                }
            ];
            
            for (const strategy of nameStrategies) {
                try {
                    const extractedName = strategy();
                    if (extractedName) {
                        this.log(`检查文件名: "${extractedName}" vs "${fileName}"`);
                        
                        // 精确匹配
                        if (extractedName === fileName) {
                            this.log(`✓ 找到匹配的文件: ${fileName}`);
                            return true;
                        }
                        
                        // 模糊匹配（处理可能的格式差异）
                        if (extractedName.toLowerCase() === fileName.toLowerCase()) {
                            this.log(`✓ 找到匹配的文件（忽略大小写）: ${fileName}`);
                            return true;
                        }
                        
                        // 检查是否文件名是extractedName的一部分（处理可能的显示截断）
                        if (extractedName.includes(fileName) || fileName.includes(extractedName)) {
                            this.log(`✓ 找到部分匹配的文件: ${fileName} <-> ${extractedName}`);
                            return true;
                        }
                        
                        // 只检查第一个有效策略的结果
                        break;
                    }
                } catch (e) {
                    // 忽略单个策略的错误，继续下一个
                    continue;
                }
            }
        }
        
        this.log(`未找到文件: ${fileName}`);
        return false;
    }

    // 工具函数：延时
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 获取当前真实位置 - 新方法，解析面包屑路径
    async getCurrentLocation() {
        this.log('开始解析当前真实位置');
        
        // 1. 从面包屑解析路径
        const breadcrumbPath = await this.parseBreadcrumbPath();
        if (breadcrumbPath) {
            this.log(`从面包屑解析的路径: ${breadcrumbPath.join(' / ')}`);
            return breadcrumbPath;
        }
        
        // 2. 从URL解析路径（备用方法）
        const urlPath = await this.parseUrlPath();
        if (urlPath) {
            this.log(`从URL解析的路径: ${urlPath.join(' / ')}`);
            return urlPath;
        }
        
        // 3. 从页面标题解析路径（最后备用）
        const titlePath = await this.parseTitlePath();
        if (titlePath) {
            this.log(`从页面标题解析的路径: ${titlePath.join(' / ')}`);
            return titlePath;
        }
        
        this.log('无法解析当前位置，可能在根目录');
        return [];
    }
    
    // 解析面包屑路径 - 新方法
    async parseBreadcrumbPath() {
        const breadcrumbSelectors = [
            '.breadcrumb',
            '.nav-path', 
            '.file-path',
            '.path-nav',
            '[class*="breadcrumb"]',
            '[class*="path"]',
            '.crumb-path',
            '.location-path'
        ];
        
        let breadcrumbElement = null;
        let foundSelector = '';
        
        // 查找面包屑元素
        for (const selector of breadcrumbSelectors) {
            breadcrumbElement = document.querySelector(selector);
            if (breadcrumbElement && breadcrumbElement.offsetParent !== null) {
                foundSelector = selector;
                this.log(`找到面包屑元素: ${selector}`);
                break;
            }
        }
        
        if (!breadcrumbElement) {
            this.log('未找到面包屑元素');
            return null;
        }
        
        // === 修复：使用与getCurrentSimplePath相同的正确选择器 ===
        this.log('=== 面包屑HTML结构分析 ===');
        this.log('面包屑完整HTML:', breadcrumbElement.outerHTML);
        this.log('面包屑完整文本内容:', breadcrumbElement.textContent);
        
        // === 修复：使用正确的选择器 nz-breadcrumb-item .breadcrumb-name ===
        const breadcrumbNameElements = breadcrumbElement.querySelectorAll('nz-breadcrumb-item .breadcrumb-name');
        this.log(`使用正确选择器找到 ${breadcrumbNameElements.length} 个 .breadcrumb-name 元素`);
        
        if (breadcrumbNameElements.length > 0) {
            const linkPath = [];
            
            breadcrumbNameElements.forEach((nameEl, index) => {
                const linkText = nameEl.textContent?.trim();
                this.log(`面包屑元素[${index}]: "${linkText}", 可见=${nameEl.offsetParent !== null}`);
                
                if (linkText && 
                    !this.isHomeIndicator(linkText) && 
                    linkText !== '/' && 
                    linkText !== '>' &&
                    linkText !== '»') {
                    linkPath.push(linkText);
                    this.log(`  → 添加到路径: "${linkText}"`);
                } else {
                    this.log(`  → 跳过: "${linkText}"`);
                }
            });
            
            if (linkPath.length > 0) {
                this.log(`从面包屑正确解析的路径: [${linkPath.join(' / ')}]`);
                return linkPath;
            }
        }
        
        // === 移除原有的错误选择器逻辑，因为它会导致重复 ===
        this.log('未能通过正确选择器解析路径，返回null');
        return null;
    }
    
    // 判断是否为首页指示器 - 新方法
    isHomeIndicator(text) {
        const homeIndicators = [
            '首页', '全部文件', '根目录', '我的文件', 'Home', 'All Files', 
            'Root', 'Files', '文件', '主页', '根文件夹', 'My Files'
        ];
        return homeIndicators.some(indicator => text.includes(indicator));
    }
    
    // 从URL解析路径 - 新方法  
    async parseUrlPath() {
        const currentUrl = window.location.href;
        this.log(`尝试从URL解析路径: ${currentUrl}`);
        
        // 查找URL中的文件夹ID或路径参数
        const urlPatterns = [
            /[?&]id=(\d+)/,
            /[?&]folderId=(\d+)/,
            /[?&]path=([^&]+)/,
            /#\/file\/([^?]+)/,
            /\/folder\/([^?]+)/
        ];
        
        for (const pattern of urlPatterns) {
            const match = currentUrl.match(pattern);
            if (match && match[1] !== '0') {
                this.log(`从URL提取到路径信息: ${match[1]}`);
                // 这里可以根据需要进一步解析ID为路径名
                // 暂时返回null，让面包屑方法处理
                return null;
            }
        }
        
        return null;
    }
    
    // 从页面标题解析路径 - 新方法
    async parseTitlePath() {
        const pageTitle = document.title;
        this.log(`尝试从页面标题解析路径: "${pageTitle}"`);
        
        // 页面标题通常格式为 "文件夹名 - 云盘" 或 "路径 | 云盘"
        const titlePatterns = [
            /^([^-|]+)\s*[-|]/,  // "文件夹名 - 云盘" 或 "文件夹名 | 云盘"
            /([^-|]+)$/          // 如果没有分隔符，取整个标题
        ];
        
        for (const pattern of titlePatterns) {
            const match = pageTitle.match(pattern);
            if (match) {
                const titlePath = match[1].trim();
                if (titlePath && !this.isHomeIndicator(titlePath) && titlePath !== '云盘') {
                    this.log(`从页面标题提取路径: ${titlePath}`);
                    return [titlePath];
                }
            }
        }
        
        return null;
    }

    // 同步当前路径状态 - 简化版，使用新的路径获取方法
    async syncCurrentPath() {
        this.log('开始同步当前路径状态');
        
        // ===== 强制重新获取真实路径，不依赖内部状态 =====
        this.log('强制重新获取当前真实路径');
        
        const realLocation = this.getCurrentSimplePath();
        
        if (realLocation && realLocation.length > 0) {
            // 更新内部路径状态
            const oldPath = this.currentPath.join('/');
            this.currentPath = [...realLocation];
            const newPath = this.currentPath.join('/');
            
            this.log(`路径同步: "${oldPath}" → "${newPath}"`);
        } else {
            // 如果无法解析路径，可能在根目录
            if (this.currentPath.length > 0) {
                this.log(`路径同步: "${this.currentPath.join('/')}" → "(根目录)"`);
                this.currentPath = [];
            } else {
                this.log('路径状态确认：当前在根目录');
            }
        }
        
        // ===== 验证路径状态 =====
        this.log(`路径同步完成，最终路径: [${this.currentPath.join(' / ')}]`);
        
        return this.currentPath;
    }
    
    // 显示当前位置信息 - 新方法，用于调试和用户反馈
    async showCurrentLocationInfo() {
        this.log('=== 当前位置详细信息 ===');
        
        // 获取并显示真实位置
        const realLocation = await this.getCurrentLocation();
        this.log(`真实位置: ${realLocation ? realLocation.join(' / ') : '(根目录)'}`);
        
        // 显示内部记录的路径
        this.log(`内部路径: ${this.currentPath.length > 0 ? this.currentPath.join(' / ') : '(根目录)'}`);
        
        // 显示URL信息
        this.log(`当前URL: ${window.location.href}`);
        
        // 显示页面标题
        this.log(`页面标题: ${document.title}`);
        
        // 显示面包屑原始内容
        const breadcrumbSelectors = ['.breadcrumb', '.nav-path', '.file-path'];
        for (const selector of breadcrumbSelectors) {
            const breadcrumb = document.querySelector(selector);
            if (breadcrumb) {
                this.log(`面包屑(${selector}): "${breadcrumb.textContent.trim()}"`);
                break;
            }
        }
        
        // 检查路径一致性
        const pathMatches = this.checkPathConsistency(realLocation);
        this.log(`路径一致性: ${pathMatches ? '✓ 一致' : '✗ 不一致'}`);
        
        this.log('=== 位置信息结束 ===');
        
        return {
            realLocation: realLocation || [],
            internalPath: [...this.currentPath],
            url: window.location.href,
            title: document.title,
            pathMatches
        };
    }
    
    // 检查路径一致性 - 新方法
    checkPathConsistency(realLocation) {
        if (!realLocation) realLocation = [];
        
        if (realLocation.length !== this.currentPath.length) {
            return false;
        }
        
        for (let i = 0; i < realLocation.length; i++) {
            if (realLocation[i] !== this.currentPath[i]) {
                return false;
            }
        }
        
        return true;
    }

    // 获取当前简化路径 - 直接从面包屑提取，不做复杂解析
    getCurrentSimplePath() {
        this.log('获取当前简化路径');
        
        try {
            // 查找面包屑容器
            const breadcrumbItems = document.querySelectorAll('nz-breadcrumb-item .breadcrumb-name');
            
            if (breadcrumbItems.length === 0) {
                this.log('未找到面包屑项');
                return [];
            }
            
            const pathParts = [];
            for (const item of breadcrumbItems) {
                const pathText = item.textContent.trim();
                if (pathText && pathText !== '全部文件') {  // 跳过"全部文件"这个根目录标识
                    pathParts.push(pathText);
                }
            }
            
            this.log(`当前简化路径: [${pathParts.join(' / ')}]`);
            return pathParts;
            
        } catch (error) {
            this.log(`获取路径失败: ${error.message}`);
            return [];
        }
    }

    // 检查是否在期待路径 - 简单直接的路径比对
    isAtExpectedPath(expectedPath) {
        // === 修复：使用当前简化路径进行比对，而不是内部路径状态 ===
        const currentPath = this.getCurrentSimplePath();
        
        // 标准化期望路径
        let expectedPathArray = [];
        if (typeof expectedPath === 'string') {
            expectedPathArray = expectedPath ? expectedPath.split('/').filter(p => p.trim()) : [];
        } else if (Array.isArray(expectedPath)) {
            expectedPathArray = expectedPath.filter(p => p && p.trim());
        }
        
        this.log(`路径比对（修复版）:`);
        this.log(`- 当前简化路径: [${currentPath.join(' / ')}] (${currentPath.length}层)`);
        this.log(`- 期望路径: [${expectedPathArray.join(' / ')}] (${expectedPathArray.length}层)`);
        this.log(`- 内部路径状态: [${this.currentPath.join(' / ')}] (${this.currentPath.length}层)`);
        
        // 路径长度必须相等
        if (currentPath.length !== expectedPathArray.length) {
            this.log(`路径长度不匹配: ${currentPath.length} vs ${expectedPathArray.length}`);
            return false;
        }
        
        // 逐层比对路径
        for (let i = 0; i < currentPath.length; i++) {
            if (currentPath[i] !== expectedPathArray[i]) {
                this.log(`路径第${i+1}层不匹配: "${currentPath[i]}" vs "${expectedPathArray[i]}"`);
                return false;
            }
        }
        
        this.log('✓ 路径完全匹配（修复版）');
        return true;
    }

    // 等待到达期待路径 - 优化版，减少等待时间
    async waitForExpectedPath(expectedPath, maxAttempts = 6) {  // 从10次减少到6次，总共3秒
        this.log(`等待到达期待路径: [${Array.isArray(expectedPath) ? expectedPath.join(' / ') : expectedPath}]`);
        
        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            this.log(`路径检查第 ${attempt}/${maxAttempts} 次`);
            
            if (this.isAtExpectedPath(expectedPath)) {
                this.log('已到达期待路径');
                return true;
            }
            
            await this.sleep(500);  // 保持500ms检查间隔
        }
        
        this.log('等待期待路径超时（3秒）');
        return false;
    }

    // 检查元素是否在文件列表中 - 新增辅助方法
    isElementInFileList(element) {
        // 检查元素是否在文件列表容器内
        const fileListSelectors = [
            '.file-list',
            '.list-container', 
            '.file-list-content',
            '.grid-container',
            '.files-container',
            '[data-file-list]'
        ];
        
        for (const selector of fileListSelectors) {
            const container = document.querySelector(selector);
            if (container && container.contains(element)) {
                return true;
            }
        }
        
        // 检查元素是否具有文件项的特征
        const fileItemIndicators = [
            'file-item',
            'list-item', 
            'file-card',
            'folder-item',
            'grid-item'
        ];
        
        const elementClasses = element.className || '';
        for (const indicator of fileItemIndicators) {
            if (elementClasses.includes(indicator)) {
                return true;
            }
        }
        
        // 检查父元素是否为文件项
        let parent = element.parentElement;
        let depth = 0;
        while (parent && depth < 5) { // 最多检查5层父元素
            const parentClasses = parent.className || '';
            for (const indicator of fileItemIndicators) {
                if (parentClasses.includes(indicator)) {
                    return true;
                }
            }
            parent = parent.parentElement;
            depth++;
        }
        
        return false;
    }
    
    // 检查元素是否为真正的新建文件夹选项 - 增强版
    isNewFolderOption(element, text) {
        if (!element || !text) return false;
        
        const normalizedText = text.toLowerCase().trim();
        
        // 1. 必须包含"文件夹"相关词汇
        const hasFolderKeyword = normalizedText.includes('文件夹') || 
                               normalizedText.includes('folder') ||
                               normalizedText.includes('新建文件夹') ||
                               normalizedText.includes('创建文件夹');
        
        if (!hasFolderKeyword) return false;
        
        // 2. 排除明显的现有文件夹项目
        const isExistingFolder = normalizedText.includes('测试文件夹') ||
                               normalizedText.includes('下载文件夹') ||
                               normalizedText.includes('上传文件夹') ||
                               /^\d{4}.*文件夹$/.test(normalizedText) || // 如 "2024文件夹"
                               normalizedText.length > 20; // 太长的可能是现有文件夹名
        
        if (isExistingFolder) return false;
        
        // 3. 检查是否在菜单上下文中（新建选项通常在菜单中）
        const inMenuContext = this.isInMenuContext(element);
        
        // 4. 检查是否具有操作特征
        const hasActionCharacteristics = this.hasActionCharacteristics(element);
        
        // 5. 检查元素的属性和类名
        const hasCreateAttributes = element.getAttribute('data-action') === 'create-folder' ||
                                   element.getAttribute('data-type') === 'new-folder' ||
                                   element.classList.contains('create-folder') ||
                                   element.classList.contains('new-folder') ||
                                   element.classList.contains('folder-create');
        
        // 6. 检查标题属性
        const hasFolderTitle = element.title?.toLowerCase().includes('文件夹') ||
                             element.getAttribute('aria-label')?.toLowerCase().includes('文件夹');
        
        // 7. 特殊情况：如果文本就是"文件夹"且在菜单中，很可能是新建选项
        const isSimpleFolderInMenu = normalizedText === '文件夹' && inMenuContext;
        
        // 8. 检查父元素上下文
        const parentElement = element.parentElement;
        const hasNewCreateParent = parentElement && (
            parentElement.classList.contains('new-menu') ||
            parentElement.classList.contains('create-menu') ||
            parentElement.classList.contains('dropdown-menu') ||
            parentElement.textContent?.toLowerCase().includes('新建')
        );
        
        // 综合判断
        const score = [
            hasFolderKeyword,           // 必须有
            !isExistingFolder,          // 必须不是现有文件夹
            inMenuContext,              // 在菜单中加分
            hasActionCharacteristics,   // 有操作特征加分
            hasCreateAttributes,        // 有创建属性加分
            hasFolderTitle,            // 标题相关加分
            isSimpleFolderInMenu,      // 简单"文件夹"文本在菜单中加分
            hasNewCreateParent         // 父元素相关加分
        ].filter(Boolean).length;
        
        // 必须满足基本条件，并且至少有3个加分项
        const isValid = hasFolderKeyword && !isExistingFolder && score >= 3;
        
        this.log(`新建文件夹选项验证: "${normalizedText}" - 得分=${score}/8, 有效=${isValid}`);
        this.log(`  - 包含文件夹词汇: ${hasFolderKeyword}`);
        this.log(`  - 非现有文件夹: ${!isExistingFolder}`);
        this.log(`  - 在菜单中: ${inMenuContext}`);
        this.log(`  - 有操作特征: ${hasActionCharacteristics}`);
        this.log(`  - 有创建属性: ${hasCreateAttributes}`);
        this.log(`  - 有文件夹标题: ${hasFolderTitle}`);
        this.log(`  - 简单文件夹菜单项: ${isSimpleFolderInMenu}`);
        this.log(`  - 有新建父元素: ${hasNewCreateParent}`);
        
        return isValid;
    }
    
    // 检查元素是否在菜单上下文中 - 新增辅助方法
    isInMenuContext(element) {
        const menuIndicators = [
            'menu', 'dropdown', 'popup', 'context', 'option', 'action'
        ];
        
        // 检查元素本身的类名
        const elementClasses = (element.className || '').toLowerCase();
        for (const indicator of menuIndicators) {
            if (elementClasses.includes(indicator)) {
                return true;
            }
        }
        
        // 检查父级容器
        let parent = element.parentElement;
        let depth = 0;
        while (parent && depth < 3) {
            const parentClasses = (parent.className || '').toLowerCase();
            for (const indicator of menuIndicators) {
                if (parentClasses.includes(indicator)) {
                    return true;
                }
            }
            parent = parent.parentElement;
            depth++;
        }
        
        return false;
    }
    
    // 检查元素是否具有操作特征 - 新增辅助方法
    hasActionCharacteristics(element) {
        // 检查是否为可点击元素
        const isClickable = element.tagName === 'BUTTON' || 
                           element.tagName === 'A' ||
                           element.getAttribute('role') === 'button' ||
                           element.onclick !== null ||
                           element.style.cursor === 'pointer';
        
        // 检查是否有操作相关的属性
        const hasActionAttributes = element.getAttribute('data-action') !== null ||
                                   element.getAttribute('data-type') !== null ||
                                   element.getAttribute('onclick') !== null;
        
        // 检查是否在下拉菜单或对话框中最近出现
        const isRecentlyVisible = this.isRecentlyAddedToDOM(element);
        
        return isClickable || hasActionAttributes || isRecentlyVisible;
    }
    
    // 检查元素是否最近添加到DOM中 - 新增辅助方法
    isRecentlyAddedToDOM(element) {
        // 简单的启发式检查：如果元素在一个最近可见的模态框或下拉菜单中
        const recentContainers = document.querySelectorAll(
            '.modal:not([style*="display: none"]), ' +
            '.dropdown:not([style*="display: none"]), ' +
            '.popup:not([style*="display: none"]), ' +
            '[style*="display: block"], ' +
            '[style*="visibility: visible"]'
        );
        
        for (const container of recentContainers) {
            if (container.contains(element)) {
                return true;
            }
        }
        
        return false;
    }

    // === 新增：返回根目录的方法 ===
    async returnToRoot() {
        this.log('开始返回根目录...');
        
        // 检查当前是否已经在根目录
        if (this.currentPath.length === 0) {
            this.log('已在根目录，无需返回');
            return;
        }
        
        try {
            // 方法1：尝试点击面包屑导航中的根目录链接
            const breadcrumbSelectors = [
                '.ant-breadcrumb a:first-child',
                '.breadcrumb a:first-child', 
                '[class*="breadcrumb"] a:first-child',
                '.nav-breadcrumb a:first-child'
            ];
            
            for (const selector of breadcrumbSelectors) {
                const homeLink = document.querySelector(selector);
                if (homeLink && homeLink.textContent && (homeLink.textContent.includes('全部文件') || homeLink.textContent.includes('我的网盘') || homeLink.textContent.includes('首页'))) {
                    this.log(`通过面包屑返回根目录: ${selector}`);
                    homeLink.click();
                    await this.sleep(1500);
                    
                    // 验证是否成功返回
                    await this.syncCurrentPath();
                    if (this.currentPath.length === 0) {
                        this.log('通过面包屑成功返回根目录');
                        return;
                    }
                }
            }
            
            // === 修复：增加更多面包屑查找策略 ===
            this.log('标准选择器未找到，尝试其他面包屑策略');
            
            // 查找所有面包屑项，寻找根目录标识
            const allBreadcrumbItems = document.querySelectorAll('nz-breadcrumb-item, .breadcrumb-item, [class*="breadcrumb"] *');
            
            for (const item of allBreadcrumbItems) {
                if (item && item.textContent) {
                    const text = item.textContent.trim();
                    if (text === '全部文件' || text === '我的网盘' || text === '首页' || text === 'Home' || text === 'All Files') {
                        this.log(`找到根目录面包屑项: "${text}"`);
                        
                        // 检查是否为可点击元素或查找可点击的父/子元素
                        let clickableElement = null;
                        
                        if (item.tagName === 'A' || item.onclick) {
                            clickableElement = item;
                        } else {
                            // 查找可点击的父元素
                            let parent = item.parentElement;
                            while (parent && !clickableElement) {
                                if (parent.tagName === 'A' || parent.onclick || parent.classList.contains('breadcrumb-name')) {
                                    clickableElement = parent;
                                    break;
                                }
                                parent = parent.parentElement;
                            }
                            
                            // 查找可点击的子元素
                            if (!clickableElement) {
                                const clickableChild = item.querySelector('a, [onclick], .breadcrumb-name');
                                if (clickableChild) {
                                    clickableElement = clickableChild;
                                }
                            }
                        }
                        
                        if (clickableElement) {
                            this.log(`点击根目录面包屑: "${text}"`);
                            clickableElement.click();
                            await this.sleep(1500);
                            
                            // 验证是否成功返回
                            await this.syncCurrentPath();
                            if (this.currentPath.length === 0) {
                                this.log('通过面包屑项成功返回根目录');
                                return;
                            }
                        }
                    }
                }
            }
            
            // 方法2：尝试通过URL导航到根目录
            this.log('面包屑方法失败，尝试通过URL导航返回根目录');
            await this.forceNavigateToRoot();
            
        } catch (error) {
            this.log(`返回根目录失败: ${error.message}`);
            throw error;
        }
    }
    
    // === 新增：强制导航到根目录的方法 ===
    async forceNavigateToRoot() {
        this.log('强制导航到根目录...');
        
        try {
            // 获取当前URL的基础部分
            const currentUrl = window.location.href;
            let baseUrl;
            
            if (currentUrl.includes('#')) {
                baseUrl = currentUrl.split('#')[0];
            } else {
                baseUrl = currentUrl.split('?')[0];
            }
            
            // 构建根目录URL
            const rootUrl = `${baseUrl}#/home/<USER>
            this.log(`导航到根目录URL: ${rootUrl}`);
            
            // 执行导航
            window.location.href = rootUrl;
            
            // 等待页面加载
            await this.sleep(2000);
            
            // 等待应用程序加载完成
            await this.waitForElement('.file-list-container', 5000);
            
            // 同步路径状态
            await this.syncCurrentPath();
            
            this.log('强制导航到根目录完成');
            
        } catch (error) {
            this.log(`强制导航到根目录失败: ${error.message}`);
            throw error;
        }
    }
    
    // === 增强：路径同步方法 ===
    async syncCurrentPath() {
        this.log('同步当前路径状态...');
        
        try {
            // === 修复：使用getCurrentSimplePath方法保持一致性 ===
            const simplePath = this.getCurrentSimplePath();
            this.currentPath = [...simplePath];
            
            this.log(`从简化路径同步: [${this.currentPath.join(' / ')}]`);
            
            // 备用方案：如果简化路径获取失败，尝试传统方法
            if (this.currentPath.length === 0) {
                this.log('简化路径为空，尝试传统面包屑解析');
                
                const breadcrumbSelectors = [
                    '.ant-breadcrumb',
                    '.breadcrumb', 
                    '[class*="breadcrumb"]',
                    '.nav-breadcrumb'
                ];
                
                for (const selector of breadcrumbSelectors) {
                    const breadcrumb = document.querySelector(selector);
                    if (breadcrumb) {
                        const breadcrumbLinks = breadcrumb.querySelectorAll('a, span');
                        const pathParts = [];
                        
                        // === 修复：正确跳过根目录标识 ===
                        for (let i = 0; i < breadcrumbLinks.length; i++) {
                            const text = breadcrumbLinks[i].textContent.trim();
                            // 跳过根目录标识和分隔符
                            if (text && 
                                text !== '全部文件' && 
                                text !== '我的网盘' && 
                                text !== '首页' && 
                                text !== 'Home' && 
                                text !== 'All Files' &&
                                text !== '>' && 
                                text !== '/') {
                                pathParts.push(text);
                            }
                        }
                        
                        // === 新增：在传统方法中也应用去重逻辑 ===
                        const cleanedPathParts = [];
                        let previousPart = null;
                        
                        for (let i = 0; i < pathParts.length; i++) {
                            const currentPart = pathParts[i];
                            
                            if (currentPart !== previousPart) {
                                cleanedPathParts.push(currentPart);
                                previousPart = currentPart;
                            } else {
                                this.log(`传统方法中检测到重复路径段，跳过: "${currentPart}"`);
                            }
                        }
                        
                        // 如果发现重复并进行了清理，记录日志
                        if (pathParts.length !== cleanedPathParts.length) {
                            this.log(`传统方法路径去重: 原始=[${pathParts.join(' / ')}] → 清理后=[${cleanedPathParts.join(' / ')}]`);
                        }
                        
                        this.currentPath = cleanedPathParts;
                        this.log(`从传统面包屑同步路径: [${this.currentPath.join(' / ')}]`);
                        break;
                    }
                }
            }
            
            // 最后备用：从URL获取
            if (this.currentPath.length === 0) {
                const urlParams = new URLSearchParams(window.location.search);
                const currentId = urlParams.get('id');
                
                if (!currentId || currentId === '0') {
                    this.currentPath = [];
                    this.log('从URL确认在根目录');
                } else {
                    this.log(`当前URL参数id=${currentId}，但无法确定具体路径`);
                }
            }
            
            // === 新增：最终的路径一致性检查 ===
            const finalSimplePath = this.getCurrentSimplePath();
            if (JSON.stringify(this.currentPath) !== JSON.stringify(finalSimplePath)) {
                this.log(`路径同步后发现不一致:`);
                this.log(`- 内部路径: [${this.currentPath.join(' / ')}]`);
                this.log(`- 简化路径: [${finalSimplePath.join(' / ')}]`);
                this.log('使用简化路径作为最终结果');
                this.currentPath = [...finalSimplePath];
            }
            
        } catch (error) {
            this.log(`同步路径状态失败: ${error.message}`);
            // 发生错误时重置为空路径
            this.currentPath = [];
        }
    }
    
    // === 增强：验证是否在期望路径的方法 ===
    isAtExpectedPath(expectedPath) {
        if (!expectedPath || !Array.isArray(expectedPath)) {
            return this.currentPath.length === 0;
        }
        
        if (this.currentPath.length !== expectedPath.length) {
            return false;
        }
        
        for (let i = 0; i < expectedPath.length; i++) {
            if (this.currentPath[i] !== expectedPath[i]) {
                return false;
            }
        }
        
        return true;
    }
    
    // === 修复：等待到达期望路径，增加超时和重试机制 ===
    async waitForExpectedPath(expectedPath, maxWaitTime = 10000) {
        this.log(`等待到达期望路径: [${expectedPath.join(' / ')}]`);
        
        const startTime = Date.now();
        
        while (Date.now() - startTime < maxWaitTime) {
            // 每次检查前先同步路径
            await this.syncCurrentPath();
            
            if (this.isAtExpectedPath(expectedPath)) {
                this.log(`已到达期望路径: [${expectedPath.join(' / ')}]`);
                return true;
            }
            
            this.log(`当前路径: [${this.currentPath.join(' / ')}], 期望: [${expectedPath.join(' / ')}]`);
            await this.sleep(500);
        }
        
        this.log(`等待期望路径超时: [${expectedPath.join(' / ')}]`);
        return false;
    }

    // === 新增：等待元素出现的辅助方法 ===
    async waitForElement(selector, timeout = 5000) {
        this.log(`等待元素出现: ${selector} (超时: ${timeout}ms)`);
        
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (element && element.offsetParent !== null) {
                this.log(`元素已出现: ${selector}`);
                return element;
            }
            
            await this.sleep(200);
        }
        
        this.log(`等待元素超时: ${selector}`);
        return null;
    }
}

// 初始化云盘控制器
const yunpanController = new YunpanController(); 
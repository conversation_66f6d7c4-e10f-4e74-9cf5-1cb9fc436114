// 调试上传流程的脚本
// 在浏览器控制台中运行此脚本来诊断问题

console.log('=== 文件云流转助手上传流程调试工具 ===');

// 1. 检查扩展是否正确加载
function checkExtensionLoaded() {
    console.log('\n1. 检查扩展加载状态:');
    
    if (typeof chrome === 'undefined') {
        console.error('❌ Chrome API不可用');
        return false;
    }
    
    if (!chrome.runtime) {
        console.error('❌ chrome.runtime不可用');
        return false;
    }
    
    console.log('✅ Chrome扩展API可用');
    return true;
}

// 2. 检查云盘页面内容脚本
async function checkYunpanContentScript() {
    console.log('\n2. 检查云盘页面内容脚本:');
    
    try {
        const tabs = await chrome.tabs.query({});
        const yunpanTabs = tabs.filter(tab => 
            tab.url && (
                tab.url.includes('yunpan.gdcourts.gov.cn') ||
                tab.url.includes('yunpan.gdcourts.gov.cn:82')
            )
        );
        
        if (yunpanTabs.length === 0) {
            console.error('❌ 未找到云盘页面标签');
            return false;
        }
        
        console.log(`✅ 找到 ${yunpanTabs.length} 个云盘页面标签:`);
        yunpanTabs.forEach((tab, index) => {
            console.log(`   ${index + 1}. ${tab.url} (ID: ${tab.id})`);
        });
        
        // 测试与每个云盘页面的通信
        for (const tab of yunpanTabs) {
            try {
                const response = await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('PING超时'));
                    }, 3000);
                    
                    chrome.tabs.sendMessage(tab.id, { type: 'PING' }, (response) => {
                        clearTimeout(timeout);
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                if (response && response.success) {
                    console.log(`✅ 标签页 ${tab.id} 内容脚本响应正常`);
                } else {
                    console.error(`❌ 标签页 ${tab.id} 内容脚本响应异常:`, response);
                }
            } catch (error) {
                console.error(`❌ 标签页 ${tab.id} 通信失败:`, error.message);
            }
        }
        
        return yunpanTabs.length > 0;
    } catch (error) {
        console.error('❌ 检查云盘页面失败:', error);
        return false;
    }
}

// 3. 检查文件树数据
async function checkFileTreeData() {
    console.log('\n3. 检查文件树数据:');
    
    try {
        const result = await chrome.storage.local.get('admin_file_tree');
        const fileTree = result.admin_file_tree;
        
        if (!fileTree) {
            console.error('❌ 未找到文件树数据');
            return false;
        }
        
        console.log('✅ 找到文件树数据');
        console.log('   数据结构:', {
            hasChildren: !!(fileTree.children && Array.isArray(fileTree.children)),
            childrenCount: fileTree.children ? fileTree.children.length : 0,
            keys: Object.keys(fileTree)
        });
        
        // 统计待上传文件
        let pendingCount = 0;
        let totalCount = 0;
        
        function countFiles(node) {
            if (node.type === 'file') {
                totalCount++;
                if (node.status === 'pending' || node.status === 'failed') {
                    pendingCount++;
                }
            } else if (node.children && Array.isArray(node.children)) {
                node.children.forEach(child => countFiles(child));
            }
        }
        
        if (fileTree.children && Array.isArray(fileTree.children)) {
            fileTree.children.forEach(child => countFiles(child));
        }
        
        console.log(`   文件统计: 总计 ${totalCount} 个文件，待上传 ${pendingCount} 个`);
        
        return pendingCount > 0;
    } catch (error) {
        console.error('❌ 检查文件树数据失败:', error);
        return false;
    }
}

// 4. 测试background通信
async function testBackgroundCommunication() {
    console.log('\n4. 测试background通信:');
    
    try {
        // 测试连接状态
        const statusResponse = await chrome.runtime.sendMessage({
            type: 'GET_CONNECTION_STATUS'
        });
        console.log('✅ 连接状态响应:', statusResponse);
        
        // 测试上传触发
        const triggerResponse = await chrome.runtime.sendMessage({
            type: 'TRIGGER_AUTO_UPLOAD',
            source: 'debug_script',
            timestamp: Date.now()
        });
        console.log('✅ 上传触发响应:', triggerResponse);
        
        return true;
    } catch (error) {
        console.error('❌ background通信失败:', error);
        return false;
    }
}

// 5. 测试直接云盘通信
async function testDirectYunpanCommunication() {
    console.log('\n5. 测试直接云盘通信:');
    
    try {
        const tabs = await chrome.tabs.query({});
        const yunpanTab = tabs.find(tab => 
            tab.url && (
                tab.url.includes('yunpan.gdcourts.gov.cn') ||
                tab.url.includes('yunpan.gdcourts.gov.cn:82')
            )
        );
        
        if (!yunpanTab) {
            console.error('❌ 未找到云盘页面');
            return false;
        }
        
        // 模拟文件数据
        const mockFiles = [{
            name: 'debug-test.txt',
            fileName: 'debug-test.txt',
            path: 'debug-test.txt',
            size: 100,
            type: 'file',
            status: 'pending'
        }];
        
        console.log('📤 发送测试上传请求到云盘页面...');
        
        const response = await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('响应超时（10秒）'));
            }, 10000);
            
            chrome.tabs.sendMessage(yunpanTab.id, {
                type: 'START_UPLOAD',
                data: {
                    files: mockFiles,
                    source: 'debug_direct_test'
                }
            }, (response) => {
                clearTimeout(timeout);
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
        
        if (response && response.success) {
            console.log('✅ 直接云盘通信成功:', response);
            return true;
        } else {
            console.error('❌ 直接云盘通信失败:', response);
            return false;
        }
    } catch (error) {
        console.error('❌ 直接云盘通信异常:', error);
        return false;
    }
}

// 主调试函数
async function runFullDiagnostic() {
    console.log('🔍 开始完整诊断...\n');
    
    const results = {
        extensionLoaded: checkExtensionLoaded(),
        yunpanContentScript: await checkYunpanContentScript(),
        fileTreeData: await checkFileTreeData(),
        backgroundCommunication: await testBackgroundCommunication(),
        directYunpanCommunication: await testDirectYunpanCommunication()
    };
    
    console.log('\n=== 诊断结果汇总 ===');
    Object.entries(results).forEach(([key, value]) => {
        console.log(`${value ? '✅' : '❌'} ${key}: ${value ? '正常' : '异常'}`);
    });
    
    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
        console.log('\n🎉 所有检查都通过了！上传功能应该可以正常工作。');
    } else {
        console.log('\n⚠️ 发现问题，请根据上述错误信息进行修复。');
    }
    
    return results;
}

// 导出函数供控制台使用
window.debugUploadFlow = {
    checkExtensionLoaded,
    checkYunpanContentScript,
    checkFileTreeData,
    testBackgroundCommunication,
    testDirectYunpanCommunication,
    runFullDiagnostic
};

console.log('\n使用方法:');
console.log('- 运行完整诊断: debugUploadFlow.runFullDiagnostic()');
console.log('- 单独检查扩展: debugUploadFlow.checkExtensionLoaded()');
console.log('- 检查云盘脚本: debugUploadFlow.checkYunpanContentScript()');
console.log('- 检查文件数据: debugUploadFlow.checkFileTreeData()');
console.log('- 测试background: debugUploadFlow.testBackgroundCommunication()');
console.log('- 测试直接通信: debugUploadFlow.testDirectYunpanCommunication()');

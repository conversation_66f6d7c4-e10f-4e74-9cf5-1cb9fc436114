// 文件云流转助手 - 注入面板文件树组件
// 用于在注入面板中渲染文件树结构

class InjectedPanelTree {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            showThumbnails: true,
            allowSelection: true,
            expandable: true,
            ...options
        };
        
        this.treeData = null;
        this.selectedItems = new Set();
        this.expandedFolders = new Set();
        
        this.init();
    }

    init() {
        this.container.innerHTML = '';
        this.container.className = 'ylz-tree-container';
        
        // 添加基本样式
        this.injectTreeStyles();
    }

    // 注入树形组件样式
    injectTreeStyles() {
        if (document.getElementById('ylz-tree-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'ylz-tree-styles';
        style.textContent = `
            .ylz-tree-container {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 13px;
                line-height: 1.4;
            }

            .ylz-tree-item {
                display: flex;
                align-items: center;
                padding: 6px 8px;
                cursor: pointer;
                border-radius: 4px;
                margin: 1px 0;
                transition: background-color 0.15s ease;
                user-select: none;
            }

            .ylz-tree-item:hover {
                background-color: #f8f9fa;
            }

            .ylz-tree-item.selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }

            .ylz-tree-item.folder {
                font-weight: 500;
            }

            .ylz-tree-expand-icon {
                width: 16px;
                height: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 4px;
                font-size: 10px;
                color: #666;
                transition: transform 0.15s ease;
            }

            .ylz-tree-item.expanded .ylz-tree-expand-icon {
                transform: rotate(90deg);
            }

            .ylz-tree-icon {
                width: 16px;
                height: 16px;
                margin-right: 8px;
                flex-shrink: 0;
            }

            .ylz-tree-thumbnail {
                width: 20px;
                height: 20px;
                margin-right: 8px;
                border-radius: 2px;
                object-fit: cover;
                flex-shrink: 0;
                background: #f0f0f0;
            }

            .ylz-tree-label {
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .ylz-tree-status {
                margin-left: 8px;
                padding: 2px 6px;
                border-radius: 10px;
                font-size: 10px;
                font-weight: 500;
                flex-shrink: 0;
            }

            .ylz-tree-status.pending {
                background: #fff3cd;
                color: #856404;
            }

            .ylz-tree-status.uploaded {
                background: #d4edda;
                color: #155724;
            }

            .ylz-tree-status.uploading {
                background: #cce5ff;
                color: #004085;
            }

            .ylz-tree-status.failed {
                background: #f8d7da;
                color: #721c24;
            }

            .ylz-tree-children {
                margin-left: 20px;
                display: none;
            }

            .ylz-tree-item.expanded + .ylz-tree-children {
                display: block;
            }

            .ylz-tree-empty {
                text-align: center;
                padding: 40px 20px;
                color: #666;
                font-style: italic;
            }

            .ylz-tree-loading {
                text-align: center;
                padding: 20px;
                color: #666;
            }

            .ylz-tree-loading::after {
                content: '';
                display: inline-block;
                width: 16px;
                height: 16px;
                border: 2px solid #ddd;
                border-top: 2px solid #007bff;
                border-radius: 50%;
                animation: ylz-spin 1s linear infinite;
                margin-left: 8px;
            }

            @keyframes ylz-spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            /* 文件大小显示 */
            .ylz-tree-size {
                margin-left: 8px;
                font-size: 11px;
                color: #999;
                flex-shrink: 0;
            }

            /* 修改时间显示 */
            .ylz-tree-time {
                margin-left: 8px;
                font-size: 11px;
                color: #999;
                flex-shrink: 0;
            }
        `;
        document.head.appendChild(style);
    }

    // 渲染树形结构
    render(treeData) {
        this.treeData = treeData;
        
        if (!treeData || (Array.isArray(treeData) && treeData.length === 0)) {
            this.renderEmpty();
            return;
        }

        this.container.innerHTML = '';
        
        if (Array.isArray(treeData)) {
            treeData.forEach(item => {
                this.renderTreeItem(item, this.container, 0);
            });
        } else if (treeData.files || treeData.folders) {
            // 处理包含files和folders的数据结构
            const items = [
                ...(treeData.folders || []).map(folder => ({ ...folder, type: 'folder' })),
                ...(treeData.files || []).map(file => ({ ...file, type: 'file' }))
            ];
            
            items.forEach(item => {
                this.renderTreeItem(item, this.container, 0);
            });
        }
    }

    // 渲染单个树项
    renderTreeItem(item, parentContainer, level) {
        const itemElement = document.createElement('div');
        itemElement.className = `ylz-tree-item ${item.type || 'file'}`;
        itemElement.dataset.path = item.path || item.name;
        itemElement.dataset.type = item.type || 'file';
        
        // 展开图标（仅文件夹）
        if (item.type === 'folder' || item.children) {
            const expandIcon = document.createElement('span');
            expandIcon.className = 'ylz-tree-expand-icon';
            expandIcon.textContent = '▶';
            itemElement.appendChild(expandIcon);
            
            // 检查是否已展开
            if (this.expandedFolders.has(item.path || item.name)) {
                itemElement.classList.add('expanded');
            }
        } else {
            // 文件项的缩进
            const spacer = document.createElement('span');
            spacer.style.width = '16px';
            spacer.style.height = '16px';
            spacer.style.marginRight = '4px';
            itemElement.appendChild(spacer);
        }

        // 文件图标或缩略图
        if (this.options.showThumbnails && item.thumbnail) {
            const thumbnail = document.createElement('img');
            thumbnail.className = 'ylz-tree-thumbnail';
            thumbnail.src = item.thumbnail;
            thumbnail.alt = item.name;
            thumbnail.onerror = () => {
                thumbnail.style.display = 'none';
                this.addFileIcon(itemElement, item);
            };
            itemElement.appendChild(thumbnail);
        } else {
            this.addFileIcon(itemElement, item);
        }

        // 文件名
        const label = document.createElement('span');
        label.className = 'ylz-tree-label';
        label.textContent = item.name || item.filename;
        label.title = item.path || item.name;
        itemElement.appendChild(label);

        // 文件大小
        if (item.size && item.type !== 'folder') {
            const size = document.createElement('span');
            size.className = 'ylz-tree-size';
            size.textContent = this.formatFileSize(item.size);
            itemElement.appendChild(size);
        }

        // 状态标签
        if (item.status || item.syncStatus) {
            const status = document.createElement('span');
            status.className = `ylz-tree-status ${this.getStatusClass(item.status || item.syncStatus)}`;
            status.textContent = this.getStatusText(item.status || item.syncStatus);
            itemElement.appendChild(status);
        }

        // 绑定点击事件
        itemElement.addEventListener('click', (e) => {
            e.stopPropagation();
            this.handleItemClick(item, itemElement);
        });

        parentContainer.appendChild(itemElement);

        // 渲染子项（如果是文件夹且有子项）
        if ((item.type === 'folder' || item.children) && (item.children || item.files)) {
            const childrenContainer = document.createElement('div');
            childrenContainer.className = 'ylz-tree-children';
            
            const children = item.children || item.files || [];
            children.forEach(child => {
                this.renderTreeItem(child, childrenContainer, level + 1);
            });
            
            parentContainer.appendChild(childrenContainer);
        }
    }

    // 添加文件图标
    addFileIcon(itemElement, item) {
        const icon = document.createElement('span');
        icon.className = 'ylz-tree-icon';
        
        if (item.type === 'folder') {
            icon.textContent = '📁';
        } else {
            // 根据文件扩展名显示不同图标
            const ext = this.getFileExtension(item.name || item.filename);
            icon.textContent = this.getFileIcon(ext);
        }
        
        itemElement.appendChild(icon);
    }

    // 处理项目点击
    handleItemClick(item, itemElement) {
        if (item.type === 'folder' || item.children) {
            // 切换文件夹展开状态
            this.toggleFolder(item, itemElement);
        } else {
            // 选择文件
            if (this.options.allowSelection) {
                this.toggleSelection(item, itemElement);
            }
        }

        // 触发自定义事件
        this.container.dispatchEvent(new CustomEvent('itemClick', {
            detail: { item, element: itemElement }
        }));
    }

    // 切换文件夹展开状态
    toggleFolder(item, itemElement) {
        const path = item.path || item.name;
        const isExpanded = this.expandedFolders.has(path);
        
        if (isExpanded) {
            this.expandedFolders.delete(path);
            itemElement.classList.remove('expanded');
        } else {
            this.expandedFolders.add(path);
            itemElement.classList.add('expanded');
        }
    }

    // 切换选择状态
    toggleSelection(item, itemElement) {
        const path = item.path || item.name;
        const isSelected = this.selectedItems.has(path);
        
        if (isSelected) {
            this.selectedItems.delete(path);
            itemElement.classList.remove('selected');
        } else {
            this.selectedItems.add(path);
            itemElement.classList.add('selected');
        }
    }

    // 渲染空状态
    renderEmpty() {
        this.container.innerHTML = `
            <div class="ylz-tree-empty">
                <div>📁</div>
                <div>暂无文件</div>
            </div>
        `;
    }

    // 渲染加载状态
    renderLoading() {
        this.container.innerHTML = `
            <div class="ylz-tree-loading">
                正在加载文件列表
            </div>
        `;
    }

    // 工具方法
    getFileExtension(filename) {
        return filename.split('.').pop().toLowerCase();
    }

    getFileIcon(extension) {
        const iconMap = {
            'pdf': '📄',
            'doc': '📝', 'docx': '📝',
            'xls': '📊', 'xlsx': '📊',
            'ppt': '📽️', 'pptx': '📽️',
            'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️',
            'mp4': '🎬', 'avi': '🎬', 'mov': '🎬',
            'mp3': '🎵', 'wav': '🎵',
            'zip': '📦', 'rar': '📦', '7z': '📦',
            'txt': '📄',
            'html': '🌐', 'htm': '🌐',
            'js': '⚡', 'css': '🎨',
            'default': '📄'
        };
        
        return iconMap[extension] || iconMap.default;
    }

    getStatusClass(status) {
        const statusMap = {
            '未同步': 'pending',
            '同步中': 'uploading', 
            '已同步': 'uploaded',
            '同步失败': 'failed',
            'pending': 'pending',
            'uploading': 'uploading',
            'uploaded': 'uploaded',
            'failed': 'failed'
        };
        
        return statusMap[status] || 'pending';
    }

    getStatusText(status) {
        const textMap = {
            '未同步': '待上传',
            '同步中': '上传中',
            '已同步': '已上传',
            '同步失败': '失败',
            'pending': '待上传',
            'uploading': '上传中',
            'uploaded': '已上传',
            'failed': '失败'
        };
        
        return textMap[status] || '待上传';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    // 公共方法
    expandAll() {
        const folders = this.container.querySelectorAll('.ylz-tree-item.folder');
        folders.forEach(folder => {
            const path = folder.dataset.path;
            this.expandedFolders.add(path);
            folder.classList.add('expanded');
        });
    }

    collapseAll() {
        const folders = this.container.querySelectorAll('.ylz-tree-item.folder');
        folders.forEach(folder => {
            const path = folder.dataset.path;
            this.expandedFolders.delete(path);
            folder.classList.remove('expanded');
        });
    }

    getSelectedItems() {
        return Array.from(this.selectedItems);
    }

    clearSelection() {
        this.selectedItems.clear();
        const selectedElements = this.container.querySelectorAll('.ylz-tree-item.selected');
        selectedElements.forEach(element => {
            element.classList.remove('selected');
        });
    }

    refresh(newData) {
        this.render(newData);
    }
}

// 导出到全局作用域
window.InjectedPanelTree = InjectedPanelTree;

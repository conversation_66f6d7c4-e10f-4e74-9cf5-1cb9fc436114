<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件云流转助手 - 注入面板</title>
    <style>
        /* 注入面板专用样式 - 基于popup.css但适配页面注入 */
        #ylz-injected-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 480px;
            max-height: 80vh;
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            z-index: 999999;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            overflow: hidden;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        #ylz-injected-panel.minimized {
            height: 60px;
            overflow: hidden;
        }

        #ylz-injected-panel.hidden {
            transform: translateX(100%);
            opacity: 0;
        }

        /* 面板头部 */
        .ylz-panel-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: move;
            user-select: none;
        }

        .ylz-panel-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0;
        }

        .ylz-panel-controls {
            display: flex;
            gap: 8px;
        }

        .ylz-control-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: background 0.2s;
        }

        .ylz-control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* 面板内容区域 */
        .ylz-panel-content {
            padding: 16px;
            max-height: calc(80vh - 60px);
            overflow-y: auto;
        }

        /* 连接状态区域 */
        .ylz-status-section {
            margin-bottom: 16px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .ylz-connection-status {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .ylz-status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #dc3545;
            transition: background 0.3s;
        }

        .ylz-status-dot.connected {
            background: #28a745;
        }

        .ylz-status-text {
            font-size: 13px;
            color: #666;
        }

        .ylz-yunpan-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
            background: #ffc107;
            color: #856404;
            margin-left: auto;
        }

        .ylz-yunpan-status.connected {
            background: #d4edda;
            color: #155724;
        }

        /* 自动刷新设置 */
        .ylz-auto-refresh-setting {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 8px;
            flex-wrap: wrap;
        }

        .ylz-setting-label {
            font-size: 12px;
            color: #666;
        }

        .ylz-refresh-interval-select {
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
            background: white;
            cursor: pointer;
        }

        .ylz-refresh-interval-select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        /* 勾选框样式 */
        .ylz-checkbox-label {
            display: flex;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            font-size: 12px;
            color: #666;
            user-select: none;
        }

        .ylz-checkbox {
            width: 14px;
            height: 14px;
            cursor: pointer;
            accent-color: #007bff;
        }

        .ylz-checkbox-text {
            white-space: nowrap;
        }

        .ylz-checkbox-label:hover .ylz-checkbox-text {
            color: #007bff;
        }

        /* 操作按钮区域 */
        .ylz-action-buttons {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            flex-wrap: wrap;
        }

        .ylz-button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .ylz-button.primary {
            background: #007bff;
            color: white;
        }

        .ylz-button.primary:hover {
            background: #0056b3;
        }

        .ylz-button.secondary {
            background: #6c757d;
            color: white;
        }

        .ylz-button.secondary:hover {
            background: #545b62;
        }

        .ylz-button.icon-button {
            width: 36px;
            height: 36px;
            padding: 0;
            justify-content: center;
            background: #f8f9fa;
            color: #666;
        }

        .ylz-button.icon-button:hover {
            background: #e9ecef;
        }

        /* 自动上传开关 */
        .ylz-auto-upload-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-left: auto;
        }

        .ylz-toggle-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .ylz-toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .ylz-toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: 0.3s;
            border-radius: 24px;
        }

        .ylz-toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: 0.3s;
            border-radius: 50%;
        }

        input:checked + .ylz-toggle-slider {
            background-color: #007bff;
        }

        input:checked + .ylz-toggle-slider:before {
            transform: translateX(20px);
        }

        .ylz-toggle-label {
            font-size: 12px;
            color: #666;
        }

        /* 标签页导航 */
        .ylz-tab-navigation {
            display: flex;
            border-bottom: 1px solid #e0e0e0;
            margin-bottom: 16px;
        }

        .ylz-tab-button {
            flex: 1;
            padding: 12px 16px;
            border: none;
            background: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: #666;
            font-size: 13px;
            transition: all 0.2s;
            position: relative;
        }

        .ylz-tab-button.active {
            color: #007bff;
        }

        .ylz-tab-button.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: #007bff;
        }

        .ylz-tab-count {
            background: #e9ecef;
            color: #666;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            min-width: 18px;
            text-align: center;
        }

        .ylz-tab-button.active .ylz-tab-count {
            background: #007bff;
            color: white;
        }

        /* 标签页内容 */
        .ylz-tab-content {
            min-height: 200px;
        }

        .ylz-tab-panel {
            display: none;
        }

        .ylz-tab-panel.active {
            display: block;
        }

        .ylz-panel-header-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .ylz-panel-header-section h3 {
            margin: 0;
            font-size: 14px;
            color: #333;
        }

        .ylz-panel-actions {
            display: flex;
            gap: 4px;
        }

        .ylz-action-btn {
            background: none;
            border: 1px solid #ddd;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }

        .ylz-action-btn:hover {
            background: #f8f9fa;
        }

        /* 树形容器 */
        .ylz-tree-container {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: white;
        }

        /* 加载状态 */
        .ylz-loading-state {
            padding: 20px;
            text-align: center;
            color: #666;
        }

        .ylz-loading-skeleton {
            height: 40px;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: ylz-loading 1.5s infinite;
            border-radius: 4px;
            margin-bottom: 8px;
        }

        @keyframes ylz-loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* 文件计数信息 */
        .ylz-status-info {
            font-size: 12px;
            color: #666;
            margin-top: 8px;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            #ylz-injected-panel {
                width: calc(100vw - 40px);
                right: 20px;
                left: 20px;
            }
        }

        /* 拖拽相关样式 */
        #ylz-injected-panel.dragging {
            transition: none;
        }

        /* 滚动条样式 */
        .ylz-panel-content::-webkit-scrollbar,
        .ylz-tree-container::-webkit-scrollbar {
            width: 6px;
        }

        .ylz-panel-content::-webkit-scrollbar-track,
        .ylz-tree-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .ylz-panel-content::-webkit-scrollbar-thumb,
        .ylz-tree-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .ylz-panel-content::-webkit-scrollbar-thumb:hover,
        .ylz-tree-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <!-- 注入面板容器 -->
    <div id="ylz-injected-panel">
        <!-- 面板头部 -->
        <div class="ylz-panel-header" id="ylz-panel-header">
            <h1 class="ylz-panel-title">文件云流转助手</h1>
            <div class="ylz-panel-controls">
                <button class="ylz-control-btn" id="ylz-minimize-btn" title="最小化">−</button>
                <button class="ylz-control-btn" id="ylz-close-btn" title="关闭">×</button>
            </div>
        </div>

        <!-- 面板内容 -->
        <div class="ylz-panel-content" id="ylz-panel-content">
            <!-- 连接状态指示区域 -->
            <section class="ylz-status-section">
                <div class="ylz-connection-status">
                    <div class="ylz-status-dot" id="ylz-connectionStatusDot"></div>
                    <span class="ylz-status-text" id="ylz-connectionStatusText">连接状态检查中...</span>
                    <div class="ylz-yunpan-status" id="ylz-yunpan-status">云盘未连接</div>
                </div>
                
                <!-- 自动刷新设置 -->
                <div class="ylz-auto-refresh-setting">
                    <label for="ylz-autoRefreshSelect" class="ylz-setting-label">自动刷新:</label>
                    <select id="ylz-autoRefreshSelect" class="ylz-refresh-interval-select">
                        <option value="0">关闭</option>
                        <option value="60">60秒</option>
                        <option value="300">5分钟</option>
                        <option value="900">15分钟</option>
                        <option value="1800">30分钟</option>
                    </select>
                    <label class="ylz-checkbox-label">
                        <input type="checkbox" id="ylz-allowActiveRefresh" class="ylz-checkbox">
                        <span class="ylz-checkbox-text">刷新当前页面</span>
                    </label>
                </div>
                
                <div class="ylz-status-info">
                    <span id="ylz-fileCountInfo">加载中...</span>
                </div>
            </section>

            <!-- 操作按钮区域 -->
            <section class="ylz-action-buttons">
                <button id="ylz-uploadAll" class="ylz-button primary" title="开始上传所有待上传文件">
                    <span>开始上传</span>
                </button>
                <button id="ylz-openCloud" class="ylz-button secondary" title="在新标签页打开文件云流转">
                    <span>打开云盘</span>
                </button>
                <button id="ylz-refreshList" class="ylz-button icon-button" title="刷新文件列表">
                    <span>↻</span>
                </button>
                
                <!-- 自动上传开关 -->
                <div class="ylz-auto-upload-toggle">
                    <label class="ylz-toggle-switch">
                        <input type="checkbox" id="ylz-autoUploadSwitch">
                        <span class="ylz-toggle-slider"></span>
                    </label>
                    <span class="ylz-toggle-label">自动上传</span>
                </div>
            </section>

            <!-- 主要内容区域 -->
            <main class="ylz-main-content">
                <!-- 标签页导航 -->
                <div class="ylz-tab-navigation">
                    <button class="ylz-tab-button active" data-tab="pending" id="ylz-pendingTab">
                        <span class="ylz-tab-text">未上传</span>
                        <span class="ylz-tab-count" id="ylz-pendingCount">0</span>
                    </button>
                    <button class="ylz-tab-button" data-tab="uploaded" id="ylz-uploadedTab">
                        <span class="ylz-tab-text">已上传</span>
                        <span class="ylz-tab-count" id="ylz-uploadedCount">0</span>
                    </button>
                </div>
                
                <!-- 标签页内容 -->
                <div class="ylz-tab-content">
                    <!-- 未上传文件标签页 -->
                    <div class="ylz-tab-panel active" id="ylz-pendingPanel">
                        <div class="ylz-panel-header-section">
                            <h3>未上传文件</h3>
                            <div class="ylz-panel-actions">
                                <button class="ylz-action-btn" id="ylz-expandAllPending" title="展开所有目录">📂</button>
                                <button class="ylz-action-btn" id="ylz-collapseAllPending" title="折叠所有目录">📁</button>
                            </div>
                        </div>
                        <div class="ylz-tree-container" id="ylz-pendingTreeContainer">
                            <div class="ylz-loading-state">
                                <div class="ylz-loading-skeleton"></div>
                                <div class="ylz-loading-skeleton"></div>
                                <div class="ylz-loading-skeleton"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 已上传文件标签页 -->
                    <div class="ylz-tab-panel" id="ylz-uploadedPanel">
                        <div class="ylz-panel-header-section">
                            <h3>已上传文件</h3>
                            <div class="ylz-panel-actions">
                                <button class="ylz-action-btn" id="ylz-expandAllUploaded" title="展开所有目录">📂</button>
                                <button class="ylz-action-btn" id="ylz-collapseAllUploaded" title="折叠所有目录">📁</button>
                            </div>
                        </div>
                        <div class="ylz-tree-container" id="ylz-uploadedTreeContainer">
                            <div class="ylz-loading-state">
                                <div class="ylz-loading-skeleton"></div>
                                <div class="ylz-loading-skeleton"></div>
                                <div class="ylz-loading-skeleton"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</body>
</html>

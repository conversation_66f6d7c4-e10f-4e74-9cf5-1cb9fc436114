// 文件云流转助手 - 页面注入面板
// 专门针对 https://yunpan.gdcourts.gov.cn:82/ 网站的注入面板

class YlzInjectedPanel {
    constructor() {
        this.panel = null;
        this.isMinimized = false;
        this.isDragging = false;
        this.dragOffset = { x: 0, y: 0 };
        this.currentTab = 'pending';

        // 状态数据
        this.connectionStatus = false;
        this.yunpanStatus = false;
        this.fileData = {
            pending: [],
            uploaded: [],
            pendingCount: 0,
            uploadedCount: 0
        };

        // 自动上传相关属性
        this.autoUploadEnabled = false;
        this.lastFileCount = 0;
        this.uploadInProgress = false;

        // 定时器
        this.refreshTimer = null;

        // 文件树组件实例
        this.treeTrees = {};

        this.init();
    }

    // 初始化面板
    async init() {
        try {
            console.log('[YLZ注入面板] 开始初始化面板');

            // 检查是否已经存在面板元素
            const existingPanel = document.getElementById('ylz-injected-panel');
            if (existingPanel) {
                console.log('[YLZ注入面板] 发现现有面板，使用现有元素');
                this.panel = existingPanel;
            } else {
                console.log('[YLZ注入面板] 未找到现有面板，加载新面板');
                // 加载面板HTML
                await this.loadPanelHTML();
            }

            // 确保面板元素存在
            if (!this.panel) {
                throw new Error('面板元素未能正确创建');
            }

            // 绑定事件
            this.bindEvents();

            // 初始化数据
            await this.initializeData();

            console.log('[YLZ注入面板] 初始化完成');
        } catch (error) {
            console.error('[YLZ注入面板] 初始化失败:', error);
        }
    }

    // 加载面板HTML
    async loadPanelHTML() {
        try {
            console.log('[YLZ注入面板] 开始加载HTML模板');

            // 检查是否已存在面板
            if (document.getElementById('ylz-injected-panel')) {
                console.log('[YLZ注入面板] 面板已存在，使用现有面板');
                this.panel = document.getElementById('ylz-injected-panel');
                return;
            }

            // 获取扩展的HTML模板
            const response = await fetch(chrome.runtime.getURL('injected-panel.html'));
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const htmlText = await response.text();
            console.log('[YLZ注入面板] HTML模板加载成功，长度:', htmlText.length);

            // 解析HTML并提取面板内容
            const parser = new DOMParser();
            const doc = parser.parseFromString(htmlText, 'text/html');
            const panelElement = doc.getElementById('ylz-injected-panel');

            if (panelElement) {
                // 确保面板样式正确
                panelElement.style.zIndex = '2147483647';
                panelElement.style.position = 'fixed';

                // 将面板添加到页面
                document.body.appendChild(panelElement);
                this.panel = panelElement;

                // 添加样式
                this.injectStyles();

                console.log('[YLZ注入面板] 面板HTML加载完成');
            } else {
                throw new Error('无法找到面板元素');
            }
        } catch (error) {
            console.error('[YLZ注入面板] 加载HTML失败:', error);
            // 如果加载失败，创建简化版面板
            this.createFallbackPanel();
        }
    }

    // 创建备用面板（如果HTML加载失败）
    createFallbackPanel() {
        const panelHTML = `
            <div id="ylz-injected-panel" style="position: fixed; top: 20px; right: 20px; width: 480px; max-height: 80vh; background: white; border: 1px solid #e0e0e0; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.15); z-index: 999999; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                <div class="ylz-panel-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 16px; display: flex; justify-content: space-between; align-items: center; cursor: move;">
                    <h1 style="margin: 0; font-size: 16px; font-weight: 600;">文件云流转助手</h1>
                    <div>
                        <button id="ylz-minimize-btn" style="background: rgba(255,255,255,0.2); border: none; color: white; width: 24px; height: 24px; border-radius: 4px; cursor: pointer; margin-left: 8px;">−</button>
                        <button id="ylz-close-btn" style="background: rgba(255,255,255,0.2); border: none; color: white; width: 24px; height: 24px; border-radius: 4px; cursor: pointer; margin-left: 8px;">×</button>
                    </div>
                </div>
                <div class="ylz-panel-content" style="padding: 16px;">
                    <div style="text-align: center; padding: 20px; color: #666;">
                        <p>文件云流转助手已激活</p>
                        <p>正在加载完整界面...</p>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', panelHTML);
        this.panel = document.getElementById('ylz-injected-panel');
    }

    // 注入样式
    injectStyles() {
        // 样式已经在HTML中内联，这里可以添加额外的样式调整
        const style = document.createElement('style');
        style.textContent = `
            /* 确保面板在所有元素之上 */
            #ylz-injected-panel {
                z-index: 2147483647 !important;
            }
            
            /* 防止页面样式影响面板 */
            #ylz-injected-panel * {
                box-sizing: border-box !important;
            }
        `;
        document.head.appendChild(style);
    }

    // 绑定事件
    bindEvents() {
        if (!this.panel) {
            console.error('[YLZ注入面板] 面板元素不存在，无法绑定事件');
            return;
        }

        console.log('[YLZ注入面板] 开始绑定事件');

        // 面板控制按钮
        const minimizeBtn = this.panel.querySelector('#ylz-minimize-btn');
        const closeBtn = this.panel.querySelector('#ylz-close-btn');

        if (minimizeBtn) {
            console.log('[YLZ注入面板] 绑定最小化按钮');
            minimizeBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('[YLZ注入面板] 最小化按钮被点击');
                this.toggleMinimize();
            });
        } else {
            console.warn('[YLZ注入面板] 未找到最小化按钮');
        }

        if (closeBtn) {
            console.log('[YLZ注入面板] 绑定关闭按钮');
            closeBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('[YLZ注入面板] 关闭按钮被点击');
                this.hide();
            });
        } else {
            console.warn('[YLZ注入面板] 未找到关闭按钮');
        }

        // 拖拽功能
        const header = this.panel.querySelector('.ylz-panel-header');
        if (header) {
            console.log('[YLZ注入面板] 绑定拖拽功能');
            header.addEventListener('mousedown', (e) => {
                // 确保不是点击按钮时才开始拖拽
                if (!e.target.closest('.ylz-control-btn')) {
                    console.log('[YLZ注入面板] 开始拖拽');
                    this.startDrag(e);
                }
            });

            // 添加拖拽样式提示
            header.style.cursor = 'move';
        } else {
            console.warn('[YLZ注入面板] 未找到面板头部');
        }

        // 标签页切换
        const tabButtons = this.panel.querySelectorAll('.ylz-tab-button');
        console.log(`[YLZ注入面板] 找到 ${tabButtons.length} 个标签按钮`);
        tabButtons.forEach((button) => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const tab = e.currentTarget.dataset.tab;
                console.log(`[YLZ注入面板] 切换到标签页: ${tab}`);
                this.switchTab(tab);
            });
        });

        // 操作按钮
        this.bindActionButtons();

        // 全局鼠标事件（用于拖拽）
        document.addEventListener('mousemove', (e) => this.onDrag(e), { passive: true });
        document.addEventListener('mouseup', () => this.endDrag(), { passive: true });

        // 监听来自background的消息（通过页面事件）
        window.addEventListener('ylz_background_message', (event) => {
            this.handleMessage(event.detail, null, null);
        });

        console.log('[YLZ注入面板] 事件绑定完成');
    }

    // 绑定操作按钮事件
    bindActionButtons() {
        const buttons = {
            'ylz-uploadAll': () => this.startUpload(),
            'ylz-openCloud': () => this.openCloud(),
            'ylz-refreshList': () => this.refreshList(),
            'ylz-expandAllPending': () => this.expandAll('pending'),
            'ylz-collapseAllPending': () => this.collapseAll('pending'),
            'ylz-expandAllUploaded': () => this.expandAll('uploaded'),
            'ylz-collapseAllUploaded': () => this.collapseAll('uploaded')
        };

        Object.entries(buttons).forEach(([id, handler]) => {
            const button = this.panel.querySelector(`#${id}`);
            if (button) {
                button.addEventListener('click', handler);
            }
        });

        // 自动上传开关
        const autoUploadSwitch = this.panel.querySelector('#ylz-autoUploadSwitch');
        if (autoUploadSwitch) {
            autoUploadSwitch.addEventListener('change', (e) => {
                this.toggleAutoUpload(e.target.checked);
            });
        }

        // 自动刷新选择
        const autoRefreshSelect = this.panel.querySelector('#ylz-autoRefreshSelect');
        if (autoRefreshSelect) {
            autoRefreshSelect.addEventListener('change', (e) => {
                this.setAutoRefresh(parseInt(e.target.value));
            });
        }

        // 允许刷新当前页面勾选框
        const allowActiveRefreshCheckbox = this.panel.querySelector('#ylz-allowActiveRefresh');
        if (allowActiveRefreshCheckbox) {
            allowActiveRefreshCheckbox.addEventListener('change', (e) => {
                this.setAllowActiveRefresh(e.target.checked);
            });
        }
    }

    // 处理来自background的消息
    handleMessage(message, sender, sendResponse) {
        // sender参数保留用于未来扩展
        console.log('[YLZ注入面板] 收到消息:', message);

        switch (message.type) {
            case 'CONNECTION_STATUS_CHANGED':
                console.log('[YLZ注入面板] 连接状态变化:', message.connected);
                this.updateConnectionStatus(message.connected);

                // 连接状态变化时，重新获取数据
                if (message.connected) {
                    setTimeout(() => {
                        this.initializeData();
                    }, 1000);
                } else {
                    this.stopPeriodicRefresh();
                }
                break;

            case 'admin_tree_updated':
                console.log('[YLZ注入面板] 文件树更新');
                // 重新获取最新的文件树数据
                this.refreshFileTree();
                break;

            case 'files_updated':
                console.log('[YLZ注入面板] 文件列表更新');
                this.updateFileList(message);
                break;

            case 'upload_progress':
                console.log('[YLZ注入面板] 上传进度更新');
                this.updateUploadProgress(message);
                break;

            case 'file_status_updated':
                console.log('[YLZ注入面板] 文件状态更新');
                // 文件状态更新时，刷新文件树
                this.refreshFileTree();
                break;

            case 'PAGE_REFRESHED':
                console.log('[YLZ注入面板] 页面已自动刷新');
                this.showNotification(
                    `页面已自动刷新 (${Math.floor(message.interval / 60)}分钟间隔)`,
                    'info'
                );

                // 页面刷新后重新初始化数据
                setTimeout(() => {
                    this.initializeData();
                }, 3000); // 等待页面加载完成
                break;

            case 'REFRESH_SKIPPED':
                console.log('[YLZ注入面板] 自动刷新被跳过:', message.reason);
                if (message.reason === 'active_tab') {
                    this.showNotification(
                        '自动刷新已跳过（页面正在使用中）',
                        'warning'
                    );
                }
                break;

            default:
                console.log('[YLZ注入面板] 收到未知消息:', message);
        }

        // 如果有回调函数，调用它
        if (sendResponse) {
            sendResponse({ received: true });
        }
    }

    // 初始化数据
    async initializeData() {
        try {
            console.log('[YLZ注入面板] 开始初始化数据');

            // 检查云盘页面状态（这个不需要等待）
            this.checkYunpanStatus();

            // 1. 首先检查连接状态
            try {
                const connectionResponse = await this.safeRuntimeMessage({ type: 'GET_CONNECTION_STATUS' });
                console.log('[YLZ注入面板] 连接状态响应:', connectionResponse);

                // 处理连接状态，null也视为未连接
                const isConnected = connectionResponse?.connected === true;
                console.log('[YLZ注入面板] 处理后的连接状态:', isConnected);
                this.updateConnectionStatus(isConnected);
            } catch (err) {
                console.error('[YLZ注入面板] 获取连接状态失败:', err);
                this.updateConnectionStatus(false);
            }

            // 2. 获取文件树数据
            try {
                const treeResponse = await this.safeRuntimeMessage({ type: 'GET_ADMIN_TREE' });
                console.log('[YLZ注入面板] 文件树响应:', treeResponse);
                if (treeResponse?.success && treeResponse.tree) {
                    this.updateFileTree(treeResponse.tree);
                } else {
                    console.warn('[YLZ注入面板] 文件树数据无效:', treeResponse);
                    // 显示空状态
                    this.updateFileTree(null);
                }
            } catch (err) {
                console.error('[YLZ注入面板] 获取文件树失败:', err);
                this.updateFileTree(null);
            }

            // 3. 获取自动上传状态
            try {
                const autoUploadResponse = await this.safeRuntimeMessage({ type: 'GET_AUTO_UPLOAD_STATUS' });
                console.log('[YLZ注入面板] 自动上传状态响应:', autoUploadResponse);

                // 更新面板的自动上传状态
                this.autoUploadEnabled = autoUploadResponse?.enabled || false;

                const autoUploadSwitch = this.panel?.querySelector('#ylz-autoUploadSwitch');
                if (autoUploadSwitch) {
                    autoUploadSwitch.checked = this.autoUploadEnabled;
                    console.log('[YLZ注入面板] 自动上传开关状态已设置:', this.autoUploadEnabled);

                    // 绑定开关变化事件
                    autoUploadSwitch.addEventListener('change', (e) => {
                        this.autoUploadEnabled = e.target.checked;
                        console.log('[YLZ注入面板] 自动上传状态已更改:', this.autoUploadEnabled);

                        // 保存到本地存储
                        chrome.storage.local.set({ autoUploadEnabled: this.autoUploadEnabled });

                        // 通知后台
                        this.safeRuntimeMessage({
                            type: 'SET_AUTO_UPLOAD_STATE',
                            enabled: this.autoUploadEnabled
                        });

                        this.showNotification(
                            `自动上传已${this.autoUploadEnabled ? '启用' : '禁用'}`,
                            'info'
                        );

                        // 如果启用且有待上传文件，立即检查
                        if (this.autoUploadEnabled && this.fileData.pendingCount > 0) {
                            setTimeout(() => {
                                this.triggerAutoUpload();
                            }, 1000);
                        }
                    });
                }
            } catch (err) {
                console.error('[YLZ注入面板] 获取自动上传状态失败:', err);
            }

            // 4. 获取自动刷新状态并设置默认值
            try {
                const autoRefreshResponse = await this.safeRuntimeMessage({ type: 'GET_AUTO_REFRESH_STATUS' });
                console.log('[YLZ注入面板] 自动刷新状态响应:', autoRefreshResponse);

                const autoRefreshSelect = this.panel?.querySelector('#ylz-autoRefreshSelect');
                if (autoRefreshSelect) {
                    if (autoRefreshResponse?.enabled && autoRefreshResponse.interval > 0) {
                        // 设置当前选中的间隔
                        autoRefreshSelect.value = autoRefreshResponse.interval.toString();
                        console.log('[YLZ注入面板] 自动刷新选择器状态已设置:', autoRefreshResponse.interval);
                    } else {
                        // 设置默认值为5分钟（300秒）
                        autoRefreshSelect.value = '300';
                        console.log('[YLZ注入面板] 设置自动刷新默认值: 300秒');

                        // 自动启用默认的自动刷新
                        setTimeout(() => {
                            this.setAutoRefresh(300);
                        }, 2000);
                    }
                }
            } catch (err) {
                console.error('[YLZ注入面板] 获取自动刷新状态失败:', err);

                // 如果获取失败，仍然设置默认值
                const autoRefreshSelect = this.panel?.querySelector('#ylz-autoRefreshSelect');
                if (autoRefreshSelect) {
                    autoRefreshSelect.value = '300';
                    setTimeout(() => {
                        this.setAutoRefresh(300);
                    }, 2000);
                }
            }

            // 5. 获取并设置允许刷新当前页面的状态
            try {
                const response = await this.safeRuntimeMessage({ type: 'GET_ALLOW_ACTIVE_REFRESH' });
                const allowActiveRefreshCheckbox = this.panel?.querySelector('#ylz-allowActiveRefresh');
                if (allowActiveRefreshCheckbox) {
                    allowActiveRefreshCheckbox.checked = response?.allowed === true;
                    console.log('[YLZ注入面板] 允许刷新当前页面状态已设置:', response?.allowed);
                }
            } catch (err) {
                console.error('[YLZ注入面板] 获取允许刷新当前页面状态失败:', err);
            }

            // 4. 启动定期刷新（如果连接正常）
            if (this.connectionStatus) {
                this.startPeriodicRefresh();
            } else {
                // 如果连接断开，尝试重新连接
                console.log('[YLZ注入面板] 连接断开，尝试重新连接...');
                this.attemptReconnection();
            }

            console.log('[YLZ注入面板] 初始化数据完成');

            // 启动定期连接状态检查
            this.startConnectionMonitoring();

        } catch (error) {
            console.error('[YLZ注入面板] 初始化数据失败:', error);
            // 即使失败也显示面板，只是可能没有数据
            this.updateFileTree(null);
        }
    }

    // 启动连接状态监控
    startConnectionMonitoring() {
        // 每30秒检查一次连接状态
        this.connectionMonitorTimer = setInterval(() => {
            this.checkConnectionStatus();
        }, 30000);

        console.log('[YLZ注入面板] 连接状态监控已启动');
    }

    // 停止连接状态监控
    stopConnectionMonitoring() {
        if (this.connectionMonitorTimer) {
            clearInterval(this.connectionMonitorTimer);
            this.connectionMonitorTimer = null;
            console.log('[YLZ注入面板] 连接状态监控已停止');
        }
    }

    // 启动定期刷新
    startPeriodicRefresh() {
        // 清除现有的定时器
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }

        // 每30秒刷新一次数据
        this.refreshTimer = setInterval(async () => {
            try {
                console.log('[YLZ注入面板] 定期刷新数据');
                await this.refreshList();
            } catch (error) {
                console.error('[YLZ注入面板] 定期刷新失败:', error);
            }
        }, 30000);

        console.log('[YLZ注入面板] 定期刷新已启动');
    }

    // 停止定期刷新
    stopPeriodicRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
            console.log('[YLZ注入面板] 定期刷新已停止');
        }
    }

    // 尝试重新连接WebSocket
    async attemptReconnection() {
        try {
            console.log('[YLZ注入面板] 尝试重新连接WebSocket...');

            // 发送重置连接请求
            const response = await this.safeRuntimeMessage({ type: 'RESET_CONNECTION' });

            if (response && response.success) {
                console.log('[YLZ注入面板] 重新连接成功');
                this.showNotification('连接已恢复', 'success');

                // 等待一段时间后重新检查连接状态
                setTimeout(() => {
                    this.checkConnectionStatus();
                }, 3000);
            } else {
                console.log('[YLZ注入面板] 重新连接失败:', response);
                this.showNotification('重新连接失败', 'error');

                // 5秒后再次尝试
                setTimeout(() => {
                    this.attemptReconnection();
                }, 5000);
            }
        } catch (error) {
            console.error('[YLZ注入面板] 重新连接异常:', error);

            // 10秒后再次尝试
            setTimeout(() => {
                this.attemptReconnection();
            }, 10000);
        }
    }

    // 检查连接状态
    async checkConnectionStatus() {
        try {
            const response = await this.safeRuntimeMessage({ type: 'GET_CONNECTION_STATUS' });

            if (response && response.connected) {
                console.log('[YLZ注入面板] 连接状态检查：已连接');
                this.updateConnectionStatus(true);

                // 如果连接恢复且定期刷新未启动，则启动
                if (!this.refreshTimer) {
                    this.startPeriodicRefresh();
                }
            } else {
                console.log('[YLZ注入面板] 连接状态检查：未连接');
                this.updateConnectionStatus(false);
            }
        } catch (error) {
            console.error('[YLZ注入面板] 连接状态检查失败:', error);
            this.updateConnectionStatus(false);
        }
    }

    // 安全的消息发送方法，通过页面事件与content script通信
    safeRuntimeMessage(message, timeout = 5000) {
        return new Promise((resolve, reject) => {
            console.log('[YLZ注入面板] 发送消息:', message);

            // 生成唯一的消息ID
            const messageId = 'ylz_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            // 创建超时处理
            const timeoutId = setTimeout(() => {
                window.removeEventListener('ylz_panel_response_' + messageId, responseHandler);
                reject(new Error('消息响应超时'));
            }, timeout);

            // 响应处理函数
            const responseHandler = (event) => {
                clearTimeout(timeoutId);
                window.removeEventListener('ylz_panel_response_' + messageId, responseHandler);

                console.log('[YLZ注入面板] 收到响应:', event.detail);
                resolve(event.detail);
            };

            // 监听响应
            window.addEventListener('ylz_panel_response_' + messageId, responseHandler);

            // 发送消息到content script
            const customEvent = new CustomEvent('ylz_panel_message', {
                detail: {
                    messageId: messageId,
                    message: message
                }
            });

            window.dispatchEvent(customEvent);
        });
    }

    // 更新连接状态
    updateConnectionStatus(connected) {
        this.connectionStatus = connected;
        
        const statusDot = this.panel?.querySelector('#ylz-connectionStatusDot');
        const statusText = this.panel?.querySelector('#ylz-connectionStatusText');
        
        if (statusDot) {
            statusDot.className = `ylz-status-dot ${connected ? 'connected' : ''}`;
        }
        
        if (statusText) {
            statusText.textContent = connected ? '已连接' : '连接断开';
        }
    }

    // 检查云盘状态
    checkYunpanStatus() {
        // 检查当前页面是否为云盘页面
        const isYunpanPage = window.location.href.includes('yunpan.gdcourts.gov.cn');
        this.yunpanStatus = isYunpanPage;
        
        const yunpanStatus = this.panel?.querySelector('#ylz-yunpan-status');
        if (yunpanStatus) {
            yunpanStatus.textContent = isYunpanPage ? '云盘已连接' : '云盘未连接';
            yunpanStatus.className = `ylz-yunpan-status ${isYunpanPage ? 'connected' : ''}`;
        }
    }

    // 切换标签页
    switchTab(tabName) {
        this.currentTab = tabName;
        
        // 更新标签按钮状态
        const tabButtons = this.panel.querySelectorAll('.ylz-tab-button');
        tabButtons.forEach(button => {
            button.classList.toggle('active', button.dataset.tab === tabName);
        });
        
        // 更新面板内容
        const tabPanels = this.panel.querySelectorAll('.ylz-tab-panel');
        tabPanels.forEach(panel => {
            panel.classList.toggle('active', panel.id === `ylz-${tabName}Panel`);
        });
    }

    // 开始上传（手动触发）
    async startUpload() {
        try {
            console.log('[YLZ注入面板] 开始手动上传操作');

            // 检查是否有待上传文件
            if (this.fileData.pendingCount === 0) {
                console.log('[YLZ注入面板] 没有待上传文件');
                this.showNotification('没有待上传文件', 'info');
                return;
            }

            // 使用新的触发逻辑
            const triggerRequest = {
                type: 'TRIGGER_AUTO_UPLOAD',
                source: 'manual_upload_button',
                timestamp: Date.now()
            };

            console.log('[YLZ注入面板] 发送手动上传触发请求:', triggerRequest);
            const response = await this.safeRuntimeMessage(triggerRequest);

            console.log('[YLZ注入面板] 手动上传触发响应:', response);

            if (response?.success) {
                this.showNotification('手动上传已启动...', 'info');

                // 刷新文件树
                setTimeout(() => {
                    this.refreshFileTree();
                }, 3000);
            } else {
                this.showNotification('上传启动失败: ' + (response?.error || '未知错误'), 'error');
            }
        } catch (error) {
            console.error('[YLZ注入面板] 手动上传启动失败:', error);
            this.showNotification('上传启动失败: ' + error.message, 'error');
        }
    }

    // 打开云盘
    openCloud() {
        window.open('https://yunpan.gdcourts.gov.cn:82/', '_blank');
    }

    // 刷新列表
    async refreshList() {
        try {
            console.log('[YLZ注入面板] 开始刷新文件列表');
            const response = await this.safeRuntimeMessage({
                type: 'SEND_WS_MESSAGE',
                wsMessage: { type: 'get_full_admin_file_tree' }
            });

            if (response?.success !== false) {
                this.showNotification('正在刷新文件列表...', 'info');
                // 延迟一段时间后重新获取数据
                setTimeout(() => {
                    this.initializeData();
                }, 2000);
            } else {
                this.showNotification('刷新失败: ' + (response?.error || '未知错误'), 'error');
            }
        } catch (error) {
            console.error('[YLZ注入面板] 刷新列表失败:', error);
            this.showNotification('刷新失败: ' + error.message, 'error');
        }
    }

    // 展开所有目录
    expandAll(tab) {
        const container = this.panel.querySelector(`#ylz-${tab}TreeContainer`);
        if (container) {
            const expandableItems = container.querySelectorAll('.tree-item.expandable');
            expandableItems.forEach(item => {
                item.classList.add('expanded');
            });
        }
    }

    // 折叠所有目录
    collapseAll(tab) {
        const container = this.panel.querySelector(`#ylz-${tab}TreeContainer`);
        if (container) {
            const expandableItems = container.querySelectorAll('.tree-item.expandable');
            expandableItems.forEach(item => {
                item.classList.remove('expanded');
            });
        }
    }

    // 切换自动上传
    async toggleAutoUpload(enabled) {
        try {
            console.log('[YLZ注入面板] 切换自动上传:', enabled);
            const response = await this.safeRuntimeMessage({
                type: 'SET_AUTO_UPLOAD',
                enabled: enabled
            });

            if (response?.success !== false) {
                this.showNotification(
                    enabled ? '自动上传已启用' : '自动上传已禁用',
                    'info'
                );
            } else {
                this.showNotification('设置失败: ' + (response?.error || '未知错误'), 'error');
            }
        } catch (error) {
            console.error('[YLZ注入面板] 切换自动上传失败:', error);
            this.showNotification('设置失败: ' + error.message, 'error');
        }
    }

    // 设置自动刷新
    async setAutoRefresh(interval) {
        try {
            console.log('[YLZ注入面板] 设置自动刷新:', interval);
            const response = await this.safeRuntimeMessage({
                type: 'SET_AUTO_REFRESH',
                interval: interval
            });

            if (response?.success !== false) {
                const message = interval > 0 ?
                    `自动刷新已设置为${interval}秒` :
                    '自动刷新已关闭';
                this.showNotification(message, 'info');
            } else {
                this.showNotification('设置失败: ' + (response?.error || '未知错误'), 'error');
            }
        } catch (error) {
            console.error('[YLZ注入面板] 设置自动刷新失败:', error);
            this.showNotification('设置失败: ' + error.message, 'error');
        }
    }

    // 设置是否允许刷新当前页面
    async setAllowActiveRefresh(allowed) {
        try {
            console.log('[YLZ注入面板] 设置允许刷新当前页面:', allowed);

            // 通过消息传递保存到Chrome存储
            const response = await this.safeRuntimeMessage({
                type: 'SET_ALLOW_ACTIVE_REFRESH',
                allowed: allowed
            });

            if (response?.success !== false) {
                const message = allowed ?
                    '已启用刷新当前页面' :
                    '已禁用刷新当前页面';
                this.showNotification(message, 'info');
            } else {
                this.showNotification('设置失败: ' + (response?.error || '未知错误'), 'error');
            }

        } catch (error) {
            console.error('[YLZ注入面板] 设置允许刷新当前页面失败:', error);
            this.showNotification('设置失败: ' + error.message, 'error');
        }
    }

    // 拖拽相关方法
    startDrag(e) {
        console.log('[YLZ注入面板] 开始拖拽操作');
        this.isDragging = true;
        this.panel.classList.add('dragging');

        const rect = this.panel.getBoundingClientRect();
        this.dragOffset = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };

        // 防止文本选择和其他默认行为
        e.preventDefault();
        e.stopPropagation();

        // 添加拖拽时的样式
        this.panel.style.transition = 'none';
        this.panel.style.userSelect = 'none';
        document.body.style.userSelect = 'none';

        console.log('[YLZ注入面板] 拖拽初始化完成', this.dragOffset);
    }

    onDrag(e) {
        if (!this.isDragging || !this.panel) return;

        const x = e.clientX - this.dragOffset.x;
        const y = e.clientY - this.dragOffset.y;

        // 获取窗口尺寸和面板尺寸
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        const panelWidth = this.panel.offsetWidth;
        const panelHeight = this.panel.offsetHeight;

        // 限制拖拽范围，确保面板不会完全移出视窗
        const minX = -panelWidth + 100; // 允许部分移出，但保留100px可见
        const maxX = windowWidth - 100;
        const minY = 0; // 顶部不能移出
        const maxY = windowHeight - 60; // 底部保留60px（标题栏高度）

        const constrainedX = Math.max(minX, Math.min(x, maxX));
        const constrainedY = Math.max(minY, Math.min(y, maxY));

        // 应用新位置
        this.panel.style.left = constrainedX + 'px';
        this.panel.style.top = constrainedY + 'px';
        this.panel.style.right = 'auto';
        this.panel.style.bottom = 'auto';

        // 调试信息
        if (Math.random() < 0.1) { // 只打印10%的拖拽事件，避免日志过多
            console.log('[YLZ注入面板] 拖拽位置:', { x: constrainedX, y: constrainedY });
        }
    }

    endDrag() {
        if (this.isDragging) {
            console.log('[YLZ注入面板] 结束拖拽操作');
            this.isDragging = false;
            this.panel.classList.remove('dragging');

            // 恢复样式
            this.panel.style.transition = 'all 0.3s ease';
            this.panel.style.userSelect = '';
            document.body.style.userSelect = '';

            // 保存位置到localStorage（可选）
            try {
                const rect = this.panel.getBoundingClientRect();
                localStorage.setItem('ylz-panel-position', JSON.stringify({
                    x: rect.left,
                    y: rect.top
                }));
            } catch (e) {
                console.warn('[YLZ注入面板] 保存位置失败:', e);
            }
        }
    }

    // 切换最小化
    toggleMinimize() {
        console.log('[YLZ注入面板] 切换最小化状态');
        this.isMinimized = !this.isMinimized;

        const content = this.panel.querySelector('.ylz-panel-content');
        const minimizeBtn = this.panel.querySelector('#ylz-minimize-btn');

        if (content) {
            if (this.isMinimized) {
                content.style.display = 'none';
                this.panel.style.height = '60px';
            } else {
                content.style.display = 'block';
                this.panel.style.height = 'auto';
            }
        }

        if (minimizeBtn) {
            minimizeBtn.textContent = this.isMinimized ? '+' : '−';
            minimizeBtn.title = this.isMinimized ? '展开' : '最小化';
        }

        console.log('[YLZ注入面板] 最小化状态:', this.isMinimized);
    }

    // 显示面板
    show() {
        console.log('[YLZ注入面板] 显示面板');
        if (this.panel) {
            this.panel.style.display = 'block';
            this.panel.classList.remove('hidden');

            // 恢复位置
            this.restorePosition();
        }
    }

    // 隐藏面板
    hide() {
        console.log('[YLZ注入面板] 隐藏面板');
        if (this.panel) {
            this.panel.style.display = 'none';
            this.panel.classList.add('hidden');
        }
    }

    // 恢复面板位置
    restorePosition() {
        try {
            const savedPosition = localStorage.getItem('ylz-panel-position');
            if (savedPosition) {
                const position = JSON.parse(savedPosition);

                // 验证位置是否在有效范围内
                const windowWidth = window.innerWidth;
                const windowHeight = window.innerHeight;

                if (position.x < windowWidth && position.y < windowHeight) {
                    this.panel.style.left = position.x + 'px';
                    this.panel.style.top = position.y + 'px';
                    this.panel.style.right = 'auto';
                    this.panel.style.bottom = 'auto';

                    console.log('[YLZ注入面板] 恢复位置:', position);
                }
            }
        } catch (e) {
            console.warn('[YLZ注入面板] 恢复位置失败:', e);
        }
    }

    // 显示通知
    showNotification(message, type = 'info') {
        // 创建简单的通知
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'error' ? '#dc3545' : '#007bff'};
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            z-index: 2147483648;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // 更新文件树
    updateFileTree(treeData) {
        console.log('[YLZ注入面板] 更新文件树:', treeData);

        if (!treeData) return;

        // 分离待上传和已上传文件树
        const { pendingFiles, uploadedFiles, pendingCount, uploadedCount } = this.categorizeFiles(treeData);

        // 更新文件数据
        this.fileData.pending = pendingFiles;
        this.fileData.uploaded = uploadedFiles;
        this.fileData.pendingCount = pendingCount;
        this.fileData.uploadedCount = uploadedCount;

        // 更新计数显示
        this.updateFileCounts();

        // 渲染文件树（异步）
        this.renderFileTree('pending', pendingFiles).catch(error => {
            console.error('[YLZ注入面板] 渲染pending文件树失败:', error);
        });
        this.renderFileTree('uploaded', uploadedFiles).catch(error => {
            console.error('[YLZ注入面板] 渲染uploaded文件树失败:', error);
        });

        // 检查是否需要触发自动上传
        this.checkAutoUpload(pendingCount);
    }

    // 分类文件（待上传/已上传）- 保持树状结构
    categorizeFiles(treeData) {
        console.log('[YLZ注入面板] 开始分类文件，原始数据:', treeData);

        // 检查文件是否已上传（与popup逻辑一致）
        const isFileUploaded = (file) => {
            if (!file) return false;

            const serverStatus = file.syncStatus || file.status;
            const uploadedStatuses = ['已同步', '已上传', 'uploaded'];
            const isUploaded = uploadedStatuses.includes(serverStatus);

            console.log(`[YLZ注入面板] 文件状态检查: ${file.name || file.filename} -> ${serverStatus} (${isUploaded ? '已上传' : '未上传'})`);
            return isUploaded;
        };

        // 过滤树结构，保持目录层级
        const filterTreeByStatus = (tree, showUploaded) => {
            if (!tree) return null;

            const filtered = {
                files: [],
                folders: []
            };

            // 过滤文件
            if (tree.files && Array.isArray(tree.files)) {
                filtered.files = tree.files.filter(file => {
                    const isUploaded = isFileUploaded(file);
                    return showUploaded ? isUploaded : !isUploaded;
                });
            }

            // 递归过滤文件夹
            if (tree.folders && Array.isArray(tree.folders)) {
                tree.folders.forEach(folder => {
                    const filteredFolder = {
                        ...folder,
                        children: filterTreeByStatus(folder.children, showUploaded)
                    };

                    // 只有当文件夹包含文件或子文件夹时才添加
                    if ((filteredFolder.children?.files?.length > 0) ||
                        (filteredFolder.children?.folders?.length > 0)) {
                        filtered.folders.push(filteredFolder);
                    }
                });
            }

            return filtered;
        };

        const pendingTree = filterTreeByStatus(treeData, false);
        const uploadedTree = filterTreeByStatus(treeData, true);

        // 计算文件总数
        const countFiles = (tree) => {
            if (!tree) return 0;
            let count = tree.files ? tree.files.length : 0;
            if (tree.folders) {
                tree.folders.forEach(folder => {
                    count += countFiles(folder.children);
                });
            }
            return count;
        };

        const pendingCount = countFiles(pendingTree);
        const uploadedCount = countFiles(uploadedTree);

        console.log('[YLZ注入面板] 文件分类完成:', {
            pending: pendingCount,
            uploaded: uploadedCount,
            pendingTree: pendingTree,
            uploadedTree: uploadedTree
        });

        return {
            pendingFiles: pendingTree,
            uploadedFiles: uploadedTree,
            pendingCount,
            uploadedCount
        };
    }

    // 渲染文件树
    async renderFileTree(type, files) {
        const containerId = `ylz-${type}TreeContainer`;
        const container = this.panel?.querySelector(`#${containerId}`);

        if (!container) {
            console.warn(`[YLZ注入面板] 容器 ${containerId} 不存在`);
            return;
        }

        console.log(`[YLZ注入面板] 渲染 ${type} 文件树，文件数量:`, files?.length || 0);

        // 创建或获取树组件实例
        if (!this.treeTrees) {
            this.treeTrees = {};
        }

        if (!this.treeTrees[type]) {
            try {
                // 等待树组件加载完成
                await this.loadTreeComponent();

                if (window.InjectedPanelTree) {
                    console.log(`[YLZ注入面板] 创建 ${type} 树实例`);
                    this.treeTrees[type] = new window.InjectedPanelTree(container, {
                        showThumbnails: true,
                        allowSelection: true,
                        expandable: true
                    });

                    // 绑定事件
                    container.addEventListener('itemClick', (e) => {
                        this.handleTreeItemClick(e.detail.item, type);
                    });

                    // 渲染数据
                    console.log(`[YLZ注入面板] 渲染 ${type} 数据:`, files);
                    this.treeTrees[type].render(files);
                } else {
                    console.warn(`[YLZ注入面板] 树组件创建失败，使用备用方法`);
                    // 备用渲染方法
                    this.renderSimpleFileList(container, files);
                }
            } catch (error) {
                console.error(`[YLZ注入面板] 创建 ${type} 树实例失败:`, error);
                this.renderSimpleFileList(container, files);
            }
        } else {
            // 更新现有树
            console.log(`[YLZ注入面板] 更新 ${type} 树数据:`, files);
            this.treeTrees[type].refresh(files);
        }
    }

    // 动态加载树组件
    async loadTreeComponent() {
        if (window.InjectedPanelTree) return;

        try {
            console.log('[YLZ注入面板] 使用简化文件树组件');

            // 直接创建简化的树组件，不依赖外部文件
            this.createFallbackTree();
        } catch (error) {
            console.error('[YLZ注入面板] 加载树组件失败:', error);
        }
    }

    // 创建简化的文件树显示
    createFallbackTree() {
        console.log('[YLZ注入面板] 创建简化文件树组件');

        // 创建简化的树组件类
        window.InjectedPanelTree = class {
            constructor(container, options) {
                this.container = container;
                this.options = options;
            }

            // render方法（与refresh方法相同）
            render(files) {
                this.refresh(files);
            }

            refresh(treeData) {
                this.container.innerHTML = '';

                if (!treeData || (!treeData.files?.length && !treeData.folders?.length)) {
                    this.container.innerHTML = '<div style="padding: 20px; text-align: center; color: #666; font-size: 13px;">暂无文件</div>';
                    return;
                }

                const list = document.createElement('div');
                list.style.cssText = 'max-height: 300px; overflow-y: auto;';

                // 渲染树状结构
                this.renderTreeLevel(list, treeData, 0);

                this.container.appendChild(list);
            }

            renderTreeLevel(container, treeData, level) {
                if (!treeData) return;

                const indent = level * 20; // 每级缩进20px

                // 渲染文件夹
                if (treeData.folders && treeData.folders.length > 0) {
                    treeData.folders.forEach(folder => {
                        const folderItem = this.createTreeItem(folder, true, indent);
                        container.appendChild(folderItem);

                        // 创建子级容器
                        const childContainer = document.createElement('div');
                        childContainer.style.display = 'block'; // 默认展开

                        // 递归渲染子级
                        if (folder.children) {
                            this.renderTreeLevel(childContainer, folder.children, level + 1);
                        }

                        container.appendChild(childContainer);
                    });
                }

                // 渲染文件
                if (treeData.files && treeData.files.length > 0) {
                    treeData.files.forEach(file => {
                        const fileItem = this.createTreeItem(file, false, indent);
                        container.appendChild(fileItem);
                    });
                }
            }

            createTreeItem(item, isFolder, indent) {
                const itemEl = document.createElement('div');
                itemEl.style.cssText = `
                    padding: 6px 12px;
                    border-bottom: 1px solid #f0f0f0;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    font-size: 13px;
                    transition: background-color 0.2s;
                    padding-left: ${12 + indent}px;
                `;

                // 悬停效果
                itemEl.addEventListener('mouseenter', () => {
                    itemEl.style.backgroundColor = '#f5f5f5';
                });
                itemEl.addEventListener('mouseleave', () => {
                    itemEl.style.backgroundColor = '';
                });

                // 图标
                const icon = document.createElement('span');
                icon.textContent = isFolder ? '📁' : '📄';
                icon.style.marginRight = '8px';

                // 名称
                const name = document.createElement('span');
                name.textContent = item.name || item.filename;
                name.style.flex = '1';
                name.style.overflow = 'hidden';
                name.style.textOverflow = 'ellipsis';
                name.style.whiteSpace = 'nowrap';

                // 大小或文件数量
                const info = document.createElement('span');
                if (isFolder && item.fileCount) {
                    info.textContent = `${item.fileCount} 项`;
                    info.style.color = '#999';
                    info.style.fontSize = '12px';
                    info.style.marginLeft = '8px';
                } else if (!isFolder && item.size) {
                    info.textContent = this.formatFileSize(item.size);
                    info.style.color = '#999';
                    info.style.fontSize = '12px';
                    info.style.marginLeft = '8px';
                }

                itemEl.appendChild(icon);
                itemEl.appendChild(name);
                if (info.textContent) itemEl.appendChild(info);

                // 点击事件
                itemEl.addEventListener('click', () => {
                    const event = new CustomEvent('itemClick', {
                        detail: { item: item, isFolder: isFolder }
                    });
                    this.container.dispatchEvent(event);
                });

                return itemEl;
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
            }
        };

        console.log('[YLZ注入面板] 简化文件树组件创建完成');
    }

    // 简单文件列表渲染（备用方案）
    renderSimpleFileList(container, files) {
        if (!files || files.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; padding: 20px; color: #666;">
                    📁 暂无文件
                </div>
            `;
            return;
        }

        const listHTML = files.map(file => `
            <div style="
                display: flex;
                align-items: center;
                padding: 8px;
                border-bottom: 1px solid #eee;
                cursor: pointer;
            " data-path="${file.path || file.name}">
                <span style="margin-right: 8px;">
                    ${file.type === 'folder' ? '📁' : '📄'}
                </span>
                <span style="flex: 1; overflow: hidden; text-overflow: ellipsis;">
                    ${file.name || file.filename}
                </span>
                <span style="
                    font-size: 11px;
                    padding: 2px 6px;
                    border-radius: 10px;
                    background: ${this.getStatusColor(file.status || file.syncStatus)};
                    color: white;
                ">
                    ${this.getStatusText(file.status || file.syncStatus)}
                </span>
            </div>
        `).join('');

        container.innerHTML = listHTML;
    }

    // 处理树项点击
    handleTreeItemClick(item, treeType) {
        console.log('[YLZ注入面板] 文件项点击:', item, treeType);

        // 如果是文件，可以触发预览或其他操作
        if (item.type !== 'folder') {
            this.handleFileClick(item);
        }
    }

    // 处理文件点击
    handleFileClick(file) {
        // 可以实现文件预览、下载等功能
        console.log('[YLZ注入面板] 文件点击:', file);

        // 显示文件信息
        this.showNotification(`点击了文件: ${file.name}`, 'info');
    }

    // 更新文件计数
    updateFileCounts() {
        const pendingCount = this.panel?.querySelector('#ylz-pendingCount');
        const uploadedCount = this.panel?.querySelector('#ylz-uploadedCount');
        const fileCountInfo = this.panel?.querySelector('#ylz-fileCountInfo');

        const pendingCountValue = this.fileData.pendingCount || 0;
        const uploadedCountValue = this.fileData.uploadedCount || 0;

        if (pendingCount) {
            pendingCount.textContent = pendingCountValue;
        }

        if (uploadedCount) {
            uploadedCount.textContent = uploadedCountValue;
        }

        if (fileCountInfo) {
            const total = pendingCountValue + uploadedCountValue;
            fileCountInfo.textContent = `共 ${total} 个文件，${pendingCountValue} 个待上传`;
        }
    }

    // 检查自动上传
    async checkAutoUpload(currentFileCount) {
        console.log('[YLZ注入面板] 检查自动上传:', {
            enabled: this.autoUploadEnabled,
            currentFileCount,
            lastFileCount: this.lastFileCount,
            uploadInProgress: this.uploadInProgress
        });

        // 如果自动上传未启用，跳过
        if (!this.autoUploadEnabled) {
            return;
        }

        // 如果正在上传，跳过
        if (this.uploadInProgress) {
            console.log('[YLZ注入面板] 上传正在进行中，跳过自动上传检查');
            return;
        }

        // 检查是否有新文件
        const hasNewFiles = currentFileCount > this.lastFileCount;
        const hasFiles = currentFileCount > 0;

        if (hasFiles && (hasNewFiles || this.lastFileCount === 0)) {
            console.log('[YLZ注入面板] 检测到待上传文件，触发自动上传');
            this.lastFileCount = currentFileCount;

            // 延迟触发，避免频繁调用
            setTimeout(() => {
                this.triggerAutoUpload();
            }, 2000);
        } else {
            this.lastFileCount = currentFileCount;
        }
    }

    // 触发自动上传
    async triggerAutoUpload() {
        if (!this.autoUploadEnabled || this.uploadInProgress) {
            return;
        }

        console.log('[YLZ注入面板] 开始自动上传流程');

        try {
            this.uploadInProgress = true;
            this.showNotification('开始自动上传...', 'info');

            // 检查是否有待上传文件（简单检查）
            if (this.fileData.pendingCount === 0) {
                console.log('[YLZ注入面板] 没有待上传文件');
                this.showNotification('没有待上传文件', 'info');
                return;
            }

            console.log('[YLZ注入面板] 触发自动上传，待上传文件数:', this.fileData.pendingCount);

            // 发送简单的触发消息到background script
            const triggerRequest = {
                type: 'TRIGGER_AUTO_UPLOAD',
                source: 'panel_auto_upload',
                timestamp: Date.now()
            };

            console.log('[YLZ注入面板] 发送上传触发请求:', triggerRequest);
            const response = await this.safeRuntimeMessage(triggerRequest);

            console.log('[YLZ注入面板] 上传触发响应:', response);

            if (response && response.success) {
                this.showNotification(`自动上传已启动`, 'success');

                // 刷新文件树
                setTimeout(() => {
                    this.refreshFileTree();
                }, 3000);
            } else {
                const errorMsg = response?.error || '上传触发失败';
                console.error('[YLZ注入面板] 自动上传触发失败:', errorMsg);
                this.showNotification(`自动上传失败: ${errorMsg}`, 'error');
            }

        } catch (error) {
            console.error('[YLZ注入面板] 自动上传出错:', error);
            this.showNotification(`自动上传出错: ${error.message}`, 'error');
        } finally {
            this.uploadInProgress = false;
        }
    }

    // 获取待上传文件列表
    getUnuploadedFilesList() {
        const unuploadedFiles = [];

        // 递归提取文件
        const extractFiles = (tree, currentPath = '') => {
            if (!tree) return;

            // 提取文件
            if (tree.files && Array.isArray(tree.files)) {
                tree.files.forEach(file => {
                    const filePath = currentPath ? `${currentPath}/${file.name}` : file.name;
                    unuploadedFiles.push({
                        name: file.name || file.filename,
                        path: filePath,
                        size: file.size,
                        type: file.type
                    });
                });
            }

            // 递归处理文件夹
            if (tree.folders && Array.isArray(tree.folders)) {
                tree.folders.forEach(folder => {
                    const folderPath = currentPath ? `${currentPath}/${folder.name}` : folder.name;
                    if (folder.children) {
                        extractFiles(folder.children, folderPath);
                    }
                });
            }
        };

        // 从待上传文件树中提取
        if (this.fileData.pending) {
            extractFiles(this.fileData.pending);
        }

        console.log('[YLZ注入面板] 提取到的待上传文件:', unuploadedFiles);
        return unuploadedFiles;
    }

    // 工具方法
    getStatusColor(status) {
        const colorMap = {
            '未同步': '#ffc107',
            '同步中': '#007bff',
            '已同步': '#28a745',
            '同步失败': '#dc3545',
            'pending': '#ffc107',
            'uploading': '#007bff',
            'uploaded': '#28a745',
            'failed': '#dc3545'
        };

        return colorMap[status] || '#6c757d';
    }

    getStatusText(status) {
        const textMap = {
            '未同步': '待上传',
            '同步中': '上传中',
            '已同步': '已上传',
            '同步失败': '失败',
            'pending': '待上传',
            'uploading': '上传中',
            'uploaded': '已上传',
            'failed': '失败'
        };

        return textMap[status] || '待上传';
    }

    // 刷新文件树
    async refreshFileTree() {
        try {
            console.log('[YLZ注入面板] 刷新文件树');
            const response = await this.safeRuntimeMessage({ type: 'GET_ADMIN_TREE' });
            if (response?.success && response.tree) {
                this.updateFileTree(response.tree);
            } else {
                console.warn('[YLZ注入面板] 刷新文件树失败:', response);
            }
        } catch (error) {
            console.error('[YLZ注入面板] 刷新文件树出错:', error);
        }
    }

    // 更新文件列表
    updateFileList(message) {
        console.log('[YLZ注入面板] 更新文件列表:', message);
        // 当收到文件列表更新消息时，重新获取文件树
        this.refreshFileTree();
    }

    // 更新上传进度
    updateUploadProgress(progress) {
        console.log('[YLZ注入面板] 上传进度:', progress);

        // 更新界面上的进度显示
        if (progress.filename && progress.status) {
            // 可以在这里更新特定文件的状态显示
            this.showNotification(
                `${progress.filename}: ${progress.status}`,
                progress.status === 'completed' ? 'success' : 'info'
            );
        }

        // 如果上传完成，刷新文件树
        if (progress.status === 'completed' || progress.status === 'failed') {
            setTimeout(() => {
                this.refreshFileTree();
            }, 1000);
        }
    }
}

// 全局变量
let ylzInjectedPanel = null;

// 初始化函数
function initYlzInjectedPanel() {
    console.log('[YLZ注入面板] 开始全局初始化');

    // 检查是否为目标网站
    const isTargetSite = window.location.href.includes('yunpan.gdcourts.gov.cn') ||
                        window.location.href.includes('injected-panel-test.html');

    if (!isTargetSite) {
        console.log('[YLZ注入面板] 非目标网站，跳过初始化');
        return;
    }

    // 避免重复初始化
    if (ylzInjectedPanel) {
        console.log('[YLZ注入面板] 已有实例，重新初始化');
        // 不直接返回，而是重新初始化
    }

    try {
        // 创建面板实例
        ylzInjectedPanel = new YlzInjectedPanel();
        console.log('[YLZ注入面板] 面板实例创建成功');
    } catch (error) {
        console.error('[YLZ注入面板] 面板实例创建失败:', error);
    }
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(initYlzInjectedPanel, 1000);
    });
} else {
    // 延迟初始化，确保页面完全稳定
    setTimeout(initYlzInjectedPanel, 1000);
}

// 导出到全局作用域（用于调试）
if (typeof window !== 'undefined') {
    window.ylzInjectedPanel = ylzInjectedPanel;
}

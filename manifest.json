{"manifest_version": 3, "name": "文件云流转助手", "description": "自动将上传的文件转移到文件云流转平台", "version": "1.0", "permissions": ["storage", "activeTab", "scripting", "tabs"], "host_permissions": ["<all_urls>"], "action": {"default_popup": "popup.html", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_end"}, {"matches": ["https://yunpan.gdcourts.gov.cn/*", "https://yunpan.gdcourts.gov.cn:82/*", "https://*.gov.cn/*"], "js": ["content-script-yunpan.js"], "run_at": "document_end"}, {"matches": ["https://yunpan.gdcourts.gov.cn:82/*"], "js": ["content-script-injected-panel.js"], "run_at": "document_end"}], "background": {"service_worker": "background.js", "type": "module"}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["popup.html", "config.js", "popup.js", "styles/popup.css", "utils/file-icon-mapper.js", "icons/file-types/*.svg", "icons/*.svg", "extension_websocket_test.html", "websocket_diagnostic.html", "websocket_monitor.html", "test_yunpan_upload.html", "simple_upload_test.html", "upload_debug_tool.html", "test-injected-panel-upload.html", "debug-upload-flow.js", "test-background-upload.js", "test-background-simple.js", "content-script-yunpan.js", "injected-panel.html", "injected-panel.js", "injected-panel-tree.js", "content-script-injected-panel.js", "content-script-injected-panel-simple.js"], "matches": ["<all_urls>"]}]}
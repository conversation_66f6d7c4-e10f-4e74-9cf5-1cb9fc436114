<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件云流转助手</title>
    <link rel="stylesheet" href="styles/popup.css">
    <link rel="stylesheet" href="styles/tree-view.css">
</head>
<body>
    <!-- 顶部标题区域 -->
    <header class="header">
        <h1>文件云流转助手</h1>
        <div class="header-actions">
            <!-- 可以添加设置按钮等 -->
    </div>
    </header>
    
    <!-- 连接状态指示区域 -->
    <section class="status-section">
        <div class="connection-status">
            <div class="connection-status-dot disconnected" id="connectionStatusDot"></div>
            <span class="connection-status-text" id="connectionStatusText">连接状态检查中...</span>
            
            <!-- 云盘连接状态 -->
            <div class="yunpan-status disconnected" id="yunpan-status">云盘未连接</div>
        </div>
        
        <!-- 自动刷新设置 -->
        <div class="auto-refresh-setting" data-tooltip="定时刷新云盘页面保持登录状态">
            <label for="autoRefreshSelect" class="setting-label">自动刷新:</label>
            <select id="autoRefreshSelect" class="refresh-interval-select">
                <option value="0">关闭</option>
                <option value="60">60秒</option>
                <option value="300">5分钟</option>
                <option value="900">15分钟</option>
                <option value="1800">30分钟</option>
            </select>
        </div>
        
        <div class="status-info">
            <span id="fileCountInfo">加载中...</span>
        </div>
    </section>

    <!-- 操作按钮区域 -->
    <section class="action-buttons">
        <button id="uploadAll" class="button primary" data-tooltip="开始上传所有待上传文件">
            <span>开始上传</span>
        </button>
        <button id="openCloud" class="button secondary" data-tooltip="在新标签页打开文件云流转">
            <span>打开云盘</span>
        </button>
        <button id="refreshList" class="button icon-button" data-tooltip="刷新文件列表">
            <span>↻</span>
        </button>
        
        <!-- 自动上传开关 -->
        <div class="auto-upload-toggle" data-tooltip="开启后自动检测并上传新文件">
            <label class="toggle-switch">
                <input type="checkbox" id="autoUploadSwitch">
                <span class="toggle-slider"></span>
                <span class="toggle-label">自动上传</span>
            </label>
        </div>
    </section>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 标签页导航 -->
        <div class="tab-navigation">
            <button class="tab-button active" data-tab="pending" id="pendingTab">
                <span class="tab-text">未上传</span>
                <span class="tab-count" id="pendingCount">0</span>
            </button>
            <button class="tab-button" data-tab="uploaded" id="uploadedTab">
                <span class="tab-text">已上传</span>
                <span class="tab-count" id="uploadedCount">0</span>
            </button>
    </div>
    
        <!-- 标签页内容 -->
        <div class="tab-content">
            <!-- 未上传文件标签页 -->
            <div class="tab-panel active" id="pendingPanel">
                <div class="panel-header">
                    <h3>未上传文件</h3>
                    <div class="panel-actions">
                        <button class="action-btn" id="expandAllPending" title="展开所有目录">
                            <span>📂</span>
                        </button>
                        <button class="action-btn" id="collapseAllPending" title="折叠所有目录">
                            <span>📁</span>
                        </button>
                    </div>
                </div>
                <div class="tree-container" id="pendingTreeContainer">
                    <!-- 加载状态占位符 -->
                    <div class="loading-state">
                        <div class="loading-skeleton tree-item"></div>
                        <div class="loading-skeleton tree-item"></div>
                        <div class="loading-skeleton tree-item"></div>
                    </div>
                </div>
    </div>
    
            <!-- 已上传文件标签页 -->
            <div class="tab-panel" id="uploadedPanel">
                <div class="panel-header">
                    <h3>已上传文件</h3>
                    <div class="panel-actions">
                        <button class="action-btn" id="expandAllUploaded" title="展开所有目录">
                            <span>📂</span>
                        </button>
                        <button class="action-btn" id="collapseAllUploaded" title="折叠所有目录">
                            <span>📁</span>
                        </button>
                    </div>
                </div>
                <div class="tree-container" id="uploadedTreeContainer">
                    <!-- 加载状态占位符 -->
                    <div class="loading-state">
                        <div class="loading-skeleton tree-item"></div>
                        <div class="loading-skeleton tree-item"></div>
                        <div class="loading-skeleton tree-item"></div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 底部状态栏（可选） -->
    <footer class="footer" style="display: none;">
        <div class="footer-content">
            <span class="footer-text">就绪</span>
        </div>
    </footer>

    <!-- 隐藏的重置状态按钮容器 -->
    <div id="reset-button-container" style="display: none;">
        <button id="resetState" class="button secondary reset-btn" data-tooltip="将所有文件重置为待上传状态">
            <span>🔄</span>
            <span>重置状态</span>
        </button>
    </div>

    <!-- JavaScript文件 -->
    <script src="utils/file-icon-mapper.js"></script>
    <script type="module" src="popup.js"></script>
</body>
</html> 
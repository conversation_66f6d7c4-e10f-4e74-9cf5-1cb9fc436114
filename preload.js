const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');
const fs = require('fs').promises;
const path = require('path');

// 暴露安全的 API 给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
    // 窗口控制
    minimizeWindow: () => ipcRenderer.invoke('window:minimize'),
    restoreWindow: () => ipcRenderer.invoke('window:restore'),
    getCurrentWindowId: () => ipcRenderer.invoke('window:getCurrentId'),
    
    // 文件系统操作
    readFile: async (filePath) => {
        try {
            return await fs.readFile(filePath, 'utf8');
        } catch (error) {
            if (error.code === 'ENOENT') {
                return null;
            }
            throw error;
        }
    },
    
    writeFile: async (filePath, data) => {
        await fs.writeFile(filePath, data, 'utf8');
    },
    
    getConfigPath: () => path.join(__dirname, 'projects.json'),
    
    // IPC 通信
    onStartSelection: (callback) => {
        ipcRenderer.on('START_SELECTION', (event, ...args) => callback(...args));
        return () => ipcRenderer.removeListener('START_SELECTION', callback);
    },
    sendElementSelected: (data) => ipcRenderer.send('ELEMENT_SELECTED', data),
    sendCancelSelection: (data) => ipcRenderer.send('CANCEL_SELECTION', data),
    sendTestFailed: (data) => ipcRenderer.send('TEST_FAILED', data)
}); 
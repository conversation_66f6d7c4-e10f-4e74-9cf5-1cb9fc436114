/* 文件云流转助手 - 现代化样式系统 */

/* ========================
   CSS自定义属性 (设计变量)
   ======================== */
:root {
  /* 色彩系统 */
  --color-primary: #2563eb;
  --color-primary-hover: #1d4ed8;
  --color-primary-light: #dbeafe;
  
  --color-success: #10b981;
  --color-success-light: #d1fae5;
  --color-warning: #f59e0b;
  --color-warning-light: #fef3c7;
  --color-error: #ef4444;
  --color-error-light: #fee2e2;
  
  /* 中性色阶 */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  
  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-card: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-card-hover: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  
  /* 圆角系统 */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-full: 9999px;
  
  /* 过渡动画 */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* 字体系统 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
}

/* ========================
   基础重置和全局样式
   ======================== */
* {
  box-sizing: border-box;
}

body {
  width: 540px; /* 固定面板宽度，增加到540px确保内容充分显示 */
  min-width: 540px; /* 设置最小宽度防止缩小 */
  max-width: 540px; /* 设置最大宽度防止扩大 */
  min-height: 420px; /* 稍微增加最小高度 */
  max-height: 700px; /* 添加最大高度限制 */
  margin: 0;
  padding: 0;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: var(--font-size-sm);
  line-height: 1.5;
  color: var(--color-gray-800);
  background-color: var(--color-gray-50);
  overflow-x: hidden;
  overflow-y: auto; /* 允许垂直滚动 */
  box-sizing: border-box; /* 确保padding和border包含在width内 */
}

/* ========================
   布局容器 - 增加内边距
   ======================== */
.container {
  padding: var(--spacing-lg); /* 从md增加到lg，提供更多边距 */
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg) var(--spacing-xl); /* 增加水平和垂直内边距 */
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
  color: white;
  border-bottom: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
  margin: 0; /* 确保header不贴边 */
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  animation: headerShine 3s infinite;
}

@keyframes headerShine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.header h1 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

/* ========================
   状态指示器 - 更新布局以容纳自动刷新控制
   ======================== */
.status-section {
  display: grid;
  grid-template-columns: 1fr auto 1fr; /* 三列布局：连接状态、自动刷新控制、状态信息 */
  grid-template-areas: 
    "connection refresh status";
  gap: var(--spacing-md);
  align-items: center;
  padding: var(--spacing-md) var(--spacing-xl); /* 增加水平内边距 */
  background-color: white;
  border-bottom: 1px solid var(--color-gray-200);
  font-size: var(--font-size-xs);
  margin: 0 var(--spacing-sm); /* 添加左右边距 */
  border-radius: var(--radius-md); /* 添加圆角 */
  margin-bottom: var(--spacing-sm); /* 底部边距 */
}

.connection-status {
  grid-area: connection;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm); /* 增加间距 */
  justify-self: start; /* 左对齐 */
}

.auto-refresh-setting {
  grid-area: refresh;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  justify-self: center; /* 居中对齐 */
  font-size: var(--font-size-xs);
}

.status-info {
  grid-area: status;
  display: flex;
  justify-content: flex-end; /* 右对齐 */
  align-items: center;
}

.connection-status-dot {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
  position: relative;
}

.connection-status-dot.connected {
  background-color: var(--color-success);
  box-shadow: 0 0 0 2px var(--color-success-light);
}

.connection-status-dot.connected::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid var(--color-success);
  border-radius: var(--radius-full);
  animation: connectionPulse 2s infinite;
}

@keyframes connectionPulse {
  0% { transform: translate(-50%, -50%) scale(0.8); opacity: 1; }
  100% { transform: translate(-50%, -50%) scale(2); opacity: 0; }
}

.connection-status-dot.disconnected {
  background-color: var(--color-error);
  box-shadow: 0 0 0 2px var(--color-error-light);
  animation: disconnectedBlink 1s infinite alternate;
}

@keyframes disconnectedBlink {
  0% { opacity: 1; }
  100% { opacity: 0.5; }
}

.connection-status-text {
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
}

/* ========================
   按钮系统
   ======================== */
.action-buttons {
  display: flex;
  padding: var(--spacing-md);
  gap: var(--spacing-md);
  align-items: center;
  justify-content: flex-start;
  background-color: white;
  border-bottom: 1px solid var(--color-gray-200);
}

.button {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  position: relative;
  overflow: hidden;
  flex: 0 0 auto; /* 防止按钮被拉伸 */
  min-width: auto;
}

/* 文字按钮样式 */
.button:not(.icon-button) {
  min-width: 100px;
  flex: 0 0 auto;
}

/* 图标按钮样式 */
.button.icon-button {
  width: 40px;
  height: 40px;
  min-width: 40px;
  padding: 0;
  border-radius: var(--radius-lg);
  flex-shrink: 0;
}

.button.icon-button span {
  font-size: 18px;
  font-weight: bold;
}

.button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-full);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.button:active::before {
  width: 300px;
  height: 300px;
}

.button.primary {
  background-color: var(--color-primary);
  color: white;
}

.button.primary:hover {
  background-color: var(--color-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.button.secondary {
  background-color: var(--color-gray-500);
  color: white;
}

.button.secondary:hover {
  background-color: var(--color-gray-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.button:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* ========================
   文件容器 - 现代化网格布局
   ======================== */
#file-tree-container {
  max-height: 500px;
  overflow-y: auto;
  padding: var(--spacing-md);
  background-color: var(--color-gray-50);
}

.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
  padding: var(--spacing-sm) 0;
}

/* ========================
   文件卡片组件
   ======================== */
.file-card {
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-card);
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--color-gray-200);
  transform-origin: center;
}

.file-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(37, 99, 235, 0.05) 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.file-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-card-hover);
  border-color: var(--color-primary);
}

.file-card:hover::before {
  opacity: 1;
}

.file-card.selected {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
  transform: translateY(-1px);
}

.file-card.selected::before {
  opacity: 1;
}

/* 文件类型特定样式 */
.file-card[data-file-type="image"] {
  border-left: 4px solid #ec4899;
}

.file-card[data-file-type="video"] {
  border-left: 4px solid #8b5cf6;
}

.file-card[data-file-type="audio"] {
  border-left: 4px solid #f59e0b;
}

.file-card[data-file-type="document"] {
  border-left: 4px solid #ef4444;
}

.file-card[data-file-type="code"] {
  border-left: 4px solid #10b981;
}

.file-card[data-file-type="compressed"] {
  border-left: 4px solid #6b7280;
}

.file-card-header {
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-height: 120px;
  position: relative;
}

.file-icon-container {
  width: 48px;
  height: 48px;
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.file-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform var(--transition-fast);
}

.file-card:hover .file-icon {
  transform: scale(1.1);
}

.file-status-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  border-radius: var(--radius-full);
  border: 2px solid white;
  font-size: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.file-status-badge.uploaded {
  background-color: var(--color-success);
  color: white;
}

.file-status-badge.pending {
  background-color: var(--color-warning);
  color: white;
}

.file-status-badge.failed {
  background-color: var(--color-error);
  color: white;
}

.file-status-badge.uploading {
  background-color: var(--color-primary);
  color: white;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(0.95); }
}

.file-name {
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-800);
  font-size: var(--font-size-sm);
  line-height: 1.3;
  margin-bottom: var(--spacing-xs);
  word-break: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.file-meta {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
  text-align: center;
}

.file-size {
  font-weight: var(--font-weight-medium);
}

.file-date {
  opacity: 0.8;
}

.file-card-footer {
  padding: var(--spacing-sm) var(--spacing-md);
  border-top: 1px solid var(--color-gray-100);
  background-color: var(--color-gray-50);
  display: flex;
  gap: var(--spacing-xs);
  justify-content: center;
  transform: translateY(100%);
  transition: transform var(--transition-normal);
}

.file-card:hover .file-card-footer {
  transform: translateY(0);
}

.file-action-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-sm);
  background-color: white;
  color: var(--color-gray-600);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: 4px;
}

.file-action-btn:hover {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
  transform: translateY(-1px);
}

/* ========================
   文件夹特殊样式
   ======================== */
.folder-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
}

.folder-card .file-icon-container {
  background: radial-gradient(circle, rgba(37, 99, 235, 0.1) 0%, transparent 70%);
  border-radius: var(--radius-full);
}

/* ========================
   空状态和加载状态
   ======================== */
.empty-state {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--color-gray-500);
}

.empty-state-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-md);
  opacity: 0.5;
  font-size: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-state {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
  padding: var(--spacing-sm) 0;
}

.loading-skeleton {
  background: linear-gradient(90deg, var(--color-gray-200) 25%, var(--color-gray-100) 50%, var(--color-gray-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--radius-lg);
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* ========================
   响应式设计
   ======================== */
@media (max-width: 500px) {
  body {
    width: 100%;
    min-width: 320px;
  }
  
  .file-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: var(--spacing-sm);
  }
  
  .file-card-header {
    min-height: 100px;
    padding: var(--spacing-sm);
  }
  
  .file-icon-container {
    width: 40px;
    height: 40px;
  }
  
  /* 保持按钮水平排列，只调整间距 */
  .action-buttons {
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  /* 按钮保持水平布局，但减小最小宽度 */
  .button:not(.icon-button) {
    min-width: 80px;
    padding: var(--spacing-sm);
    font-size: var(--font-size-xs);
  }
  
  .button.icon-button {
    width: 36px;
    height: 36px;
    min-width: 36px;
  }
}

@media (max-width: 400px) {
  .file-grid {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xs);
  }
  
  .file-card-header {
    min-height: 80px;
    padding: var(--spacing-xs);
  }
  
  .file-name {
    font-size: var(--font-size-xs);
  }
}

/* ========================
   滚动条美化
   ======================== */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-gray-100);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: var(--radius-sm);
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

/* ========================
   工具提示
   ======================== */
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--color-gray-800);
  color: white;
  font-size: var(--font-size-xs);
  border-radius: var(--radius-sm);
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--transition-fast);
  z-index: 1000;
  margin-bottom: 4px;
}

.tooltip:hover::after {
  opacity: 1;
}

/* ========================
   性能优化和硬件加速
   ======================== */
.file-card,
.button,
.file-action-btn {
  will-change: transform;
}

.file-grid {
  contain: layout style paint;
}

/* ========================
   主题支持（预留）
   ======================== */
@media (prefers-color-scheme: dark) {
  /* 暗色主题样式 - 预留 */
}

/* ========================
   高对比度支持
   ======================== */
@media (prefers-contrast: high) {
  .file-card {
    border-width: 2px;
  }
  
  .file-action-btn {
    border-width: 2px;
  }
}

/* ========================================
   缩略图系统样式 - 第二阶段功能扩展
   ======================================== */

/* 文件类型分组样式 */
.file-type-section {
  margin-bottom: var(--spacing-xl);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background: linear-gradient(135deg, var(--color-gray-50) 0%, white 100%);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-sm);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.section-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(37, 99, 235, 0.02) 50%, transparent 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.section-header:hover {
  background: linear-gradient(135deg, white 0%, var(--color-gray-50) 100%);
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.section-header:hover::before {
  opacity: 1;
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-800);
}

.section-icon {
  font-size: var(--font-size-lg);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.file-count {
  color: var(--color-gray-500);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-xs);
  background: var(--color-gray-100);
  padding: 2px 6px;
  border-radius: var(--radius-full);
}

.section-toggle {
  background: none;
  border: none;
  color: var(--color-gray-500);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-toggle:hover {
  background: var(--color-gray-100);
  color: var(--color-gray-700);
}

.toggle-icon {
  font-size: var(--font-size-sm);
  transition: transform var(--transition-normal);
}

.section-toggle[data-collapsed="true"] .toggle-icon {
  transform: rotate(-90deg);
}

/* 增强的网格和列表布局 */
.file-grid.grid-layout {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  padding: var(--spacing-sm);
}

.file-grid.list-layout {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

/* 增强的文件卡片样式 */
.file-card.thumbnail-item {
  background: white;
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  overflow: hidden;
  transition: all var(--transition-normal);
  position: relative;
  box-shadow: var(--shadow-card);
}

.file-card.thumbnail-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(37, 99, 235, 0.04) 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
  z-index: 1;
}

.file-card.thumbnail-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-card-hover);
  border-color: var(--color-primary);
}

.file-card.thumbnail-item:hover::before {
  opacity: 1;
}

.thumbnail-container {
  width: 100%;
  height: 150px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-gray-100) 100%);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.thumbnail-container:hover::after {
  transform: translateX(100%);
}

.thumbnail-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-gray-500);
  text-align: center;
  z-index: 2;
}

.placeholder-icon {
  font-size: 32px;
  margin-bottom: var(--spacing-sm);
  opacity: 0.6;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.placeholder-text {
  font-size: var(--font-size-xs);
  opacity: 0.8;
  font-weight: var(--font-weight-medium);
}

/* 增强的文件行样式 */
.file-row {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background: white;
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.file-row::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 0;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
  transition: width var(--transition-normal);
}

.file-row:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
  transform: translateX(4px);
}

.file-row:hover::before {
  width: 4px;
}

.file-icon-container {
  margin-right: var(--spacing-md);
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

.file-icon {
  font-size: 24px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.file-content {
  flex: 1;
  min-width: 0;
  position: relative;
  z-index: 2;
}

.file-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
}

.file-metadata {
  display: flex;
  gap: var(--spacing-md);
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
  flex-wrap: wrap;
}

/* 增强的文件信息样式 */
.file-info {
  padding: var(--spacing-md);
  position: relative;
  z-index: 2;
}

.file-name {
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-xs);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
  transition: color var(--transition-fast);
}

.file-card:hover .file-name {
  color: var(--color-primary);
}

.file-details {
  display: flex;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
  flex-wrap: wrap;
}

/* 增强的文件操作按钮 */
.file-actions {
  display: flex;
  gap: var(--spacing-xs);
  opacity: 0;
  transition: all var(--transition-normal);
  align-items: center;
  position: relative;
  z-index: 10;
}

.file-item:hover .file-actions {
  opacity: 1;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-md);
  background: white;
  color: var(--color-gray-600);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  backdrop-filter: blur(8px);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  transition: all var(--transition-fast);
  z-index: -1;
}

.action-btn:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

.action-btn:hover::before {
  width: 40px;
  height: 40px;
  transform: translate(-50%, -50%);
}

.preview-btn:hover::before {
  background: var(--color-primary);
}

.preview-btn:hover {
  color: white;
}

.upload-btn:hover::before {
  background: var(--color-success);
}

.upload-btn:hover {
  color: white;
}

.menu-btn:hover::before {
  background: var(--color-gray-700);
}

.menu-btn:hover {
  color: white;
}

/* 增强的上传状态指示器 */
.upload-status {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: 10px;
  font-weight: var(--font-weight-semibold);
  backdrop-filter: blur(10px);
  z-index: 10;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.upload-status[data-status="pending"] {
  background: rgba(107, 114, 128, 0.9);
  color: white;
}

.upload-status[data-status="uploading"] {
  background: rgba(59, 130, 246, 0.9);
  color: white;
  animation: pulse 2s infinite;
}

.upload-status[data-status="uploaded"] {
  background: rgba(16, 185, 129, 0.9);
  color: white;
}

.upload-status[data-status="failed"] {
  background: rgba(239, 68, 68, 0.9);
  color: white;
  animation: disconnectedBlink 1s infinite alternate;
}

.status-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
  box-shadow: 0 0 4px currentColor;
}

.status-text {
  font-size: 9px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 缩略图加载状态 */
.thumbnail-loaded .thumbnail-container {
  background: transparent;
}

.thumbnail-error .thumbnail-container {
  background: linear-gradient(135deg, var(--color-error-light) 0%, #fef2f2 100%);
  border: 1px solid var(--color-error);
}

.thumbnail-error .placeholder-icon {
  color: var(--color-error);
}

/* 增强的空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px var(--spacing-xl);
  text-align: center;
  color: var(--color-gray-500);
  background: linear-gradient(135deg, var(--color-gray-50) 0%, white 100%);
  border-radius: var(--radius-xl);
  border: 2px dashed var(--color-gray-200);
  margin: var(--spacing-xl);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: var(--spacing-lg);
  opacity: 0.6;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.empty-state h3 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-lg);
  color: var(--color-gray-700);
  font-weight: var(--font-weight-semibold);
}

.empty-state p {
  margin: 0;
  font-size: var(--font-size-sm);
  opacity: 0.8;
  line-height: 1.6;
}

/* 响应式设计增强 */
@media (max-width: 500px) {
  .file-grid.grid-layout {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--spacing-md);
  }
  
  .thumbnail-container {
    height: 120px;
  }
  
  .file-row {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .file-actions {
    opacity: 1; /* 在移动设备上始终显示 */
  }
  
  .action-btn {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  
  .section-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }
}

@media (max-width: 400px) {
  .file-grid.grid-layout {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: var(--spacing-sm);
  }
  
  .file-info {
    padding: var(--spacing-sm);
  }
  
  .file-name {
    font-size: 13px;
  }
  
  .file-details {
    font-size: 11px;
  }
  
  .empty-state {
    padding: 40px var(--spacing-lg);
  }
  
  .empty-icon {
    font-size: 48px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .section-header {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border-color: #4b5563;
    color: #f9fafb;
  }
  
  .section-header:hover {
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    border-color: #60a5fa;
  }
  
  .thumbnail-container {
    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  }
  
  .file-row {
    background: #1f2937;
    border-color: #4b5563;
    color: #f9fafb;
  }
  
  .file-card.thumbnail-item {
    background: #1f2937;
    border-color: #4b5563;
  }
  
  .action-btn {
    background: #374151;
    color: #d1d5db;
  }
  
  .empty-state {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border-color: #4b5563;
    color: #d1d5db;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .file-card.thumbnail-item {
    border-width: 2px;
  }
  
  .action-btn {
    border: 2px solid currentColor;
  }
  
  .upload-status {
    border-width: 2px;
  }
}

/* 云盘上传功能样式 */
.yunpan-status {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    margin-left: var(--spacing-md);
    transition: all var(--transition-fast);
    border: 1px solid;
}

.yunpan-status.connected {
    background-color: var(--color-success-light);
    color: var(--color-success);
    border-color: var(--color-success);
}

.yunpan-status.disconnected {
    background-color: var(--color-error-light);
    color: var(--color-error);
    border-color: var(--color-error);
}

/* 上传按钮状态样式 */
#uploadAll.uploading {
    background: linear-gradient(90deg, #4CAF50, #45a049);
    position: relative;
    overflow: hidden;
}

#uploadAll.uploading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: uploadProgress 1.5s infinite;
}

@keyframes uploadProgress {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

#uploadAll:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* 消息容器样式 */
.message-container {
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 10000;
    max-width: 300px;
}

.message {
    padding: 12px 16px;
    margin-bottom: 8px;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.4;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    animation: slideIn 0.3s ease-out;
    position: relative;
    padding-right: 40px;
}

.message-info {
    background-color: #e3f2fd;
    color: #1976d2;
    border-left: 4px solid #2196f3;
}

.message-success {
    background-color: #e8f5e8;
    color: #2e7d32;
    border-left: 4px solid #4caf50;
}

.message-warning {
    background-color: #fff3e0;
    color: #f57c00;
    border-left: 4px solid #ff9800;
}

.message-error {
    background-color: #ffebee;
    color: #d32f2f;
    border-left: 4px solid #f44336;
}

.message-close {
    position: absolute;
    top: 8px;
    right: 8px;
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.message-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ========================
   自动上传开关样式
   ======================== */
.auto-upload-toggle {
  display: flex;
  align-items: center;
  margin-left: auto; /* 推向右边 */
}

.toggle-switch {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  user-select: none;
}

.toggle-switch input[type="checkbox"] {
  display: none;
}

.toggle-slider {
  position: relative;
  width: 44px;
  height: 24px;
  background-color: var(--color-gray-300);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background-color: white;
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.toggle-switch input[type="checkbox"]:checked + .toggle-slider {
  background-color: var(--color-primary);
}

.toggle-switch input[type="checkbox"]:checked + .toggle-slider::before {
  transform: translateX(20px);
}

.toggle-switch:hover .toggle-slider {
  box-shadow: var(--shadow-md);
}

.toggle-label {
  margin-left: var(--spacing-sm);
  font-size: var(--font-size-xs);
  color: var(--color-gray-700);
  user-select: none;
}

/* ========================
   自动刷新设置
   ======================== */
.auto-refresh-setting {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xs);
}

.setting-label {
  color: var(--color-gray-700);
  font-weight: var(--font-weight-medium);
  user-select: none;
  margin: 0;
  white-space: nowrap; /* 防止标签换行 */
}

.refresh-interval-select {
  background-color: white;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
  color: var(--color-gray-700);
  cursor: pointer;
  transition: all var(--transition-fast);
  outline: none;
  min-width: 80px;
}

.refresh-interval-select:hover {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 1px var(--color-primary-light);
}

.refresh-interval-select:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-light);
}

.refresh-interval-select option {
  padding: var(--spacing-xs);
  background-color: white;
  color: var(--color-gray-700);
}

/* 响应式调整 */
@media (max-width: 500px) {
  /* 状态区域响应式调整 */
  .status-section {
    grid-template-columns: 1fr; /* 单列布局 */
    grid-template-areas: 
      "connection"
      "refresh"
      "status";
    gap: var(--spacing-sm);
    text-align: center;
  }
  
  .connection-status,
  .auto-refresh-setting,
  .status-info {
    justify-self: center; /* 居中对齐 */
  }
  
  .action-buttons {
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }
}
/* 标签页导航样式 */
.tab-navigation {
  display: flex;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  margin: 0 var(--spacing-sm);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tab-button {
  flex: 1;
  padding: var(--spacing-lg) var(--spacing-xl);
  border: none;
  background: white;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  border-bottom: 3px solid transparent;
}

.tab-button:hover {
  background: var(--hover-bg, rgba(59, 130, 246, 0.1));
  color: var(--text-primary, #1f2937);
}

.tab-button.active {
  background: var(--primary-color, #3b82f6);
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.tab-icon {
  display: none;
}

.tab-text {
  font-weight: 600;
  font-size: 14px;
}

.tab-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.tab-button.active .tab-count {
  background: rgba(255, 255, 255, 0.3);
}

/* 标签页内容样式 */
.tab-content {
  min-height: 400px;
}

.tab-panel {
  display: none;
}

.tab-panel.active {
  display: block;
}

/* 面板头部样式 - 增加左右边距 */
.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg) var(--spacing-xl); /* 增加左右内边距 */
  border-bottom: 1px solid var(--border-light, #e5e7eb);
  margin: 0 var(--spacing-sm) var(--spacing-lg) var(--spacing-sm); /* 增加左右外边距，与树状容器保持一致 */
  background: white; /* 添加背景色 */
  border-radius: var(--radius-md); /* 添加圆角 */
  box-shadow: var(--shadow-sm); /* 添加轻微阴影 */
}

.panel-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
}

.panel-actions {
  display: flex;
  gap: var(--spacing-sm); /* 增加按钮间距 */
}

.panel-actions .action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: var(--radius-md); /* 使用统一的圆角变量 */
  background: var(--action-btn-bg, #f3f4f6);
  color: var(--text-secondary, #6b7280);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 14px;
}

.panel-actions .action-btn:hover {
  background: var(--action-btn-hover-bg, #e5e7eb);
  color: var(--text-primary, #1f2937);
  transform: scale(1.05);
}

/* 树状容器样式 */
.tree-container {
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid var(--border-light, #e5e7eb);
  border-radius: 12px;
  background: var(--card-bg, white);
  margin: 0 var(--spacing-sm);
  padding: var(--spacing-sm);
}

/* 树状项目基础样式 */
.tree-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-light, #f1f3f4);
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-xs);
}

.tree-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.tree-item:hover {
  background: var(--hover-bg, #f8f9fa);
  transform: translateX(2px);
}

.tree-item.selected {
  background: var(--primary-light, rgba(59, 130, 246, 0.1));
  border-left: 3px solid var(--primary-color, #3b82f6);
}

/* 树状项目缩进 - 优化边距 */
.tree-item[data-level="0"] { padding-left: var(--spacing-lg); }
.tree-item[data-level="1"] { padding-left: calc(var(--spacing-lg) + var(--spacing-xl)); }
.tree-item[data-level="2"] { padding-left: calc(var(--spacing-lg) + var(--spacing-xl) * 2); }
.tree-item[data-level="3"] { padding-left: calc(var(--spacing-lg) + var(--spacing-xl) * 3); }
.tree-item[data-level="4"] { padding-left: calc(var(--spacing-lg) + var(--spacing-xl) * 4); }
.tree-item[data-level="5"] { padding-left: calc(var(--spacing-lg) + var(--spacing-xl) * 5); }

/* 文件夹样式 */
.tree-folder {
  font-weight: 600;
  color: var(--text-primary, #1f2937);
}

.tree-folder .folder-toggle {
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  color: var(--text-secondary, #6b7280);
  transition: all 0.2s ease;
}

.tree-folder .folder-toggle:hover {
  color: var(--text-primary, #1f2937);
  transform: scale(1.1);
}

.tree-folder .folder-toggle.expanded {
  transform: rotate(90deg);
}

.tree-folder .folder-icon {
  margin-right: 8px;
  font-size: 16px;
  color: var(--folder-color, #f59e0b);
}

.tree-folder .folder-name {
  flex: 1;
  margin-right: 8px;
}

.tree-folder .folder-stats {
  font-size: 12px;
  color: var(--text-secondary, #6b7280);
  font-weight: 400;
}

/* 文件样式 */
.tree-file {
  color: var(--text-primary, #1f2937);
}

.tree-file .file-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.tree-file .file-name {
  flex: 1;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tree-file .file-size {
  font-size: 12px;
  color: var(--text-secondary, #6b7280);
  margin-right: 8px;
}

.tree-file .file-actions {
  display: flex;
  gap: var(--spacing-sm);
  opacity: 0;
  transition: opacity 0.2s ease;
  margin-left: var(--spacing-md);
}

.tree-item:hover .file-actions {
  opacity: 1;
}

.tree-file .action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-md);
  background: var(--action-btn-bg, #f3f4f6);
  color: var(--text-secondary, #6b7280);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 14px;
  box-shadow: var(--shadow-sm);
}

.tree-file .action-btn:hover {
  background: var(--action-btn-hover-bg, #e5e7eb);
  color: var(--text-primary, #1f2937);
  transform: scale(1.1);
}

.tree-file .action-btn.preview-btn:hover {
  background: var(--primary-color, #3b82f6);
  color: white;
}

.tree-file .action-btn.upload-btn:hover {
  background: var(--success-color, #10b981);
  color: white;
}

/* 上传状态指示器 */
.upload-status {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  margin-left: 8px;
}

.upload-status.pending {
  background: rgba(107, 114, 128, 0.1);
  color: var(--text-secondary, #6b7280);
  border: 1px solid rgba(107, 114, 128, 0.2);
}

.upload-status.uploading {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color, #3b82f6);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.upload-status.uploaded {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color, #10b981);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.upload-status.failed {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color, #ef4444);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

/* 缩略图容器 */
.tree-file .thumbnail-container {
  width: 40px;
  height: 30px;
  margin-right: 8px;
  border-radius: 4px;
  overflow: hidden;
  background: var(--skeleton-bg, #f3f4f6);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.tree-file .thumbnail-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.tree-file .thumbnail-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary, #6b7280);
  font-size: 10px;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: var(--text-secondary, #6b7280);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: var(--text-primary, #1f2937);
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 加载状态样式 */
.loading-state {
  padding: 16px;
}

.loading-skeleton.tree-item {
  height: 48px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 8px;
  margin-bottom: 8px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 搜索和过滤样式 */
.tree-search {
  margin-bottom: 16px;
  position: relative;
}

.tree-search input {
  width: 100%;
  padding: 12px 16px 12px 40px;
  border: 1px solid var(--border-light, #e5e7eb);
  border-radius: 8px;
  font-size: 14px;
  color: var(--text-primary, #1f2937);
  background: var(--card-bg, white);
  transition: border-color 0.2s ease;
}

.tree-search input:focus {
  outline: none;
  border-color: var(--primary-color, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.tree-search .search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary, #6b7280);
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 500px) {
  .tab-button {
    padding: 10px 12px;
    font-size: 13px;
  }
  
  .tab-text {
    font-size: 12px;
  }
  
  .panel-header {
    padding: 12px 0;
  }
  
  .panel-header h3 {
    font-size: 16px;
  }
  
  .tree-item {
    padding: 6px 8px;
  }
  
  .tree-item[data-level="1"] { padding-left: 24px; }
  .tree-item[data-level="2"] { padding-left: 40px; }
  .tree-item[data-level="3"] { padding-left: 56px; }
  .tree-item[data-level="4"] { padding-left: 72px; }
  .tree-item[data-level="5"] { padding-left: 88px; }
  
  .tree-file .file-actions {
    opacity: 1;
  }
}

/* 黑暗模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --card-bg: #1f2937;
    --text-primary: #f9fafb;
    --text-secondary: #9ca3af;
    --border-light: #374151;
    --hover-bg: #374151;
    --skeleton-bg: #374151;
    --action-btn-bg: #374151;
    --action-btn-hover-bg: #4b5563;
    --primary-light: rgba(59, 130, 246, 0.2);
  }
}

/* ========================
   上传状态指示器样式
   ======================== */
.upload-status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: 12px;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.upload-status-indicator[data-status="pending"] {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #fcd34d;
}

.upload-status-indicator[data-status="uploaded"] {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #34d399;
}

.upload-status-indicator[data-status="uploading"] {
  background-color: #dbeafe;
  color: #1e40af;
  border: 1px solid #60a5fa;
  animation: uploadingPulse 1.5s infinite;
}

@keyframes uploadingPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.status-icon {
  font-size: 14px;
  line-height: 1;
}

.status-text {
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
} 
// 简单的background测试脚本
// 在浏览器控制台中运行

console.log('=== 简单Background测试工具 ===');

// 测试1: 发送TRIGGER_AUTO_UPLOAD消息
async function testTriggerAutoUpload() {
    console.log('\n1. 测试TRIGGER_AUTO_UPLOAD消息:');

    try {
        console.log('📤 发送TRIGGER_AUTO_UPLOAD消息...');
        console.log('⚠️ 请同时打开background控制台观察日志！');
        console.log('   1. 打开 chrome://extensions/');
        console.log('   2. 找到"文件云流转助手"扩展');
        console.log('   3. 点击"检查视图 service worker"');

        const response = await chrome.runtime.sendMessage({
            type: 'TRIGGER_AUTO_UPLOAD',
            source: 'test_simple',
            timestamp: Date.now()
        });

        console.log('✅ Background响应:', response);

        if (response && response.success) {
            // 等待10秒，观察是否有后续日志
            console.log('⏳ 等待10秒观察background异步执行日志...');
            console.log('   请在background控制台中查找以下日志:');
            console.log('   - [Background] 开始执行自动上传逻辑');
            console.log('   - [Background] 获取文件树数据...');
            console.log('   - [Background] 检查云盘页面是否可用...');

            await new Promise(resolve => setTimeout(resolve, 10000));
            console.log('⏰ 等待结束，如果没有看到background日志，说明异步执行部分有问题');
        }

        return response?.success || false;
    } catch (error) {
        console.error('❌ 测试失败:', error);
        return false;
    }
}

// 测试2: 检查文件树数据
async function testFileTreeData() {
    console.log('\n2. 检查文件树数据:');
    
    try {
        const result = await chrome.storage.local.get('admin_file_tree');
        const fileTree = result.admin_file_tree;
        
        if (!fileTree) {
            console.error('❌ 未找到文件树数据');
            return false;
        }
        
        console.log('✅ 找到文件树数据');
        console.log('   数据结构:', {
            hasChildren: !!(fileTree.children && Array.isArray(fileTree.children)),
            childrenCount: fileTree.children ? fileTree.children.length : 0
        });
        
        // 统计待上传文件
        let pendingCount = 0;
        function countPendingFiles(node) {
            if (node.type === 'file' && (node.status === 'pending' || node.status === 'failed')) {
                pendingCount++;
            } else if (node.children && Array.isArray(node.children)) {
                node.children.forEach(child => countPendingFiles(child));
            }
        }
        
        if (fileTree.children && Array.isArray(fileTree.children)) {
            fileTree.children.forEach(child => countPendingFiles(child));
        }
        
        console.log(`   待上传文件数: ${pendingCount}`);
        return pendingCount > 0;
    } catch (error) {
        console.error('❌ 测试失败:', error);
        return false;
    }
}

// 测试3: 检查云盘页面
async function testYunpanPage() {
    console.log('\n3. 检查云盘页面:');
    
    try {
        const tabs = await chrome.tabs.query({});
        const yunpanTabs = tabs.filter(tab => 
            tab.url && (
                tab.url.includes('yunpan.gdcourts.gov.cn') ||
                tab.url.includes('yunpan.gdcourts.gov.cn:82')
            )
        );
        
        if (yunpanTabs.length === 0) {
            console.error('❌ 未找到云盘页面');
            return false;
        }
        
        console.log(`✅ 找到 ${yunpanTabs.length} 个云盘页面:`);
        yunpanTabs.forEach((tab, index) => {
            console.log(`   ${index + 1}. ${tab.url} (ID: ${tab.id})`);
        });
        
        // 测试与云盘页面的通信
        for (const tab of yunpanTabs) {
            try {
                const response = await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('PING超时'));
                    }, 3000);
                    
                    chrome.tabs.sendMessage(tab.id, { type: 'PING' }, (response) => {
                        clearTimeout(timeout);
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                if (response && response.success) {
                    console.log(`   ✅ 标签页 ${tab.id} 通信正常`);
                } else {
                    console.log(`   ❌ 标签页 ${tab.id} 通信异常:`, response);
                }
            } catch (error) {
                console.log(`   ❌ 标签页 ${tab.id} 通信失败:`, error.message);
            }
        }
        
        return yunpanTabs.length > 0;
    } catch (error) {
        console.error('❌ 测试失败:', error);
        return false;
    }
}

// 测试4: 检查background service worker状态
async function testBackgroundStatus() {
    console.log('\n4. 检查background service worker状态:');
    
    try {
        // 尝试获取连接状态
        const statusResponse = await chrome.runtime.sendMessage({
            type: 'GET_CONNECTION_STATUS'
        });
        
        console.log('✅ Background状态响应:', statusResponse);
        
        // 检查是否有WebSocket连接
        if (statusResponse && statusResponse.connected !== undefined) {
            console.log(`   WebSocket连接: ${statusResponse.connected ? '已连接' : '未连接'}`);
            console.log(`   连接状态: ${statusResponse.readyState}`);
        }
        
        return true;
    } catch (error) {
        console.error('❌ Background状态检查失败:', error);
        return false;
    }
}

// 主测试函数
async function runSimpleTest() {
    console.log('🔍 开始简单Background测试...\n');
    
    const results = {
        backgroundStatus: await testBackgroundStatus(),
        fileTreeData: await testFileTreeData(),
        yunpanPage: await testYunpanPage(),
        triggerAutoUpload: await testTriggerAutoUpload()
    };
    
    console.log('\n=== 测试结果汇总 ===');
    Object.entries(results).forEach(([key, value]) => {
        console.log(`${value ? '✅' : '❌'} ${key}: ${value ? '通过' : '失败'}`);
    });
    
    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
        console.log('\n🎉 所有测试都通过了！');
    } else {
        console.log('\n⚠️ 发现问题，请检查失败的测试项。');
    }
    
    console.log('\n📋 调试建议:');
    console.log('1. 打开Chrome扩展管理页面 (chrome://extensions/)');
    console.log('2. 找到"文件云流转助手"扩展');
    console.log('3. 点击"检查视图 service worker"查看background日志');
    console.log('4. 重新运行测试，观察background控制台输出');
    
    return results;
}

// 导出测试函数
window.testBackgroundSimple = {
    testTriggerAutoUpload,
    testFileTreeData,
    testYunpanPage,
    testBackgroundStatus,
    runSimpleTest
};

console.log('\n使用方法:');
console.log('- 运行完整测试: testBackgroundSimple.runSimpleTest()');
console.log('- 单独测试上传触发: testBackgroundSimple.testTriggerAutoUpload()');
console.log('- 检查文件数据: testBackgroundSimple.testFileTreeData()');
console.log('- 检查云盘页面: testBackgroundSimple.testYunpanPage()');
console.log('- 检查background状态: testBackgroundSimple.testBackgroundStatus()');

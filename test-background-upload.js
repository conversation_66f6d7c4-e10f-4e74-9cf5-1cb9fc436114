// 测试background.js上传功能的脚本
// 在浏览器控制台中运行此脚本

console.log('=== Background上传功能测试工具 ===');

// 测试函数：检查云盘页面是否加载了content script
async function testYunpanContentScript() {
    console.log('\n1. 测试云盘页面content script:');
    
    try {
        const tabs = await chrome.tabs.query({});
        const yunpanTabs = tabs.filter(tab => 
            tab.url && (
                tab.url.includes('yunpan.gdcourts.gov.cn') ||
                tab.url.includes('yunpan.gdcourts.gov.cn:82')
            )
        );
        
        if (yunpanTabs.length === 0) {
            console.error('❌ 未找到云盘页面标签');
            return false;
        }
        
        console.log(`✅ 找到 ${yunpanTabs.length} 个云盘页面:`);
        
        for (const tab of yunpanTabs) {
            console.log(`   测试标签页: ${tab.url} (ID: ${tab.id})`);
            
            try {
                // 测试PING
                const pingResponse = await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('PING超时'));
                    }, 3000);
                    
                    chrome.tabs.sendMessage(tab.id, { type: 'PING' }, (response) => {
                        clearTimeout(timeout);
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                if (pingResponse && pingResponse.success) {
                    console.log(`   ✅ 标签页 ${tab.id} content script响应正常`);
                } else {
                    console.log(`   ❌ 标签页 ${tab.id} content script响应异常:`, pingResponse);
                }
            } catch (error) {
                console.log(`   ❌ 标签页 ${tab.id} 通信失败:`, error.message);
            }
        }
        
        return yunpanTabs.length > 0;
    } catch (error) {
        console.error('❌ 测试失败:', error);
        return false;
    }
}

// 测试函数：检查文件树数据
async function testFileTreeData() {
    console.log('\n2. 测试文件树数据:');
    
    try {
        const result = await chrome.storage.local.get('admin_file_tree');
        const fileTree = result.admin_file_tree;
        
        if (!fileTree) {
            console.error('❌ 未找到文件树数据');
            return false;
        }
        
        console.log('✅ 找到文件树数据');
        
        // 统计文件
        let totalFiles = 0;
        let pendingFiles = 0;
        
        function countFiles(node) {
            if (node.type === 'file') {
                totalFiles++;
                if (node.status === 'pending' || node.status === 'failed') {
                    pendingFiles++;
                }
            } else if (node.children && Array.isArray(node.children)) {
                node.children.forEach(child => countFiles(child));
            }
        }
        
        if (fileTree.children && Array.isArray(fileTree.children)) {
            fileTree.children.forEach(child => countFiles(child));
        }
        
        console.log(`   总文件数: ${totalFiles}`);
        console.log(`   待上传文件数: ${pendingFiles}`);
        
        return pendingFiles > 0;
    } catch (error) {
        console.error('❌ 测试失败:', error);
        return false;
    }
}

// 测试函数：触发background上传
async function testBackgroundUpload() {
    console.log('\n3. 测试background上传触发:');
    
    try {
        console.log('   发送TRIGGER_AUTO_UPLOAD消息...');
        
        const response = await chrome.runtime.sendMessage({
            type: 'TRIGGER_AUTO_UPLOAD',
            source: 'test_script',
            timestamp: Date.now()
        });
        
        console.log('   Background响应:', response);
        
        if (response && response.success) {
            console.log('✅ Background上传触发成功');
            
            // 等待一段时间，然后检查上传状态
            console.log('   等待5秒后检查上传状态...');
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // 检查云盘页面是否有上传活动
            const tabs = await chrome.tabs.query({});
            const yunpanTab = tabs.find(tab => 
                tab.url && (
                    tab.url.includes('yunpan.gdcourts.gov.cn') ||
                    tab.url.includes('yunpan.gdcourts.gov.cn:82')
                )
            );
            
            if (yunpanTab) {
                try {
                    const statusResponse = await new Promise((resolve, reject) => {
                        const timeout = setTimeout(() => {
                            reject(new Error('状态查询超时'));
                        }, 3000);
                        
                        chrome.tabs.sendMessage(yunpanTab.id, {
                            type: 'GET_UPLOAD_STATUS'
                        }, (response) => {
                            clearTimeout(timeout);
                            if (chrome.runtime.lastError) {
                                reject(new Error(chrome.runtime.lastError.message));
                            } else {
                                resolve(response);
                            }
                        });
                    });
                    
                    console.log('   上传状态:', statusResponse);
                } catch (statusError) {
                    console.log('   ❌ 无法获取上传状态:', statusError.message);
                }
            }
            
            return true;
        } else {
            console.error('❌ Background上传触发失败:', response);
            return false;
        }
    } catch (error) {
        console.error('❌ 测试失败:', error);
        return false;
    }
}

// 测试函数：直接测试executeYunpanUpload
async function testDirectUpload() {
    console.log('\n4. 测试直接上传通信:');
    
    try {
        const tabs = await chrome.tabs.query({});
        const yunpanTab = tabs.find(tab => 
            tab.url && (
                tab.url.includes('yunpan.gdcourts.gov.cn') ||
                tab.url.includes('yunpan.gdcourts.gov.cn:82')
            )
        );
        
        if (!yunpanTab) {
            console.error('❌ 未找到云盘页面');
            return false;
        }
        
        console.log(`   找到云盘页面: ${yunpanTab.url}`);
        
        // 创建测试文件数据
        const testFiles = [{
            name: 'test-file.txt',
            fileName: 'test-file.txt',
            path: 'test-file.txt',
            size: 100,
            type: 'file',
            status: 'pending'
        }];
        
        console.log('   发送START_UPLOAD消息...');
        
        const response = await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('上传请求超时'));
            }, 10000);
            
            chrome.tabs.sendMessage(yunpanTab.id, {
                type: 'START_UPLOAD',
                data: {
                    files: testFiles,
                    source: 'test_direct_upload'
                }
            }, (response) => {
                clearTimeout(timeout);
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
        
        console.log('   上传响应:', response);
        
        if (response && response.success) {
            console.log('✅ 直接上传通信成功');
            return true;
        } else {
            console.error('❌ 直接上传通信失败:', response);
            return false;
        }
    } catch (error) {
        console.error('❌ 测试失败:', error);
        return false;
    }
}

// 主测试函数
async function runBackgroundUploadTest() {
    console.log('🔍 开始Background上传功能测试...\n');
    
    const results = {
        contentScript: await testYunpanContentScript(),
        fileTreeData: await testFileTreeData(),
        backgroundUpload: await testBackgroundUpload(),
        directUpload: await testDirectUpload()
    };
    
    console.log('\n=== 测试结果汇总 ===');
    Object.entries(results).forEach(([key, value]) => {
        console.log(`${value ? '✅' : '❌'} ${key}: ${value ? '通过' : '失败'}`);
    });
    
    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
        console.log('\n🎉 所有测试都通过了！Background上传功能应该可以正常工作。');
    } else {
        console.log('\n⚠️ 发现问题，请根据上述错误信息进行修复。');
        
        // 提供修复建议
        if (!results.contentScript) {
            console.log('\n💡 修复建议：');
            console.log('1. 确保云盘页面已打开');
            console.log('2. 重新加载扩展');
            console.log('3. 检查manifest.json中的content_scripts配置');
        }
    }
    
    return results;
}

// 导出测试函数
window.testBackgroundUpload = {
    testYunpanContentScript,
    testFileTreeData,
    testBackgroundUpload,
    testDirectUpload,
    runBackgroundUploadTest
};

console.log('\n使用方法:');
console.log('- 运行完整测试: testBackgroundUpload.runBackgroundUploadTest()');
console.log('- 测试content script: testBackgroundUpload.testYunpanContentScript()');
console.log('- 测试文件数据: testBackgroundUpload.testFileTreeData()');
console.log('- 测试background上传: testBackgroundUpload.testBackgroundUpload()');
console.log('- 测试直接上传: testBackgroundUpload.testDirectUpload()');

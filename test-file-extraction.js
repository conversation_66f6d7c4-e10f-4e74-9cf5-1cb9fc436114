// 测试文件提取逻辑
// 在浏览器控制台中运行此脚本

console.log('=== 文件提取逻辑测试工具 ===');

// 测试函数：直接获取并分析文件树数据
async function testFileExtraction() {
    console.log('\n1. 获取文件树数据...');
    
    try {
        // 获取存储的文件树数据
        const result = await chrome.storage.local.get('admin_file_tree');
        const fileTree = result.admin_file_tree;
        
        if (!fileTree) {
            console.error('❌ 未找到文件树数据');
            return false;
        }
        
        console.log('✅ 找到文件树数据');
        console.log('📊 文件树结构概览:', {
            hasFiles: !!(fileTree.files && Array.isArray(fileTree.files)),
            filesCount: fileTree.files ? fileTree.files.length : 0,
            hasFolders: !!(fileTree.folders && Array.isArray(fileTree.folders)),
            foldersCount: fileTree.folders ? fileTree.folders.length : 0
        });
        
        // 详细分析文件状态
        console.log('\n2. 分析文件状态...');
        
        const fileAnalysis = {
            totalFiles: 0,
            statusCounts: {},
            pendingFiles: [],
            uploadedFiles: []
        };
        
        // 递归分析文件
        function analyzeFiles(node, currentPath = '') {
            if (!node) return;
            
            // 处理文件
            if (!node.isFolder && node.filename) {
                fileAnalysis.totalFiles++;
                
                const syncStatus = node.syncStatus || 'unknown';
                fileAnalysis.statusCounts[syncStatus] = (fileAnalysis.statusCounts[syncStatus] || 0) + 1;
                
                // 使用与注入面板相同的逻辑判断状态
                const uploadedStatuses = ['已同步', '已上传', 'uploaded'];
                const isUploaded = uploadedStatuses.includes(syncStatus);
                
                const fileInfo = {
                    name: node.filename,
                    path: node.path || (currentPath ? `${currentPath}/${node.filename}` : node.filename),
                    syncStatus: syncStatus,
                    isUploaded: isUploaded,
                    size: node.size,
                    type: node.type
                };
                
                if (isUploaded) {
                    fileAnalysis.uploadedFiles.push(fileInfo);
                } else {
                    fileAnalysis.pendingFiles.push(fileInfo);
                }
                
                console.log(`📄 文件: ${node.filename}`);
                console.log(`   状态: ${syncStatus}`);
                console.log(`   已上传: ${isUploaded}`);
                console.log(`   路径: ${fileInfo.path}`);
            }
            
            // 处理子节点
            if (node.children) {
                if (node.children.files && Array.isArray(node.children.files)) {
                    node.children.files.forEach(file => {
                        const childPath = currentPath ? `${currentPath}/${node.name || node.filename}` : (node.name || node.filename || '');
                        analyzeFiles(file, childPath);
                    });
                }
                if (node.children.folders && Array.isArray(node.children.folders)) {
                    node.children.folders.forEach(folder => {
                        const childPath = currentPath ? `${currentPath}/${node.name || node.filename}` : (node.name || node.filename || '');
                        analyzeFiles(folder, childPath);
                    });
                }
            }
        }
        
        // 分析根级别文件和文件夹
        if (fileTree.files && Array.isArray(fileTree.files)) {
            fileTree.files.forEach(file => analyzeFiles(file));
        }
        
        if (fileTree.folders && Array.isArray(fileTree.folders)) {
            fileTree.folders.forEach(folder => analyzeFiles(folder));
        }
        
        // 输出分析结果
        console.log('\n📊 文件分析结果:');
        console.log(`   总文件数: ${fileAnalysis.totalFiles}`);
        console.log(`   待上传文件数: ${fileAnalysis.pendingFiles.length}`);
        console.log(`   已上传文件数: ${fileAnalysis.uploadedFiles.length}`);
        console.log('\n📈 状态统计:');
        Object.entries(fileAnalysis.statusCounts).forEach(([status, count]) => {
            console.log(`   ${status}: ${count} 个文件`);
        });
        
        if (fileAnalysis.pendingFiles.length > 0) {
            console.log('\n📋 待上传文件列表:');
            fileAnalysis.pendingFiles.forEach((file, index) => {
                console.log(`   ${index + 1}. ${file.name} (${file.syncStatus})`);
            });
        }
        
        return {
            success: true,
            totalFiles: fileAnalysis.totalFiles,
            pendingFiles: fileAnalysis.pendingFiles,
            uploadedFiles: fileAnalysis.uploadedFiles,
            statusCounts: fileAnalysis.statusCounts
        };
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
        return { success: false, error: error.message };
    }
}

// 测试函数：模拟background的文件提取逻辑
async function testBackgroundExtraction() {
    console.log('\n3. 测试background文件提取逻辑...');
    
    try {
        const result = await chrome.storage.local.get('admin_file_tree');
        const fileTree = result.admin_file_tree;
        
        if (!fileTree) {
            console.error('❌ 未找到文件树数据');
            return false;
        }
        
        // 模拟background的extractUnuploadedFilesFromTree函数
        const unuploadedFiles = [];
        let totalFiles = 0;
        let pendingFiles = 0;
        let failedFiles = 0;
        
        function traverseTree(node, currentPath = '') {
            if (!node) return;
            
            console.log(`🔍 遍历节点: ${node.name || node.filename || 'unnamed'}, isFolder: ${node.isFolder}, syncStatus: ${node.syncStatus}`);
            
            // 处理文件
            if (!node.isFolder && node.filename) {
                totalFiles++;
                
                // 使用与注入面板相同的状态判断逻辑
                const syncStatus = node.syncStatus;
                const uploadedStatuses = ['已同步', '已上传', 'uploaded'];
                const isUploaded = uploadedStatuses.includes(syncStatus);
                const isPending = !isUploaded;
                
                console.log(`📄 文件: ${node.filename}, 状态: ${syncStatus}, 是否待上传: ${isPending}`);
                
                if (isPending) {
                    if (syncStatus === 'failed' || syncStatus === '上传失败') {
                        failedFiles++;
                    } else {
                        pendingFiles++;
                    }
                    
                    const fileInfo = {
                        name: node.filename || node.name,
                        fileName: node.filename || node.name,
                        path: node.path || (currentPath ? `${currentPath}/${node.filename}` : node.filename),
                        size: node.size || 0,
                        type: node.type || 'unknown',
                        status: syncStatus || 'pending',
                        originalPath: node.originalPath || node.path,
                        deviceId: node.deviceId,
                        portalId: node.portalId,
                        portalName: node.portalName
                    };
                    
                    console.log(`✅ 添加待上传文件:`, fileInfo);
                    unuploadedFiles.push(fileInfo);
                }
            }
            
            // 处理子节点
            if (node.children) {
                if (node.children.files && Array.isArray(node.children.files)) {
                    console.log(`📁 遍历文件夹 ${node.name || node.filename} 中的文件: ${node.children.files.length} 个`);
                    node.children.files.forEach(file => {
                        const childPath = currentPath ? `${currentPath}/${node.name || node.filename}` : (node.name || node.filename || '');
                        traverseTree(file, childPath);
                    });
                }
                if (node.children.folders && Array.isArray(node.children.folders)) {
                    console.log(`📁 遍历文件夹 ${node.name || node.filename} 中的子文件夹: ${node.children.folders.length} 个`);
                    node.children.folders.forEach(folder => {
                        const childPath = currentPath ? `${currentPath}/${node.name || node.filename}` : (node.name || node.filename || '');
                        traverseTree(folder, childPath);
                    });
                }
            }
        }
        
        // 开始遍历
        console.log('🚀 开始遍历文件树...');
        
        if (fileTree.files && Array.isArray(fileTree.files)) {
            console.log(`📂 处理根级别文件: ${fileTree.files.length} 个`);
            fileTree.files.forEach(file => traverseTree(file));
        }
        
        if (fileTree.folders && Array.isArray(fileTree.folders)) {
            console.log(`📂 处理根级别文件夹: ${fileTree.folders.length} 个`);
            fileTree.folders.forEach(folder => traverseTree(folder));
        }
        
        console.log(`\n📊 Background提取结果:`);
        console.log(`   总文件数: ${totalFiles}`);
        console.log(`   待上传: ${pendingFiles}`);
        console.log(`   失败重试: ${failedFiles}`);
        console.log(`   最终提取: ${unuploadedFiles.length}`);
        
        return {
            success: true,
            totalFiles,
            pendingFiles,
            failedFiles,
            extractedFiles: unuploadedFiles
        };
        
    } catch (error) {
        console.error('❌ Background提取测试失败:', error);
        return { success: false, error: error.message };
    }
}

// 主测试函数
async function runFileExtractionTest() {
    console.log('🔍 开始文件提取逻辑测试...\n');
    
    const results = {
        fileAnalysis: await testFileExtraction(),
        backgroundExtraction: await testBackgroundExtraction()
    };
    
    console.log('\n=== 测试结果汇总 ===');
    
    if (results.fileAnalysis.success) {
        console.log(`✅ 文件分析: 找到 ${results.fileAnalysis.pendingFiles.length} 个待上传文件`);
    } else {
        console.log(`❌ 文件分析失败: ${results.fileAnalysis.error}`);
    }
    
    if (results.backgroundExtraction.success) {
        console.log(`✅ Background提取: 提取到 ${results.backgroundExtraction.extractedFiles.length} 个待上传文件`);
    } else {
        console.log(`❌ Background提取失败: ${results.backgroundExtraction.error}`);
    }
    
    // 比较结果
    if (results.fileAnalysis.success && results.backgroundExtraction.success) {
        const panelCount = results.fileAnalysis.pendingFiles.length;
        const backgroundCount = results.backgroundExtraction.extractedFiles.length;
        
        if (panelCount === backgroundCount) {
            console.log(`🎉 结果一致: 两种方法都找到 ${panelCount} 个待上传文件`);
        } else {
            console.log(`⚠️ 结果不一致: 面板找到 ${panelCount} 个，background找到 ${backgroundCount} 个`);
        }
    }
    
    return results;
}

// 导出测试函数
window.testFileExtraction = {
    testFileExtraction,
    testBackgroundExtraction,
    runFileExtractionTest
};

console.log('\n使用方法:');
console.log('- 运行完整测试: testFileExtraction.runFileExtractionTest()');
console.log('- 单独测试文件分析: testFileExtraction.testFileExtraction()');
console.log('- 单独测试background提取: testFileExtraction.testBackgroundExtraction()');

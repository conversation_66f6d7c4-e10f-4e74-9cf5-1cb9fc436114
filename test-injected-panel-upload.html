<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注入面板上传功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>注入面板上传功能测试</h1>
    
    <div class="test-section">
        <h2>1. 检查扩展状态</h2>
        <button class="test-button" onclick="checkExtensionStatus()">检查扩展状态</button>
        <div id="extensionStatus" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>2. 检查云盘页面</h2>
        <button class="test-button" onclick="checkYunpanPage()">检查云盘页面</button>
        <div id="yunpanStatus" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>3. 测试消息传递</h2>
        <button class="test-button" onclick="testMessagePassing()">测试消息传递</button>
        <div id="messageStatus" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>4. 测试文件树数据</h2>
        <button class="test-button" onclick="checkFileTreeData()">检查文件树数据</button>
        <div id="fileTreeStatus" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>5. 模拟上传触发</h2>
        <button class="test-button" onclick="simulateUploadTrigger()">模拟上传触发</button>
        <div id="uploadStatus" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>6. 直接测试云盘通信</h2>
        <button class="test-button" onclick="testDirectYunpanCommunication()">直接测试云盘通信</button>
        <div id="directCommStatus" class="test-result"></div>
    </div>

    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `test-result ${type}`;
        }

        async function checkExtensionStatus() {
            try {
                showResult('extensionStatus', '检查中...', 'info');
                
                if (!chrome || !chrome.runtime) {
                    throw new Error('Chrome扩展API不可用');
                }
                
                const response = await chrome.runtime.sendMessage({
                    type: 'GET_CONNECTION_STATUS'
                });
                
                showResult('extensionStatus', 
                    `扩展状态: ${JSON.stringify(response, null, 2)}`, 
                    response?.success ? 'success' : 'error'
                );
            } catch (error) {
                showResult('extensionStatus', `错误: ${error.message}`, 'error');
            }
        }

        async function checkYunpanPage() {
            try {
                showResult('yunpanStatus', '检查中...', 'info');
                
                const tabs = await chrome.tabs.query({});
                const yunpanTabs = tabs.filter(tab => 
                    tab.url && (
                        tab.url.includes('yunpan.gdcourts.gov.cn') ||
                        tab.url.includes('yunpan.gdcourts.gov.cn:82')
                    )
                );
                
                if (yunpanTabs.length === 0) {
                    showResult('yunpanStatus', '未找到云盘页面标签', 'error');
                } else {
                    const tabInfo = yunpanTabs.map(tab => ({
                        id: tab.id,
                        url: tab.url,
                        title: tab.title
                    }));
                    showResult('yunpanStatus', 
                        `找到 ${yunpanTabs.length} 个云盘页面:\n${JSON.stringify(tabInfo, null, 2)}`, 
                        'success'
                    );
                }
            } catch (error) {
                showResult('yunpanStatus', `错误: ${error.message}`, 'error');
            }
        }

        async function testMessagePassing() {
            try {
                showResult('messageStatus', '测试中...', 'info');
                
                const tabs = await chrome.tabs.query({});
                const yunpanTab = tabs.find(tab => 
                    tab.url && (
                        tab.url.includes('yunpan.gdcourts.gov.cn') ||
                        tab.url.includes('yunpan.gdcourts.gov.cn:82')
                    )
                );
                
                if (!yunpanTab) {
                    throw new Error('未找到云盘页面');
                }
                
                const response = await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('PING超时'));
                    }, 5000);
                    
                    chrome.tabs.sendMessage(yunpanTab.id, {
                        type: 'PING'
                    }, (response) => {
                        clearTimeout(timeout);
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                showResult('messageStatus', 
                    `PING响应: ${JSON.stringify(response, null, 2)}`, 
                    response?.success ? 'success' : 'error'
                );
            } catch (error) {
                showResult('messageStatus', `错误: ${error.message}`, 'error');
            }
        }

        async function checkFileTreeData() {
            try {
                showResult('fileTreeStatus', '检查中...', 'info');
                
                const result = await chrome.storage.local.get('admin_file_tree');
                const fileTree = result.admin_file_tree;
                
                if (!fileTree) {
                    showResult('fileTreeStatus', '未找到文件树数据', 'error');
                } else {
                    const summary = {
                        hasData: !!fileTree,
                        hasChildren: !!(fileTree.children && Array.isArray(fileTree.children)),
                        childrenCount: fileTree.children ? fileTree.children.length : 0
                    };
                    showResult('fileTreeStatus', 
                        `文件树数据摘要:\n${JSON.stringify(summary, null, 2)}`, 
                        'success'
                    );
                }
            } catch (error) {
                showResult('fileTreeStatus', `错误: ${error.message}`, 'error');
            }
        }

        async function simulateUploadTrigger() {
            try {
                showResult('uploadStatus', '触发中...', 'info');
                
                const response = await chrome.runtime.sendMessage({
                    type: 'TRIGGER_AUTO_UPLOAD',
                    source: 'test_page',
                    timestamp: Date.now()
                });
                
                showResult('uploadStatus', 
                    `上传触发响应: ${JSON.stringify(response, null, 2)}`, 
                    response?.success ? 'success' : 'error'
                );
            } catch (error) {
                showResult('uploadStatus', `错误: ${error.message}`, 'error');
            }
        }

        async function testDirectYunpanCommunication() {
            try {
                showResult('directCommStatus', '测试中...', 'info');
                
                const tabs = await chrome.tabs.query({});
                const yunpanTab = tabs.find(tab => 
                    tab.url && (
                        tab.url.includes('yunpan.gdcourts.gov.cn') ||
                        tab.url.includes('yunpan.gdcourts.gov.cn:82')
                    )
                );
                
                if (!yunpanTab) {
                    throw new Error('未找到云盘页面');
                }
                
                // 模拟文件数据
                const mockFiles = [{
                    name: 'test.txt',
                    fileName: 'test.txt',
                    path: 'test.txt',
                    size: 100,
                    type: 'file',
                    status: 'pending'
                }];
                
                const response = await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('响应超时'));
                    }, 10000);
                    
                    chrome.tabs.sendMessage(yunpanTab.id, {
                        type: 'START_UPLOAD',
                        data: {
                            files: mockFiles,
                            source: 'test_direct_communication'
                        }
                    }, (response) => {
                        clearTimeout(timeout);
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                showResult('directCommStatus', 
                    `直接通信响应: ${JSON.stringify(response, null, 2)}`, 
                    response?.success ? 'success' : 'error'
                );
            } catch (error) {
                showResult('directCommStatus', `错误: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>

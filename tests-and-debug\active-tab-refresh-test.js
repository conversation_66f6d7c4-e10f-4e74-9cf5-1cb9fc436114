// 活跃标签页自动刷新测试脚本

console.log('🔄 开始活跃标签页自动刷新测试...');

// 1. 检查当前配置
async function checkRefreshConfig() {
    console.log('\n=== 检查刷新配置 ===');
    
    try {
        const settings = await chrome.storage.local.get([
            'allowRefreshActiveTab',
            'autoPageRefreshEnabled',
            'autoPageRefreshInterval'
        ]);
        
        console.log('📊 当前配置:');
        console.log(`  允许刷新活跃标签页: ${settings.allowRefreshActiveTab === true ? '✅ 是' : '❌ 否'}`);
        console.log(`  自动刷新启用: ${settings.autoPageRefreshEnabled === true ? '✅ 是' : '❌ 否'}`);
        console.log(`  刷新间隔: ${settings.autoPageRefreshInterval || '未设置'}秒`);
        
        return settings;
    } catch (error) {
        console.error('❌ 获取配置失败:', error);
        return null;
    }
}

// 2. 启用活跃标签页刷新
async function enableActiveTabRefresh() {
    console.log('\n=== 启用活跃标签页刷新 ===');
    
    try {
        await chrome.storage.local.set({ allowRefreshActiveTab: true });
        console.log('✅ 已启用活跃标签页刷新');
        return true;
    } catch (error) {
        console.error('❌ 启用失败:', error);
        return false;
    }
}

// 3. 禁用活跃标签页刷新
async function disableActiveTabRefresh() {
    console.log('\n=== 禁用活跃标签页刷新 ===');
    
    try {
        await chrome.storage.local.set({ allowRefreshActiveTab: false });
        console.log('✅ 已禁用活跃标签页刷新');
        return true;
    } catch (error) {
        console.error('❌ 禁用失败:', error);
        return false;
    }
}

// 4. 设置短间隔自动刷新
function setShortIntervalRefresh() {
    console.log('\n=== 设置30秒自动刷新 ===');
    
    return new Promise((resolve) => {
        chrome.runtime.sendMessage({ 
            type: 'SET_AUTO_REFRESH', 
            interval: 30 
        }, (response) => {
            if (response && response.success) {
                console.log('✅ 设置成功:', response.message);
                resolve(true);
            } else {
                console.log('❌ 设置失败:', response);
                resolve(false);
            }
        });
    });
}

// 5. 检查自动刷新状态
function checkAutoRefreshStatus() {
    console.log('\n=== 检查自动刷新状态 ===');
    
    return new Promise((resolve) => {
        chrome.runtime.sendMessage({ type: 'GET_AUTO_REFRESH_STATUS' }, (response) => {
            if (response && response.success) {
                console.log('📊 自动刷新状态:');
                console.log(`  启用: ${response.enabled ? '✅' : '❌'}`);
                console.log(`  间隔: ${response.interval}秒`);
                console.log(`  目标标签页: ${response.targetTabId || 'null'}`);
                console.log(`  上次刷新: ${response.lastRefreshTime ? new Date(response.lastRefreshTime).toLocaleTimeString() : '从未'}`);
                resolve(response);
            } else {
                console.log('❌ 获取状态失败');
                resolve(null);
            }
        });
    });
}

// 6. 监听刷新相关消息
function startRefreshMonitoring() {
    console.log('\n=== 开始监听刷新消息 ===');
    
    const listener = (message, sender, sendResponse) => {
        if (message.type === 'PAGE_REFRESHED') {
            console.log('📡 收到页面刷新通知:');
            console.log(`   时间: ${new Date(message.timestamp).toLocaleTimeString()}`);
            console.log(`   间隔: ${message.interval}秒`);
        } else if (message.type === 'REFRESH_SKIPPED') {
            console.log('⚠️ 收到刷新跳过通知:');
            console.log(`   原因: ${message.reason}`);
            console.log(`   消息: ${message.message}`);
        }
    };
    
    chrome.runtime.onMessage.addListener(listener);
    console.log('👂 监听器已启动');
    
    // 60秒后停止监听
    setTimeout(() => {
        chrome.runtime.onMessage.removeListener(listener);
        console.log('🔇 监听器已停止');
    }, 60000);
}

// 7. 测试禁用活跃标签页刷新的情况
async function testDisabledActiveRefresh() {
    console.log('\n🧪 === 测试禁用活跃标签页刷新 ===');
    
    // 1. 禁用活跃标签页刷新
    await disableActiveTabRefresh();
    
    // 2. 设置30秒刷新
    await setShortIntervalRefresh();
    
    // 3. 开始监听
    startRefreshMonitoring();
    
    console.log('⏰ 请等待30秒，应该会收到"刷新跳过"通知...');
    
    // 35秒后检查状态
    setTimeout(async () => {
        console.log('\n📊 35秒后状态检查:');
        await checkAutoRefreshStatus();
    }, 35000);
}

// 8. 测试启用活跃标签页刷新的情况
async function testEnabledActiveRefresh() {
    console.log('\n🧪 === 测试启用活跃标签页刷新 ===');
    
    // 1. 启用活跃标签页刷新
    await enableActiveTabRefresh();
    
    // 2. 设置30秒刷新
    await setShortIntervalRefresh();
    
    // 3. 开始监听
    startRefreshMonitoring();
    
    console.log('⏰ 请等待30秒，页面应该会自动刷新...');
    console.log('⚠️ 注意：页面刷新后此脚本会重新加载');
    
    // 35秒后检查状态（如果页面没有刷新）
    setTimeout(async () => {
        console.log('\n📊 35秒后状态检查（如果页面未刷新）:');
        await checkAutoRefreshStatus();
    }, 35000);
}

// 9. 完整测试流程
async function fullActiveTabRefreshTest() {
    console.log('🚀 开始完整的活跃标签页刷新测试...\n');
    
    try {
        // 1. 检查当前配置
        const currentConfig = await checkRefreshConfig();
        
        // 2. 检查自动刷新状态
        const refreshStatus = await checkAutoRefreshStatus();
        
        // 3. 询问用户想要测试哪种情况
        console.log('\n❓ 选择测试模式:');
        console.log('💡 运行以下命令之一:');
        console.log('  - testDisabledActiveRefresh() - 测试禁用活跃标签页刷新（会跳过刷新）');
        console.log('  - testEnabledActiveRefresh() - 测试启用活跃标签页刷新（会刷新页面）');
        
        // 4. 默认测试禁用情况（更安全）
        console.log('\n🔧 默认测试禁用活跃标签页刷新的情况...');
        setTimeout(() => {
            testDisabledActiveRefresh();
        }, 3000);
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
    }
}

// 10. 恢复默认设置
async function restoreDefaultSettings() {
    console.log('\n🔄 === 恢复默认设置 ===');
    
    try {
        // 禁用活跃标签页刷新（默认行为）
        await chrome.storage.local.set({ allowRefreshActiveTab: false });
        
        // 设置回5分钟刷新
        chrome.runtime.sendMessage({ 
            type: 'SET_AUTO_REFRESH', 
            interval: 300 
        }, (response) => {
            if (response && response.success) {
                console.log('✅ 已恢复默认设置（5分钟刷新，不刷新活跃标签页）');
            }
        });
        
    } catch (error) {
        console.error('❌ 恢复默认设置失败:', error);
    }
}

// 导出函数
window.checkRefreshConfig = checkRefreshConfig;
window.enableActiveTabRefresh = enableActiveTabRefresh;
window.disableActiveTabRefresh = disableActiveTabRefresh;
window.setShortIntervalRefresh = setShortIntervalRefresh;
window.checkAutoRefreshStatus = checkAutoRefreshStatus;
window.startRefreshMonitoring = startRefreshMonitoring;
window.testDisabledActiveRefresh = testDisabledActiveRefresh;
window.testEnabledActiveRefresh = testEnabledActiveRefresh;
window.fullActiveTabRefreshTest = fullActiveTabRefreshTest;
window.restoreDefaultSettings = restoreDefaultSettings;

console.log('✅ 活跃标签页刷新测试脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - fullActiveTabRefreshTest() - 完整测试');
console.log('  - testDisabledActiveRefresh() - 测试禁用活跃标签页刷新');
console.log('  - testEnabledActiveRefresh() - 测试启用活跃标签页刷新（会刷新页面！）');
console.log('  - restoreDefaultSettings() - 恢复默认设置');

// 自动运行完整测试
fullActiveTabRefreshTest();

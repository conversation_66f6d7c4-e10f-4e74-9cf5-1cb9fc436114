// 自动刷新功能调试脚本

console.log('🔧 开始自动刷新功能调试...');

// 1. 检查自动刷新状态
function debugAutoRefreshStatus() {
    console.log('\n=== 调试自动刷新状态 ===');
    
    return new Promise((resolve, reject) => {
        chrome.runtime.sendMessage({ type: 'GET_AUTO_REFRESH_STATUS' }, (response) => {
            if (chrome.runtime.lastError) {
                console.error('❌ 获取状态失败:', chrome.runtime.lastError.message);
                reject(chrome.runtime.lastError);
            } else {
                console.log('📊 后台状态:', response);
                
                if (response && response.success) {
                    console.log(`  启用: ${response.enabled ? '✅' : '❌'}`);
                    console.log(`  间隔: ${response.interval}秒`);
                    console.log(`  目标标签页: ${response.targetTabId || 'null'}`);
                    console.log(`  上次刷新: ${response.lastRefreshTime ? new Date(response.lastRefreshTime).toLocaleTimeString() : '从未'}`);
                }
                
                resolve(response);
            }
        });
    });
}

// 2. 强制设置自动刷新（绕过面板）
function forceSetAutoRefresh(interval) {
    console.log(`\n=== 强制设置自动刷新为 ${interval}秒 ===`);
    
    return new Promise((resolve, reject) => {
        chrome.runtime.sendMessage({ 
            type: 'SET_AUTO_REFRESH', 
            interval: interval 
        }, (response) => {
            if (chrome.runtime.lastError) {
                console.error('❌ 设置失败:', chrome.runtime.lastError.message);
                reject(chrome.runtime.lastError);
            } else {
                console.log('📡 设置响应:', response);
                
                if (response && response.success) {
                    console.log(`✅ 设置成功: ${response.message}`);
                    console.log(`  启用: ${response.enabled}`);
                    console.log(`  间隔: ${response.interval}秒`);
                } else {
                    console.log('❌ 设置失败');
                }
                
                resolve(response);
            }
        });
    });
}

// 3. 检查云盘标签页
async function checkYunpanTabs() {
    console.log('\n=== 检查云盘标签页 ===');
    
    try {
        const tabs = await chrome.tabs.query({
            url: ['*://yunpan.gdcourts.gov.cn:82/*']
        });
        
        console.log(`📋 找到 ${tabs.length} 个云盘标签页:`);
        
        tabs.forEach((tab, index) => {
            console.log(`  ${index + 1}. ID: ${tab.id}`);
            console.log(`     URL: ${tab.url}`);
            console.log(`     标题: ${tab.title}`);
            console.log(`     活跃: ${tab.active ? '✅' : '❌'}`);
            console.log(`     状态: ${tab.status}`);
        });
        
        return tabs;
    } catch (error) {
        console.error('❌ 查询标签页失败:', error);
        return [];
    }
}

// 4. 手动触发页面刷新测试
async function manualRefreshTest() {
    console.log('\n=== 手动触发页面刷新测试 ===');
    
    try {
        const tabs = await checkYunpanTabs();
        
        if (tabs.length === 0) {
            console.log('❌ 没有云盘标签页可以测试');
            return;
        }
        
        // 选择一个非活跃的标签页进行测试
        let targetTab = tabs.find(tab => !tab.active);
        if (!targetTab) {
            console.log('⚠️ 所有云盘标签页都是活跃的，选择第一个进行测试');
            targetTab = tabs[0];
        }
        
        console.log(`🎯 选择标签页进行测试: ${targetTab.id}`);
        console.log(`   URL: ${targetTab.url}`);
        console.log(`   活跃: ${targetTab.active ? '是' : '否'}`);
        
        // 执行刷新
        console.log('🔄 开始刷新...');
        await chrome.tabs.reload(targetTab.id);
        console.log('✅ 刷新完成');
        
        // 等待一下再检查状态
        setTimeout(async () => {
            const updatedTab = await chrome.tabs.get(targetTab.id);
            console.log('📊 刷新后状态:');
            console.log(`   状态: ${updatedTab.status}`);
            console.log(`   URL: ${updatedTab.url}`);
        }, 2000);
        
    } catch (error) {
        console.error('❌ 手动刷新测试失败:', error);
    }
}

// 5. 监听后台日志（如果可能）
function monitorBackgroundLogs() {
    console.log('\n=== 监听后台活动 ===');
    
    // 监听来自background的消息
    const messageListener = (message, sender, sendResponse) => {
        if (message.type === 'PAGE_REFRESHED') {
            console.log('📡 收到页面刷新通知:');
            console.log(`   时间: ${new Date(message.timestamp).toLocaleTimeString()}`);
            console.log(`   间隔: ${message.interval}秒`);
        }
    };
    
    chrome.runtime.onMessage.addListener(messageListener);
    
    console.log('👂 开始监听后台消息...');
    
    // 30秒后停止监听
    setTimeout(() => {
        chrome.runtime.onMessage.removeListener(messageListener);
        console.log('🔇 停止监听后台消息');
    }, 30000);
}

// 6. 检查存储中的设置
async function checkStorageSettings() {
    console.log('\n=== 检查存储设置 ===');
    
    try {
        const result = await chrome.storage.local.get([
            'autoPageRefreshEnabled',
            'autoPageRefreshInterval'
        ]);
        
        console.log('💾 存储中的设置:');
        console.log(`   启用: ${result.autoPageRefreshEnabled}`);
        console.log(`   间隔: ${result.autoPageRefreshInterval}秒`);
        
        return result;
    } catch (error) {
        console.error('❌ 读取存储失败:', error);
        return null;
    }
}

// 7. 完整的调试流程
async function fullAutoRefreshDebug() {
    console.log('🚀 开始完整的自动刷新调试...\n');
    
    try {
        // 1. 检查当前状态
        console.log('1️⃣ 检查当前状态...');
        const currentStatus = await debugAutoRefreshStatus();
        
        // 2. 检查存储设置
        console.log('\n2️⃣ 检查存储设置...');
        const storageSettings = await checkStorageSettings();
        
        // 3. 检查云盘标签页
        console.log('\n3️⃣ 检查云盘标签页...');
        const tabs = await checkYunpanTabs();
        
        // 4. 如果自动刷新未启用，强制启用
        if (!currentStatus?.enabled) {
            console.log('\n4️⃣ 自动刷新未启用，强制启用...');
            await forceSetAutoRefresh(60); // 设置为1分钟间隔便于测试
            
            // 重新检查状态
            setTimeout(async () => {
                console.log('\n📊 重新检查状态...');
                await debugAutoRefreshStatus();
            }, 2000);
        } else {
            console.log('\n4️⃣ 自动刷新已启用，跳过设置');
        }
        
        // 5. 开始监听
        console.log('\n5️⃣ 开始监听后台活动...');
        monitorBackgroundLogs();
        
        // 6. 手动测试刷新
        if (tabs.length > 0) {
            console.log('\n6️⃣ 执行手动刷新测试...');
            setTimeout(() => {
                manualRefreshTest();
            }, 3000);
        }
        
        // 7. 生成调试报告
        setTimeout(() => {
            console.log('\n📋 === 自动刷新调试报告 ===');
            console.log(`✅ 后台API: ${currentStatus ? '正常' : '异常'}`);
            console.log(`✅ 存储设置: ${storageSettings ? '正常' : '异常'}`);
            console.log(`✅ 云盘标签页: ${tabs.length > 0 ? '存在' : '不存在'}`);
            console.log(`${currentStatus?.enabled ? '✅' : '❌'} 功能启用: ${currentStatus?.enabled ? '是' : '否'}`);
            
            if (currentStatus?.enabled) {
                console.log('\n🎉 自动刷新功能已启用！');
                console.log(`⏰ 下次刷新预计在 ${currentStatus.interval}秒 后执行`);
                console.log('💡 如果是活跃标签页，刷新会被跳过');
            } else {
                console.log('\n⚠️ 自动刷新功能未正常启用');
                console.log('🔧 请检查设置或重新加载扩展');
            }
        }, 10000);
        
    } catch (error) {
        console.error('❌ 调试过程中出现错误:', error);
    }
}

// 8. 快速测试函数
async function quickRefreshTest() {
    console.log('⚡ 快速自动刷新测试...\n');
    
    // 设置30秒间隔
    await forceSetAutoRefresh(30);
    
    // 检查状态
    setTimeout(async () => {
        await debugAutoRefreshStatus();
        console.log('⏱️ 请等待30秒观察是否有自动刷新...');
    }, 2000);
}

// 导出函数
window.fullAutoRefreshDebug = fullAutoRefreshDebug;
window.debugAutoRefreshStatus = debugAutoRefreshStatus;
window.forceSetAutoRefresh = forceSetAutoRefresh;
window.checkYunpanTabs = checkYunpanTabs;
window.manualRefreshTest = manualRefreshTest;
window.monitorBackgroundLogs = monitorBackgroundLogs;
window.checkStorageSettings = checkStorageSettings;
window.quickRefreshTest = quickRefreshTest;

console.log('✅ 自动刷新调试脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - fullAutoRefreshDebug() - 完整调试');
console.log('  - quickRefreshTest() - 快速测试（30秒间隔）');
console.log('  - forceSetAutoRefresh(60) - 强制设置1分钟间隔');
console.log('  - manualRefreshTest() - 手动刷新测试');

// 自动运行完整调试
fullAutoRefreshDebug();

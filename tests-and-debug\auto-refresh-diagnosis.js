// 自动刷新功能诊断脚本

console.log('🔍 开始自动刷新功能诊断...');

// 1. 检查扩展是否正常加载
function checkExtensionLoaded() {
    console.log('\n=== 检查扩展加载状态 ===');
    
    const panel = document.getElementById('ylz-injected-panel');
    const panelInstance = window.ylzInjectedPanel;
    
    console.log('📊 扩展状态:');
    console.log(`  面板元素存在: ${panel ? '✅' : '❌'}`);
    console.log(`  面板实例存在: ${panelInstance ? '✅' : '❌'}`);
    
    if (panel) {
        const autoRefreshSelect = panel.querySelector('#ylz-autoRefreshSelect');
        const allowActiveCheckbox = panel.querySelector('#ylz-allowActiveRefresh');
        
        console.log(`  自动刷新选择器: ${autoRefreshSelect ? '✅' : '❌'}`);
        console.log(`  刷新当前页面勾选框: ${allowActiveCheckbox ? '✅' : '❌'}`);
        
        if (autoRefreshSelect) {
            console.log(`  当前刷新间隔: ${autoRefreshSelect.value}秒`);
        }
        
        if (allowActiveCheckbox) {
            console.log(`  允许刷新当前页面: ${allowActiveCheckbox.checked ? '✅' : '❌'}`);
        }
    }
    
    return {
        panelExists: !!panel,
        instanceExists: !!panelInstance,
        selectExists: !!panel?.querySelector('#ylz-autoRefreshSelect'),
        checkboxExists: !!panel?.querySelector('#ylz-allowActiveRefresh')
    };
}

// 2. 检查后台自动刷新状态
function checkBackgroundStatus() {
    console.log('\n=== 检查后台自动刷新状态 ===');
    
    return new Promise((resolve) => {
        chrome.runtime.sendMessage({ type: 'GET_AUTO_REFRESH_STATUS' }, (response) => {
            if (response && response.success !== undefined) {
                console.log('📡 后台状态:');
                console.log(`  功能启用: ${response.enabled ? '✅' : '❌'}`);
                console.log(`  刷新间隔: ${response.interval}秒`);
                console.log(`  目标标签页: ${response.targetTabId || 'null'}`);
                console.log(`  上次刷新: ${response.lastRefreshTime ? new Date(response.lastRefreshTime).toLocaleTimeString() : '从未'}`);
                
                if (response.lastRefreshTime > 0) {
                    const timeSince = (Date.now() - response.lastRefreshTime) / 1000;
                    console.log(`  距离上次刷新: ${timeSince.toFixed(1)}秒前`);
                }
                
                resolve(response);
            } else {
                console.log('❌ 获取后台状态失败');
                resolve(null);
            }
        });
    });
}

// 3. 检查活跃标签页设置
function checkActiveTabSetting() {
    console.log('\n=== 检查活跃标签页设置 ===');
    
    return new Promise((resolve) => {
        chrome.runtime.sendMessage({ type: 'GET_ALLOW_ACTIVE_REFRESH' }, (response) => {
            if (response && response.success !== undefined) {
                console.log('⚙️ 活跃标签页设置:');
                console.log(`  允许刷新当前页面: ${response.allowed ? '✅ 是' : '❌ 否'}`);
                resolve(response);
            } else {
                console.log('❌ 获取活跃标签页设置失败');
                resolve(null);
            }
        });
    });
}

// 4. 检查当前标签页状态
function checkCurrentTabStatus() {
    console.log('\n=== 检查当前标签页状态 ===');
    
    return new Promise((resolve) => {
        chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
            if (tabs.length > 0) {
                const currentTab = tabs[0];
                console.log('📋 当前标签页:');
                console.log(`  ID: ${currentTab.id}`);
                console.log(`  URL: ${currentTab.url}`);
                console.log(`  活跃状态: ${currentTab.active ? '✅ 活跃' : '❌ 非活跃'}`);
                console.log(`  状态: ${currentTab.status}`);
                resolve(currentTab);
            } else {
                console.log('❌ 无法获取当前标签页信息');
                resolve(null);
            }
        });
    });
}

// 5. 设置短间隔测试
function setupShortIntervalTest() {
    console.log('\n=== 设置30秒刷新测试 ===');
    
    return new Promise((resolve) => {
        // 1. 先启用活跃标签页刷新
        chrome.runtime.sendMessage({ 
            type: 'SET_ALLOW_ACTIVE_REFRESH', 
            allowed: true 
        }, (response1) => {
            if (response1?.success) {
                console.log('✅ 已启用刷新当前页面');
                
                // 2. 设置30秒刷新
                chrome.runtime.sendMessage({ 
                    type: 'SET_AUTO_REFRESH', 
                    interval: 30 
                }, (response2) => {
                    if (response2?.success) {
                        console.log('✅ 已设置30秒自动刷新');
                        console.log('⏰ 请等待30秒，页面应该会自动刷新...');
                        
                        // 更新UI
                        const autoRefreshSelect = document.querySelector('#ylz-autoRefreshSelect');
                        const allowActiveCheckbox = document.querySelector('#ylz-allowActiveRefresh');
                        
                        if (autoRefreshSelect) {
                            autoRefreshSelect.value = '30';
                        }
                        
                        if (allowActiveCheckbox) {
                            allowActiveCheckbox.checked = true;
                        }
                        
                        resolve(true);
                    } else {
                        console.log('❌ 设置自动刷新失败');
                        resolve(false);
                    }
                });
            } else {
                console.log('❌ 启用刷新当前页面失败');
                resolve(false);
            }
        });
    });
}

// 6. 监听刷新事件
function startRefreshMonitoring() {
    console.log('\n=== 开始监听刷新事件 ===');
    
    const listener = (message, sender, sendResponse) => {
        if (message.type === 'PAGE_REFRESHED') {
            console.log('🔄 收到页面刷新通知:');
            console.log(`   时间: ${new Date(message.timestamp).toLocaleTimeString()}`);
            console.log(`   间隔: ${message.interval}秒`);
        } else if (message.type === 'REFRESH_SKIPPED') {
            console.log('⚠️ 收到刷新跳过通知:');
            console.log(`   原因: ${message.reason}`);
            console.log(`   消息: ${message.message}`);
        }
    };
    
    chrome.runtime.onMessage.addListener(listener);
    console.log('👂 刷新事件监听器已启动');
    
    // 60秒后停止监听
    setTimeout(() => {
        chrome.runtime.onMessage.removeListener(listener);
        console.log('🔇 刷新事件监听器已停止');
    }, 60000);
}

// 7. 页面刷新检测
function setupPageRefreshDetection() {
    console.log('\n=== 设置页面刷新检测 ===');
    
    // 记录当前时间到sessionStorage
    const currentTime = Date.now();
    const lastTime = sessionStorage.getItem('lastPageLoadTime');
    
    if (lastTime) {
        const timeDiff = (currentTime - parseInt(lastTime)) / 1000;
        console.log(`📊 页面刷新检测:`);
        console.log(`   距离上次加载: ${timeDiff.toFixed(1)}秒`);
        
        if (timeDiff < 60) {
            console.log('🔄 检测到页面可能被自动刷新了！');
        }
    }
    
    sessionStorage.setItem('lastPageLoadTime', currentTime.toString());
    
    // 设置页面卸载监听
    window.addEventListener('beforeunload', () => {
        console.log('📤 页面即将刷新/关闭');
        sessionStorage.setItem('pageUnloadTime', Date.now().toString());
    });
}

// 8. 完整诊断流程
async function fullDiagnosis() {
    console.log('🚀 开始完整自动刷新功能诊断...\n');
    
    try {
        // 1. 检查扩展加载
        const extensionStatus = checkExtensionLoaded();
        
        if (!extensionStatus.panelExists) {
            console.log('\n❌ 扩展面板未加载，请检查扩展是否正确安装和启用');
            return;
        }
        
        // 2. 检查后台状态
        const backgroundStatus = await checkBackgroundStatus();
        
        // 3. 检查活跃标签页设置
        const activeTabSetting = await checkActiveTabSetting();
        
        // 4. 检查当前标签页
        const currentTab = await checkCurrentTabStatus();
        
        // 5. 设置页面刷新检测
        setupPageRefreshDetection();
        
        // 6. 开始监听刷新事件
        startRefreshMonitoring();
        
        // 7. 生成诊断报告
        console.log('\n📋 === 诊断报告 ===');
        console.log(`✅ 扩展加载: ${extensionStatus.panelExists ? '正常' : '异常'}`);
        console.log(`✅ 后台通信: ${backgroundStatus ? '正常' : '异常'}`);
        console.log(`✅ 设置获取: ${activeTabSetting ? '正常' : '异常'}`);
        console.log(`✅ 标签页信息: ${currentTab ? '正常' : '异常'}`);
        
        if (backgroundStatus) {
            console.log(`📊 当前配置:`);
            console.log(`   自动刷新: ${backgroundStatus.enabled ? '启用' : '禁用'}`);
            console.log(`   刷新间隔: ${backgroundStatus.interval}秒`);
            console.log(`   允许刷新当前页面: ${activeTabSetting?.allowed ? '是' : '否'}`);
            
            if (backgroundStatus.enabled && activeTabSetting?.allowed) {
                console.log('\n✅ 配置正确，应该会自动刷新当前页面');
            } else if (backgroundStatus.enabled && !activeTabSetting?.allowed) {
                console.log('\n⚠️ 自动刷新已启用，但不会刷新当前页面（安全模式）');
            } else {
                console.log('\n❌ 自动刷新未启用');
            }
        }
        
        // 8. 询问是否进行测试
        console.log('\n❓ 是否要进行30秒刷新测试？');
        console.log('💡 运行 setupShortIntervalTest() 开始测试');
        
    } catch (error) {
        console.error('❌ 诊断过程中出现错误:', error);
    }
}

// 9. 恢复默认设置
function restoreDefaultSettings() {
    console.log('\n🔄 恢复默认设置...');
    
    // 设置5分钟刷新，禁用活跃标签页刷新
    chrome.runtime.sendMessage({ 
        type: 'SET_ALLOW_ACTIVE_REFRESH', 
        allowed: false 
    }, () => {
        chrome.runtime.sendMessage({ 
            type: 'SET_AUTO_REFRESH', 
            interval: 300 
        }, (response) => {
            if (response?.success) {
                console.log('✅ 已恢复默认设置（5分钟刷新，不刷新当前页面）');
                
                // 更新UI
                const autoRefreshSelect = document.querySelector('#ylz-autoRefreshSelect');
                const allowActiveCheckbox = document.querySelector('#ylz-allowActiveRefresh');
                
                if (autoRefreshSelect) {
                    autoRefreshSelect.value = '300';
                }
                
                if (allowActiveCheckbox) {
                    allowActiveCheckbox.checked = false;
                }
            }
        });
    });
}

// 导出函数
window.checkExtensionLoaded = checkExtensionLoaded;
window.checkBackgroundStatus = checkBackgroundStatus;
window.checkActiveTabSetting = checkActiveTabSetting;
window.checkCurrentTabStatus = checkCurrentTabStatus;
window.setupShortIntervalTest = setupShortIntervalTest;
window.startRefreshMonitoring = startRefreshMonitoring;
window.setupPageRefreshDetection = setupPageRefreshDetection;
window.fullDiagnosis = fullDiagnosis;
window.restoreDefaultSettings = restoreDefaultSettings;

console.log('✅ 自动刷新诊断脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - fullDiagnosis() - 完整诊断');
console.log('  - setupShortIntervalTest() - 30秒刷新测试');
console.log('  - restoreDefaultSettings() - 恢复默认设置');

// 自动运行完整诊断
fullDiagnosis();

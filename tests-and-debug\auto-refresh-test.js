// 自动刷新网页功能测试脚本

console.log('🔄 开始自动刷新网页功能测试...');

// 1. 检查当前自动刷新状态
function checkAutoRefreshStatus() {
    console.log('\n=== 检查当前自动刷新状态 ===');
    
    return new Promise((resolve, reject) => {
        if (chrome && chrome.runtime) {
            chrome.runtime.sendMessage({ type: 'GET_AUTO_REFRESH_STATUS' }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error('❌ 获取自动刷新状态失败:', chrome.runtime.lastError.message);
                    reject(chrome.runtime.lastError);
                } else {
                    console.log('✅ 自动刷新状态获取成功');
                    console.log('📊 状态详情:', response);
                    
                    if (response && response.success) {
                        console.log(`  启用状态: ${response.enabled ? '✅ 已启用' : '❌ 已禁用'}`);
                        console.log(`  刷新间隔: ${response.interval}秒 (${Math.floor(response.interval / 60)}分钟)`);
                        console.log(`  目标标签页: ${response.targetTabId || '未指定'}`);
                        
                        if (response.lastRefreshTime > 0) {
                            const lastRefresh = new Date(response.lastRefreshTime);
                            console.log(`  上次刷新: ${lastRefresh.toLocaleTimeString()}`);
                        } else {
                            console.log(`  上次刷新: 尚未刷新`);
                        }
                        
                        resolve(response);
                    } else {
                        console.log('❌ 响应格式无效');
                        reject(new Error('Invalid response format'));
                    }
                }
            });
        } else {
            reject(new Error('Chrome API不可用'));
        }
    });
}

// 2. 检查面板中的自动刷新选择器状态
function checkPanelRefreshSelector() {
    console.log('\n=== 检查面板刷新选择器状态 ===');
    
    const panel = document.getElementById('ylz-injected-panel');
    if (!panel) {
        console.log('❌ 面板不存在');
        return null;
    }
    
    const autoRefreshSelect = panel.querySelector('#ylz-autoRefreshSelect');
    if (autoRefreshSelect) {
        console.log('✅ 自动刷新选择器存在');
        console.log(`  当前选中值: ${autoRefreshSelect.value}秒`);
        console.log(`  是否禁用: ${autoRefreshSelect.disabled ? '是' : '否'}`);
        
        // 显示所有选项
        const options = Array.from(autoRefreshSelect.options);
        console.log('  可用选项:');
        options.forEach(option => {
            const isSelected = option.selected ? ' (当前选中)' : '';
            const label = option.value === '0' ? '关闭' : 
                         option.value === '60' ? '1分钟' :
                         option.value === '300' ? '5分钟' :
                         option.value === '900' ? '15分钟' :
                         option.value === '1800' ? '30分钟' : 
                         `${option.value}秒`;
            console.log(`    ${option.value}: ${label}${isSelected}`);
        });
        
        return {
            exists: true,
            value: parseInt(autoRefreshSelect.value),
            disabled: autoRefreshSelect.disabled,
            options: options.map(opt => ({ value: opt.value, text: opt.text }))
        };
    } else {
        console.log('❌ 自动刷新选择器不存在');
        return { exists: false };
    }
}

// 3. 测试自动刷新设置
function testAutoRefreshSetting(interval) {
    console.log(`\n=== 测试设置自动刷新为 ${interval}秒 ===`);
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return Promise.reject(new Error('Panel instance not found'));
    }
    
    const panel = window.ylzInjectedPanel;
    
    return new Promise((resolve) => {
        console.log(`🧪 设置自动刷新间隔为 ${interval}秒...`);
        panel.setAutoRefresh(interval)
            .then(() => {
                console.log('✅ 设置自动刷新成功');
                
                // 检查设置后的状态
                setTimeout(() => {
                    checkAutoRefreshStatus()
                        .then(status => {
                            console.log(`  后台状态确认: ${status.enabled ? '✅ 已启用' : '❌ 未启用'}`);
                            console.log(`  间隔确认: ${status.interval}秒`);
                            
                            const success = status.enabled === (interval > 0) && 
                                          (interval === 0 || status.interval === interval);
                            console.log(`  设置${success ? '成功' : '失败'}: ${success ? '✅' : '❌'}`);
                            
                            resolve({ success, status });
                        })
                        .catch(err => {
                            console.error('❌ 获取设置后状态失败:', err);
                            resolve({ success: false, error: err });
                        });
                }, 1000);
            })
            .catch(error => {
                console.error('❌ 设置自动刷新失败:', error);
                resolve({ success: false, error });
            });
    });
}

// 4. 测试不同的刷新间隔
async function testDifferentIntervals() {
    console.log('\n=== 测试不同的刷新间隔 ===');
    
    const intervals = [
        { value: 0, name: '关闭' },
        { value: 60, name: '1分钟' },
        { value: 300, name: '5分钟' },
        { value: 900, name: '15分钟' }
    ];
    
    for (const interval of intervals) {
        console.log(`\n🔄 测试 ${interval.name} (${interval.value}秒)...`);
        
        try {
            const result = await testAutoRefreshSetting(interval.value);
            
            if (result.success) {
                console.log(`✅ ${interval.name} 设置成功`);
            } else {
                console.log(`❌ ${interval.name} 设置失败:`, result.error?.message || '未知错误');
            }
            
            // 等待一下再测试下一个
            await new Promise(resolve => setTimeout(resolve, 2000));
            
        } catch (error) {
            console.error(`❌ 测试 ${interval.name} 时出错:`, error);
        }
    }
}

// 5. 监控自动刷新执行
function monitorAutoRefresh(duration = 60000) {
    console.log(`\n=== 监控自动刷新执行 (${duration / 1000}秒) ===`);
    
    let refreshCount = 0;
    const startTime = Date.now();
    
    // 监听页面刷新事件
    const originalReload = location.reload;
    location.reload = function(...args) {
        refreshCount++;
        console.log(`🔄 检测到页面刷新 #${refreshCount} - ${new Date().toLocaleTimeString()}`);
        return originalReload.apply(this, args);
    };
    
    // 监听来自background的刷新通知
    const messageListener = (message) => {
        if (message.type === 'PAGE_REFRESHED') {
            refreshCount++;
            console.log(`📡 收到刷新通知 #${refreshCount} - ${new Date().toLocaleTimeString()}`);
            console.log(`   间隔: ${message.interval}秒`);
        }
    };
    
    chrome.runtime.onMessage.addListener(messageListener);
    
    // 设置监控超时
    setTimeout(() => {
        location.reload = originalReload; // 恢复原始方法
        chrome.runtime.onMessage.removeListener(messageListener);
        
        const endTime = Date.now();
        const actualDuration = (endTime - startTime) / 1000;
        
        console.log(`\n📊 监控结果 (${actualDuration.toFixed(1)}秒):`);
        console.log(`  检测到刷新次数: ${refreshCount}`);
        console.log(`  平均刷新间隔: ${refreshCount > 0 ? (actualDuration / refreshCount).toFixed(1) : 'N/A'}秒`);
        
        if (refreshCount === 0) {
            console.log('⚠️ 未检测到自动刷新，可能间隔时间较长或功能未启用');
        } else {
            console.log('✅ 自动刷新功能正常工作');
        }
    }, duration);
    
    console.log(`⏱️ 开始监控，将持续 ${duration / 1000}秒...`);
}

// 6. 完整的自动刷新功能测试
async function autoRefreshTest() {
    console.log('🚀 开始完整的自动刷新功能测试...\n');
    
    try {
        // 1. 检查当前状态
        const currentStatus = await checkAutoRefreshStatus();
        const panelSelector = checkPanelRefreshSelector();
        
        // 2. 验证状态一致性
        console.log('\n=== 状态一致性检查 ===');
        const backendEnabled = currentStatus?.enabled === true;
        const panelValue = panelSelector?.value || 0;
        const backendInterval = currentStatus?.interval || 0;
        
        console.log(`  后端启用状态: ${backendEnabled ? '✅' : '❌'}`);
        console.log(`  后端刷新间隔: ${backendInterval}秒`);
        console.log(`  面板选中值: ${panelValue}秒`);
        
        const isConsistent = (backendEnabled && backendInterval === panelValue) || 
                           (!backendEnabled && panelValue === 0);
        console.log(`  状态一致性: ${isConsistent ? '✅ 一致' : '❌ 不一致'}`);
        
        // 3. 测试不同间隔设置
        await testDifferentIntervals();
        
        // 4. 设置为默认的5分钟间隔
        console.log('\n🔄 设置为默认的5分钟间隔...');
        await testAutoRefreshSetting(300);
        
        // 5. 生成最终报告
        console.log('\n📋 === 自动刷新功能测试报告 ===');
        console.log(`✅ 后端API: ${currentStatus ? '正常' : '异常'}`);
        console.log(`✅ 面板选择器: ${panelSelector?.exists ? '存在' : '缺失'}`);
        console.log(`${isConsistent ? '✅' : '❌'} 状态同步: ${isConsistent ? '正常' : '异常'}`);
        console.log(`✅ 设置功能: 正常`);
        
        console.log('\n🎉 自动刷新功能测试完成！');
        console.log('📝 功能说明:');
        console.log('  - 自动刷新会定期刷新云盘网页，保持页面状态最新');
        console.log('  - 默认设置为5分钟间隔，用户可以自定义');
        console.log('  - 不会刷新用户正在使用的活跃标签页');
        console.log('  - 面板数据通过WebSocket保持实时同步，无需刷新');
        
        // 可选：开始短期监控
        console.log('\n❓ 是否要开始60秒的刷新监控？');
        console.log('💡 运行 monitorAutoRefresh(60000) 来监控自动刷新执行');
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
    }
}

// 导出函数
window.autoRefreshTest = autoRefreshTest;
window.checkAutoRefreshStatus = checkAutoRefreshStatus;
window.checkPanelRefreshSelector = checkPanelRefreshSelector;
window.testAutoRefreshSetting = testAutoRefreshSetting;
window.testDifferentIntervals = testDifferentIntervals;
window.monitorAutoRefresh = monitorAutoRefresh;

console.log('✅ 自动刷新功能测试脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - autoRefreshTest() - 运行完整测试');
console.log('  - checkAutoRefreshStatus() - 检查当前状态');
console.log('  - testAutoRefreshSetting(300) - 测试设置5分钟间隔');
console.log('  - monitorAutoRefresh(60000) - 监控60秒的刷新执行');

// 自动运行测试
autoRefreshTest();

// 自动上传默认设置测试脚本

console.log('🚀 开始自动上传默认设置测试...');

// 1. 检查当前自动上传状态
function checkCurrentAutoUploadStatus() {
    console.log('\n=== 检查当前自动上传状态 ===');
    
    return new Promise((resolve, reject) => {
        if (chrome && chrome.runtime) {
            chrome.runtime.sendMessage({ type: 'GET_AUTO_UPLOAD_STATUS' }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error('❌ 获取自动上传状态失败:', chrome.runtime.lastError.message);
                    reject(chrome.runtime.lastError);
                } else {
                    console.log('✅ 自动上传状态获取成功');
                    console.log('📊 状态详情:', response);
                    
                    if (response && response.success) {
                        console.log(`  启用状态: ${response.enabled ? '✅ 已启用' : '❌ 已禁用'}`);
                        console.log(`  检测间隔: ${response.intervalDuration}ms`);
                        console.log(`  运行状态: ${response.isRunning ? '运行中' : '空闲'}`);
                        
                        resolve(response);
                    } else {
                        console.log('❌ 响应格式无效');
                        reject(new Error('Invalid response format'));
                    }
                }
            });
        } else {
            reject(new Error('Chrome API不可用'));
        }
    });
}

// 2. 检查面板中的自动上传开关状态
function checkPanelSwitchStatus() {
    console.log('\n=== 检查面板开关状态 ===');
    
    const panel = document.getElementById('ylz-injected-panel');
    if (!panel) {
        console.log('❌ 面板不存在');
        return null;
    }
    
    const autoUploadSwitch = panel.querySelector('#ylz-autoUploadSwitch');
    if (autoUploadSwitch) {
        console.log('✅ 自动上传开关存在');
        console.log(`  开关状态: ${autoUploadSwitch.checked ? '✅ 开启' : '❌ 关闭'}`);
        console.log(`  是否禁用: ${autoUploadSwitch.disabled ? '是' : '否'}`);
        
        return {
            exists: true,
            checked: autoUploadSwitch.checked,
            disabled: autoUploadSwitch.disabled
        };
    } else {
        console.log('❌ 自动上传开关不存在');
        return { exists: false };
    }
}

// 3. 测试自动上传开关切换
function testAutoUploadToggle() {
    console.log('\n=== 测试自动上传开关切换 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return Promise.reject(new Error('Panel instance not found'));
    }
    
    const panel = window.ylzInjectedPanel;
    
    return new Promise((resolve) => {
        // 测试禁用自动上传
        console.log('🧪 测试禁用自动上传...');
        panel.toggleAutoUpload(false)
            .then(() => {
                console.log('✅ 禁用自动上传成功');
                
                // 检查状态
                setTimeout(() => {
                    checkCurrentAutoUploadStatus()
                        .then(status => {
                            console.log(`  后台状态: ${status.enabled ? '仍启用' : '✅ 已禁用'}`);
                            
                            // 测试重新启用
                            console.log('🧪 测试重新启用自动上传...');
                            panel.toggleAutoUpload(true)
                                .then(() => {
                                    console.log('✅ 重新启用自动上传成功');
                                    
                                    setTimeout(() => {
                                        checkCurrentAutoUploadStatus()
                                            .then(finalStatus => {
                                                console.log(`  最终状态: ${finalStatus.enabled ? '✅ 已启用' : '仍禁用'}`);
                                                resolve(finalStatus);
                                            })
                                            .catch(err => {
                                                console.error('❌ 获取最终状态失败:', err);
                                                resolve(null);
                                            });
                                    }, 1000);
                                })
                                .catch(error => {
                                    console.error('❌ 重新启用失败:', error);
                                    resolve(null);
                                });
                        })
                        .catch(err => {
                            console.error('❌ 获取禁用后状态失败:', err);
                            resolve(null);
                        });
                }, 1000);
            })
            .catch(error => {
                console.error('❌ 禁用自动上传失败:', error);
                resolve(null);
            });
    });
}

// 4. 检查后台检测状态
function checkBackgroundDetectionStatus() {
    console.log('\n=== 检查后台检测状态 ===');
    
    return new Promise((resolve, reject) => {
        if (chrome && chrome.runtime) {
            chrome.runtime.sendMessage({ type: 'GET_BACKGROUND_STATUS' }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error('❌ 获取后台状态失败:', chrome.runtime.lastError.message);
                    reject(chrome.runtime.lastError);
                } else {
                    console.log('✅ 后台状态获取成功');
                    console.log('📊 后台状态详情:', response);
                    
                    if (response && response.success && response.status) {
                        const status = response.status;
                        console.log(`  自动上传启用: ${status.autoUploadEnabled ? '✅' : '❌'}`);
                        console.log(`  检测运行中: ${status.detectionRunning ? '✅' : '❌'}`);
                        console.log(`  当前运行状态: ${status.isRunning ? '运行中' : '空闲'}`);
                        console.log(`  检测间隔: ${status.intervalDuration}ms`);
                        console.log(`  上次文件数: ${status.lastFileCount}`);
                        console.log(`  Popup状态: ${status.popupStatus.isOpen ? '打开' : '关闭'}`);
                        
                        resolve(status);
                    } else {
                        console.log('❌ 后台状态响应格式无效');
                        reject(new Error('Invalid background status response'));
                    }
                }
            });
        } else {
            reject(new Error('Chrome API不可用'));
        }
    });
}

// 5. 清除存储并重新测试默认设置
function testDefaultSettings() {
    console.log('\n=== 测试默认设置 ===');
    
    return new Promise((resolve) => {
        // 清除自动上传设置
        chrome.storage.local.remove('backgroundAutoUploadEnabled', () => {
            console.log('🗑️ 已清除存储的自动上传设置');
            
            // 重新加载扩展来测试默认设置
            console.log('⚠️ 需要重新加载扩展来测试默认设置');
            console.log('📖 请在扩展管理页面点击"重新加载"按钮，然后刷新此页面');
            
            resolve();
        });
    });
}

// 6. 完整的自动上传默认设置测试
async function autoUploadDefaultTest() {
    console.log('🚀 开始完整的自动上传默认设置测试...\n');
    
    try {
        // 1. 检查当前状态
        const currentStatus = await checkCurrentAutoUploadStatus();
        const panelSwitch = checkPanelSwitchStatus();
        
        // 2. 检查后台检测状态
        const backgroundStatus = await checkBackgroundDetectionStatus();
        
        // 3. 验证状态一致性
        console.log('\n=== 状态一致性检查 ===');
        const backendEnabled = currentStatus?.enabled === true;
        const panelEnabled = panelSwitch?.checked === true;
        const backgroundRunning = backgroundStatus?.detectionRunning === true;
        
        console.log(`  后端自动上传: ${backendEnabled ? '✅' : '❌'}`);
        console.log(`  面板开关: ${panelEnabled ? '✅' : '❌'}`);
        console.log(`  后台检测: ${backgroundRunning ? '✅' : '❌'}`);
        
        const isConsistent = backendEnabled === panelEnabled;
        console.log(`  状态一致性: ${isConsistent ? '✅ 一致' : '❌ 不一致'}`);
        
        // 4. 如果默认启用，测试切换功能
        if (backendEnabled) {
            console.log('\n✅ 自动上传默认启用 - 符合预期！');
            
            // 测试切换功能
            await testAutoUploadToggle();
        } else {
            console.log('\n❌ 自动上传默认禁用 - 不符合预期！');
            console.log('🔧 可能需要检查默认设置或重新加载扩展');
        }
        
        // 5. 生成最终报告
        console.log('\n📋 === 自动上传默认设置测试报告 ===');
        console.log(`✅ 后端API: ${currentStatus ? '正常' : '异常'}`);
        console.log(`✅ 面板开关: ${panelSwitch?.exists ? '存在' : '缺失'}`);
        console.log(`✅ 后台检测: ${backgroundStatus ? '正常' : '异常'}`);
        console.log(`${backendEnabled ? '✅' : '❌'} 默认启用: ${backendEnabled ? '是' : '否'}`);
        console.log(`${isConsistent ? '✅' : '❌'} 状态同步: ${isConsistent ? '正常' : '异常'}`);
        
        if (backendEnabled && isConsistent && backgroundRunning) {
            console.log('\n🎉 自动上传默认设置测试通过！');
            console.log('📝 自动上传功能已默认启用，用户无需手动开启');
        } else {
            console.log('\n⚠️ 自动上传默认设置存在问题，需要进一步检查');
        }
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
    }
}

// 导出函数
window.autoUploadDefaultTest = autoUploadDefaultTest;
window.checkCurrentAutoUploadStatus = checkCurrentAutoUploadStatus;
window.checkPanelSwitchStatus = checkPanelSwitchStatus;
window.testAutoUploadToggle = testAutoUploadToggle;
window.checkBackgroundDetectionStatus = checkBackgroundDetectionStatus;
window.testDefaultSettings = testDefaultSettings;

console.log('✅ 自动上传默认设置测试脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - autoUploadDefaultTest() - 运行完整测试');
console.log('  - checkCurrentAutoUploadStatus() - 检查当前状态');
console.log('  - testDefaultSettings() - 测试默认设置（需重新加载扩展）');

// 自动运行测试
autoUploadDefaultTest();

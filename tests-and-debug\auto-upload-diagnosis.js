// 自动上传功能诊断脚本

console.log('🔄 开始自动上传功能诊断...');

// 1. 检查popup是否打开
async function checkPopupStatus() {
    console.log('\n=== 检查popup状态 ===');
    
    try {
        // 检查popup窗口
        const views = chrome.extension.getViews({ type: 'popup' });
        const popupOpen = views.length > 0;
        
        console.log(`📱 Popup状态: ${popupOpen ? '已打开' : '已关闭'}`);
        
        if (popupOpen) {
            console.log('📱 Popup窗口数量:', views.length);
            
            // 检查popup中的自动上传状态
            const popupWindow = views[0];
            if (popupWindow && popupWindow.autoUploadEnabled !== undefined) {
                console.log(`📱 Popup自动上传状态: ${popupWindow.autoUploadEnabled ? '启用' : '禁用'}`);
                console.log(`📱 Popup文件计数: ${popupWindow.lastFileCount || 0}`);
            }
        }
        
        return popupOpen;
    } catch (error) {
        console.error('❌ 检查popup状态失败:', error);
        return false;
    }
}

// 2. 检查后台自动上传状态
async function checkBackgroundAutoUpload() {
    console.log('\n=== 检查后台自动上传状态 ===');
    
    return new Promise((resolve) => {
        chrome.runtime.sendMessage({ type: 'GET_AUTO_UPLOAD_STATUS' }, (response) => {
            if (response && response.success) {
                console.log('📡 后台自动上传状态:');
                console.log(`  启用: ${response.enabled ? '✅' : '❌'}`);
                console.log(`  检测间隔: ${response.intervalDuration / 1000}秒`);
                console.log(`  正在运行: ${response.isRunning ? '是' : '否'}`);
                resolve(response);
            } else {
                console.log('❌ 获取后台自动上传状态失败');
                resolve(null);
            }
        });
    });
}

// 3. 检查本地存储的自动上传设置
async function checkAutoUploadSettings() {
    console.log('\n=== 检查自动上传设置 ===');
    
    return new Promise((resolve) => {
        chrome.storage.local.get([
            'autoUploadEnabled',
            'backgroundAutoUploadEnabled'
        ], (result) => {
            console.log('💾 本地存储设置:');
            console.log(`  popup自动上传: ${result.autoUploadEnabled === true ? '启用' : '禁用'}`);
            console.log(`  后台自动上传: ${result.backgroundAutoUploadEnabled === true ? '启用' : '禁用'}`);
            resolve(result);
        });
    });
}

// 4. 检查当前待上传文件
async function checkPendingFiles() {
    console.log('\n=== 检查待上传文件 ===');
    
    // 从面板获取文件数据
    if (window.ylzInjectedPanel && window.ylzInjectedPanel.fileData) {
        const fileData = window.ylzInjectedPanel.fileData;
        const pendingCount = fileData.pendingCount || 0;
        
        console.log('📁 面板文件数据:');
        console.log(`  待上传文件数: ${pendingCount}`);
        console.log(`  已上传文件数: ${fileData.uploadedCount || 0}`);
        
        if (fileData.pending && fileData.pending.files) {
            console.log('📄 待上传文件列表:');
            fileData.pending.files.forEach((file, index) => {
                console.log(`  ${index + 1}. ${file.name || file.filename} (${file.size ? (file.size / 1024 / 1024).toFixed(1) + 'MB' : '未知大小'})`);
            });
        }
        
        return pendingCount;
    } else {
        console.log('❌ 无法从面板获取文件数据');
        return 0;
    }
}

// 5. 手动触发自动上传检查
async function triggerAutoUploadCheck() {
    console.log('\n=== 手动触发自动上传检查 ===');
    
    try {
        // 检查popup是否打开
        const popupOpen = await checkPopupStatus();
        
        if (popupOpen) {
            console.log('📱 Popup已打开，尝试触发popup中的自动上传检查...');
            
            // 尝试访问popup窗口
            const views = chrome.extension.getViews({ type: 'popup' });
            if (views.length > 0) {
                const popupWindow = views[0];
                if (popupWindow.checkAndTriggerAutoUpload) {
                    console.log('🔧 调用popup的checkAndTriggerAutoUpload()...');
                    popupWindow.checkAndTriggerAutoUpload();
                    console.log('✅ 已触发popup自动上传检查');
                    return true;
                } else {
                    console.log('❌ popup中未找到checkAndTriggerAutoUpload函数');
                }
            }
        } else {
            console.log('📱 Popup已关闭，后台应该负责自动上传');
            console.log('💡 建议: 打开popup窗口以启用前台自动上传');
        }
        
        return false;
    } catch (error) {
        console.error('❌ 触发自动上传检查失败:', error);
        return false;
    }
}

// 6. 启用自动上传功能
async function enableAutoUpload() {
    console.log('\n=== 启用自动上传功能 ===');
    
    try {
        // 1. 设置本地存储
        await new Promise((resolve) => {
            chrome.storage.local.set({
                autoUploadEnabled: true,
                backgroundAutoUploadEnabled: true
            }, resolve);
        });
        
        console.log('✅ 本地存储已更新');
        
        // 2. 通知后台启用自动上传
        const response = await new Promise((resolve) => {
            chrome.runtime.sendMessage({
                type: 'SET_AUTO_UPLOAD_STATE',
                enabled: true
            }, resolve);
        });
        
        if (response && response.success) {
            console.log('✅ 后台自动上传已启用');
        } else {
            console.log('❌ 启用后台自动上传失败');
        }
        
        // 3. 如果popup打开，也更新popup状态
        const views = chrome.extension.getViews({ type: 'popup' });
        if (views.length > 0) {
            const popupWindow = views[0];
            if (popupWindow.autoUploadEnabled !== undefined) {
                popupWindow.autoUploadEnabled = true;
                console.log('✅ Popup自动上传状态已更新');
                
                // 触发popup检查
                if (popupWindow.checkAndTriggerAutoUpload) {
                    setTimeout(() => {
                        popupWindow.checkAndTriggerAutoUpload();
                    }, 1000);
                }
            }
        }
        
        console.log('🎉 自动上传功能已全面启用');
        return true;
        
    } catch (error) {
        console.error('❌ 启用自动上传失败:', error);
        return false;
    }
}

// 7. 完整的自动上传诊断
async function fullAutoUploadDiagnosis() {
    console.log('🚀 开始完整自动上传诊断...\n');
    
    try {
        // 1. 检查popup状态
        const popupOpen = await checkPopupStatus();
        
        // 2. 检查后台状态
        const backgroundStatus = await checkBackgroundAutoUpload();
        
        // 3. 检查设置
        const settings = await checkAutoUploadSettings();
        
        // 4. 检查待上传文件
        const pendingCount = await checkPendingFiles();
        
        // 5. 生成诊断报告
        console.log('\n📋 === 自动上传诊断报告 ===');
        console.log(`✅ Popup状态: ${popupOpen ? '已打开' : '已关闭'}`);
        console.log(`✅ 后台自动上传: ${backgroundStatus?.enabled ? '启用' : '禁用'}`);
        console.log(`✅ Popup自动上传设置: ${settings?.autoUploadEnabled ? '启用' : '禁用'}`);
        console.log(`✅ 后台自动上传设置: ${settings?.backgroundAutoUploadEnabled ? '启用' : '禁用'}`);
        console.log(`✅ 待上传文件数: ${pendingCount}`);
        
        // 6. 分析问题
        console.log('\n🔍 === 问题分析 ===');
        
        const autoUploadEnabled = settings?.autoUploadEnabled || settings?.backgroundAutoUploadEnabled;
        
        if (!autoUploadEnabled) {
            console.log('❌ 自动上传功能未启用');
            console.log('💡 建议: 运行 enableAutoUpload() 启用自动上传');
        } else if (pendingCount === 0) {
            console.log('⚠️ 没有待上传文件');
            console.log('💡 建议: 添加一些文件到云盘目录进行测试');
        } else if (!popupOpen) {
            console.log('⚠️ Popup未打开，依赖后台自动上传');
            console.log('💡 建议: 打开popup窗口以启用前台自动上传');
        } else {
            console.log('✅ 配置看起来正常');
            console.log('💡 建议: 运行 triggerAutoUploadCheck() 手动触发检查');
        }
        
        // 7. 提供解决方案
        console.log('\n🔧 === 解决方案 ===');
        
        if (!autoUploadEnabled) {
            console.log('1. 启用自动上传: enableAutoUpload()');
        }
        
        if (pendingCount > 0 && autoUploadEnabled) {
            console.log('2. 手动触发检查: triggerAutoUploadCheck()');
        }
        
        if (!popupOpen) {
            console.log('3. 打开popup窗口以获得更好的自动上传体验');
        }
        
        return {
            popupOpen,
            backgroundStatus,
            settings,
            pendingCount,
            autoUploadEnabled
        };
        
    } catch (error) {
        console.error('❌ 诊断过程中出现错误:', error);
        return null;
    }
}

// 8. 快速修复自动上传
async function quickFixAutoUpload() {
    console.log('\n⚡ 快速修复自动上传...');
    
    try {
        // 1. 启用自动上传
        const enableResult = await enableAutoUpload();
        
        if (enableResult) {
            // 2. 等待2秒
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 3. 触发检查
            const triggerResult = await triggerAutoUploadCheck();
            
            if (triggerResult) {
                console.log('🎉 自动上传快速修复完成！');
                console.log('⏰ 请等待几秒钟观察是否开始上传');
            } else {
                console.log('⚠️ 触发检查失败，可能需要手动操作');
            }
        } else {
            console.log('❌ 启用自动上传失败');
        }
        
    } catch (error) {
        console.error('❌ 快速修复失败:', error);
    }
}

// 导出函数
window.checkPopupStatus = checkPopupStatus;
window.checkBackgroundAutoUpload = checkBackgroundAutoUpload;
window.checkAutoUploadSettings = checkAutoUploadSettings;
window.checkPendingFiles = checkPendingFiles;
window.triggerAutoUploadCheck = triggerAutoUploadCheck;
window.enableAutoUpload = enableAutoUpload;
window.fullAutoUploadDiagnosis = fullAutoUploadDiagnosis;
window.quickFixAutoUpload = quickFixAutoUpload;

console.log('✅ 自动上传诊断脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - fullAutoUploadDiagnosis() - 完整诊断');
console.log('  - quickFixAutoUpload() - 快速修复');
console.log('  - enableAutoUpload() - 启用自动上传');
console.log('  - triggerAutoUploadCheck() - 手动触发检查');

// 自动运行完整诊断
fullAutoUploadDiagnosis();

// 通信测试脚本 - 测试面板与background的通信

console.log('🔗 开始通信测试...');

// 1. 测试页面事件通信
function testPageEventCommunication() {
    console.log('\n=== 测试页面事件通信 ===');
    
    // 模拟面板发送消息
    const messageId = 'test_' + Date.now();
    
    // 监听响应
    const responseHandler = (event) => {
        console.log('✅ 收到响应:', event.detail);
        window.removeEventListener('ylz_panel_response_' + messageId, responseHandler);
    };
    
    window.addEventListener('ylz_panel_response_' + messageId, responseHandler);
    
    // 发送测试消息
    const testEvent = new CustomEvent('ylz_panel_message', {
        detail: {
            messageId: messageId,
            message: { type: 'GET_CONNECTION_STATUS' }
        }
    });
    
    console.log('📤 发送测试消息...');
    window.dispatchEvent(testEvent);
    
    // 5秒后清理
    setTimeout(() => {
        window.removeEventListener('ylz_panel_response_' + messageId, responseHandler);
        console.log('⏰ 测试超时，清理监听器');
    }, 5000);
}

// 2. 测试面板实例通信
function testPanelInstanceCommunication() {
    console.log('\n=== 测试面板实例通信 ===');
    
    if (window.ylzInjectedPanel) {
        console.log('✅ 面板实例存在');
        
        // 测试面板的safeRuntimeMessage方法
        console.log('📤 测试面板消息发送...');
        
        window.ylzInjectedPanel.safeRuntimeMessage({ type: 'GET_CONNECTION_STATUS' })
            .then(response => {
                console.log('✅ 面板消息发送成功:', response);
            })
            .catch(error => {
                console.log('❌ 面板消息发送失败:', error);
            });
            
    } else {
        console.log('❌ 面板实例不存在');
    }
}

// 3. 测试background消息接收
function testBackgroundMessageReceiving() {
    console.log('\n=== 测试background消息接收 ===');
    
    // 监听background消息
    const messageHandler = (event) => {
        console.log('✅ 收到background消息:', event.detail);
    };
    
    window.addEventListener('ylz_background_message', messageHandler);
    
    console.log('👂 已设置background消息监听器');
    
    // 模拟发送一个消息来触发background响应
    if (chrome && chrome.runtime) {
        chrome.runtime.sendMessage({ type: 'GET_CONNECTION_STATUS' }, (response) => {
            console.log('📨 直接Chrome API响应:', response);
        });
    }
    
    // 10秒后清理
    setTimeout(() => {
        window.removeEventListener('ylz_background_message', messageHandler);
        console.log('⏰ 清理background消息监听器');
    }, 10000);
}

// 4. 测试完整通信流程
function testFullCommunicationFlow() {
    console.log('\n=== 测试完整通信流程 ===');
    
    const steps = [
        {
            name: '检查Chrome API',
            test: () => !!(chrome && chrome.runtime)
        },
        {
            name: '检查面板元素',
            test: () => !!document.getElementById('ylz-injected-panel')
        },
        {
            name: '检查面板实例',
            test: () => !!window.ylzInjectedPanel
        },
        {
            name: '检查事件监听器',
            test: () => {
                // 简单测试：发送一个事件看是否有响应
                let hasListener = false;
                const testEvent = new CustomEvent('ylz_panel_message', {
                    detail: { messageId: 'test', message: { type: 'TEST' } }
                });
                
                // 临时监听器来检测是否有处理
                const tempHandler = () => { hasListener = true; };
                window.addEventListener('ylz_panel_response_test', tempHandler);
                
                window.dispatchEvent(testEvent);
                
                setTimeout(() => {
                    window.removeEventListener('ylz_panel_response_test', tempHandler);
                }, 100);
                
                return true; // 假设有监听器
            }
        }
    ];
    
    console.log('🔍 执行通信检查步骤:');
    
    steps.forEach((step, index) => {
        try {
            const result = step.test();
            console.log(`  ${index + 1}. ${step.name}: ${result ? '✅' : '❌'}`);
        } catch (error) {
            console.log(`  ${index + 1}. ${step.name}: ❌ (错误: ${error.message})`);
        }
    });
}

// 5. 修复建议
function provideFixes() {
    console.log('\n=== 🔧 修复建议 ===');
    
    const issues = [];
    
    // 检查各种问题
    if (!chrome || !chrome.runtime) {
        issues.push({
            problem: 'Chrome扩展API不可用',
            solution: '确保脚本在扩展上下文中运行，或使用页面事件通信'
        });
    }
    
    if (!document.getElementById('ylz-injected-panel')) {
        issues.push({
            problem: '面板元素不存在',
            solution: '检查面板HTML是否正确注入'
        });
    }
    
    if (!window.ylzInjectedPanel) {
        issues.push({
            problem: '面板实例不存在',
            solution: '检查面板脚本是否正确加载和初始化'
        });
    }
    
    // 检查事件监听器
    const hasEventListeners = window.getEventListeners && 
        window.getEventListeners(window)['ylz_panel_message'];
    
    if (!hasEventListeners) {
        issues.push({
            problem: '页面事件监听器可能未设置',
            solution: '确保content script正确设置了事件监听器'
        });
    }
    
    if (issues.length === 0) {
        console.log('✅ 未发现明显问题');
    } else {
        console.log('❌ 发现以下问题:');
        issues.forEach((issue, index) => {
            console.log(`\n${index + 1}. 问题: ${issue.problem}`);
            console.log(`   解决方案: ${issue.solution}`);
        });
    }
    
    // 提供通用修复步骤
    console.log('\n📋 通用修复步骤:');
    console.log('1. 重新加载扩展');
    console.log('2. 刷新页面');
    console.log('3. 检查控制台错误');
    console.log('4. 确认目标网站正确');
    console.log('5. 检查manifest.json配置');
}

// 6. 主测试函数
function testCommunication() {
    console.log('🚀 开始完整通信测试...\n');
    
    // 按顺序执行测试
    testFullCommunicationFlow();
    
    setTimeout(() => {
        testPageEventCommunication();
    }, 1000);
    
    setTimeout(() => {
        testPanelInstanceCommunication();
    }, 2000);
    
    setTimeout(() => {
        testBackgroundMessageReceiving();
    }, 3000);
    
    setTimeout(() => {
        provideFixes();
    }, 5000);
}

// 7. 手动修复函数
function manualFix() {
    console.log('\n🔧 尝试手动修复...');
    
    // 重新初始化面板
    if (typeof initYlzInjectedPanel === 'function') {
        console.log('🔄 重新初始化面板...');
        initYlzInjectedPanel();
    }
    
    // 重新设置通信
    if (typeof setupPanelCommunication === 'function') {
        console.log('🔄 重新设置通信...');
        setupPanelCommunication();
    }
    
    // 检查修复结果
    setTimeout(() => {
        console.log('✅ 修复完成，重新测试...');
        testCommunication();
    }, 2000);
}

// 导出函数到全局
window.testCommunication = testCommunication;
window.testPageEventCommunication = testPageEventCommunication;
window.testPanelInstanceCommunication = testPanelInstanceCommunication;
window.testBackgroundMessageReceiving = testBackgroundMessageReceiving;
window.manualFix = manualFix;

console.log('✅ 通信测试脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - testCommunication() - 运行完整通信测试');
console.log('  - testPageEventCommunication() - 测试页面事件通信');
console.log('  - testPanelInstanceCommunication() - 测试面板实例通信');
console.log('  - testBackgroundMessageReceiving() - 测试background消息接收');
console.log('  - manualFix() - 尝试手动修复');

// 自动运行测试
testCommunication();

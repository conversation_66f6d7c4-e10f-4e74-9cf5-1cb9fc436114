// 综合诊断和修复脚本

console.log('🔍 开始综合诊断...');

// 1. 检查自动刷新定时器状态
async function checkAutoRefreshTimer() {
    console.log('\n=== 检查自动刷新定时器 ===');
    
    const response = await new Promise((resolve) => {
        chrome.runtime.sendMessage({ type: 'GET_AUTO_REFRESH_STATUS' }, resolve);
    });
    
    console.log('📊 自动刷新状态:', response);
    
    if (response && response.success) {
        console.log(`  启用: ${response.enabled ? '✅' : '❌'}`);
        console.log(`  间隔: ${response.interval}秒`);
        console.log(`  目标标签页: ${response.targetTabId || 'null'}`);
        console.log(`  上次刷新: ${response.lastRefreshTime ? new Date(response.lastRefreshTime).toLocaleTimeString() : '从未'}`);
        
        if (response.enabled && response.interval > 0) {
            console.log('✅ 自动刷新配置正常');
            
            // 手动触发一次检查
            console.log('🔧 手动触发刷新检查...');
            const manualResult = await new Promise((resolve) => {
                chrome.runtime.sendMessage({ type: 'MANUAL_REFRESH_CHECK' }, resolve);
            });
            
            console.log('🔧 手动检查结果:', manualResult);
            return true;
        } else {
            console.log('❌ 自动刷新未正确配置');
            return false;
        }
    } else {
        console.log('❌ 获取自动刷新状态失败');
        return false;
    }
}

// 2. 检查WebSocket连接状态
async function checkWebSocketStatus() {
    console.log('\n=== 检查WebSocket连接状态 ===');
    
    const response = await new Promise((resolve) => {
        chrome.runtime.sendMessage({ type: 'GET_CONNECTION_STATUS' }, resolve);
    });
    
    console.log('📡 WebSocket状态:', response);
    
    if (response && response.success !== undefined) {
        console.log(`  连接状态: ${response.connected ? '✅ 已连接' : '❌ 未连接'}`);
        console.log(`  WebSocket状态码: ${response.readyState}`);
        
        if (!response.connected) {
            console.log('🔧 尝试重置WebSocket连接...');
            const resetResult = await new Promise((resolve) => {
                chrome.runtime.sendMessage({ type: 'RESET_CONNECTION' }, resolve);
            });
            
            console.log('🔧 重置结果:', resetResult);
            
            // 等待3秒后再次检查
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            const newStatus = await new Promise((resolve) => {
                chrome.runtime.sendMessage({ type: 'GET_CONNECTION_STATUS' }, resolve);
            });
            
            console.log('📡 重置后状态:', newStatus);
            return newStatus?.connected || false;
        }
        
        return response.connected;
    } else {
        console.log('❌ 获取WebSocket状态失败');
        return false;
    }
}

// 3. 检查文件树数据
async function checkFileTreeData() {
    console.log('\n=== 检查文件树数据 ===');
    
    const response = await new Promise((resolve) => {
        chrome.runtime.sendMessage({ type: 'GET_ADMIN_TREE' }, resolve);
    });
    
    console.log('📁 文件树响应:', response);
    
    if (response && response.success && response.tree) {
        const tree = response.tree;
        console.log('📊 文件树统计:');
        console.log(`  文件数量: ${tree.files ? tree.files.length : 0}`);
        console.log(`  文件夹数量: ${tree.folders ? tree.folders.length : 0}`);
        
        if (tree.files && tree.files.length > 0) {
            console.log('✅ 有文件数据');
            console.log('📄 前3个文件:', tree.files.slice(0, 3).map(f => f.name || f.filename));
        } else {
            console.log('❌ 没有文件数据');
        }
        
        if (tree.folders && tree.folders.length > 0) {
            console.log('✅ 有文件夹数据');
            console.log('📁 前3个文件夹:', tree.folders.slice(0, 3).map(f => f.name));
        } else {
            console.log('❌ 没有文件夹数据');
        }
        
        return {
            hasFiles: tree.files && tree.files.length > 0,
            hasFolders: tree.folders && tree.folders.length > 0,
            tree: tree
        };
    } else {
        console.log('❌ 获取文件树数据失败');
        return { hasFiles: false, hasFolders: false, tree: null };
    }
}

// 4. 检查面板连接状态
function checkPanelConnection() {
    console.log('\n=== 检查面板连接状态 ===');
    
    const panel = document.getElementById('ylz-injected-panel');
    if (!panel) {
        console.log('❌ 面板不存在');
        return false;
    }
    
    const connectionStatus = panel.querySelector('#ylz-connection-status');
    if (connectionStatus) {
        const statusText = connectionStatus.textContent;
        console.log(`📡 面板连接状态: ${statusText}`);
        
        if (statusText.includes('已连接')) {
            console.log('✅ 面板连接正常');
            return true;
        } else {
            console.log('❌ 面板连接异常');
            
            // 尝试手动连接
            const connectBtn = panel.querySelector('#ylz-connect-btn');
            if (connectBtn && !connectBtn.disabled) {
                console.log('🔧 尝试手动连接...');
                connectBtn.click();
                return false;
            }
        }
    }
    
    return false;
}

// 5. 强制刷新文件树数据
async function forceRefreshFileTree() {
    console.log('\n=== 强制刷新文件树数据 ===');
    
    // 通过WebSocket请求最新数据
    const wsResult = await new Promise((resolve) => {
        chrome.runtime.sendMessage({ 
            type: 'SEND_WS_MESSAGE',
            wsMessage: {
                type: 'get_full_admin_file_tree',
                forceRefresh: true,
                timestamp: Date.now()
            }
        }, resolve);
    });
    
    console.log('📡 WebSocket请求结果:', wsResult);
    
    // 等待2秒让数据更新
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 重新检查文件树
    const newTreeData = await checkFileTreeData();
    
    return newTreeData;
}

// 6. 设置短间隔自动刷新进行测试
async function setupTestAutoRefresh() {
    console.log('\n=== 设置测试自动刷新 ===');
    
    // 确保勾选"刷新当前页面"
    const allowActiveRefreshCheckbox = document.querySelector('#ylz-allowActiveRefresh');
    if (allowActiveRefreshCheckbox && !allowActiveRefreshCheckbox.checked) {
        console.log('✅ 勾选"刷新当前页面"');
        allowActiveRefreshCheckbox.click();
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // 设置30秒自动刷新（便于测试）
    console.log('🔧 设置30秒自动刷新...');
    const response = await new Promise((resolve) => {
        chrome.runtime.sendMessage({ 
            type: 'SET_AUTO_REFRESH', 
            interval: 30 
        }, resolve);
    });
    
    console.log('🔧 设置结果:', response);
    
    if (response && response.success) {
        console.log('✅ 30秒自动刷新设置成功');
        console.log('⏰ 请等待30秒观察页面是否自动刷新');
        
        // 启动倒计时
        let countdown = 30;
        const countdownTimer = setInterval(() => {
            countdown--;
            if (countdown > 0 && countdown % 5 === 0) {
                console.log(`⏰ 倒计时: ${countdown}秒`);
            } else if (countdown === 0) {
                console.log('⏰ 时间到！检查页面是否刷新...');
                clearInterval(countdownTimer);
                
                // 检查页面是否刷新
                setTimeout(() => {
                    const timeSinceLoad = (Date.now() - window.performance.timing.navigationStart) / 1000;
                    if (timeSinceLoad < 35) {
                        console.log('🎉 页面可能已自动刷新！');
                    } else {
                        console.log('⚠️ 页面似乎没有自动刷新');
                    }
                }, 2000);
            }
        }, 1000);
        
        return true;
    } else {
        console.log('❌ 设置自动刷新失败');
        return false;
    }
}

// 7. 完整诊断流程
async function fullDiagnosis() {
    console.log('🚀 开始完整诊断流程...\n');
    
    try {
        // 1. 检查面板连接
        const panelOK = checkPanelConnection();
        
        // 2. 检查WebSocket状态
        const wsOK = await checkWebSocketStatus();
        
        // 3. 检查文件树数据
        const treeData = await checkFileTreeData();
        
        // 4. 如果文件树数据不完整，强制刷新
        let refreshedTreeData = treeData;
        if (!treeData.hasFiles || !treeData.hasFolders) {
            console.log('\n🔧 文件树数据不完整，强制刷新...');
            refreshedTreeData = await forceRefreshFileTree();
        }
        
        // 5. 检查自动刷新定时器
        const autoRefreshOK = await checkAutoRefreshTimer();
        
        // 6. 生成诊断报告
        console.log('\n📋 === 综合诊断报告 ===');
        console.log(`✅ 面板连接: ${panelOK ? '正常' : '异常'}`);
        console.log(`✅ WebSocket连接: ${wsOK ? '正常' : '异常'}`);
        console.log(`✅ 文件数据: ${refreshedTreeData.hasFiles ? '正常' : '异常'}`);
        console.log(`✅ 文件夹数据: ${refreshedTreeData.hasFolders ? '正常' : '异常'}`);
        console.log(`✅ 自动刷新: ${autoRefreshOK ? '正常' : '异常'}`);
        
        const allOK = panelOK && wsOK && refreshedTreeData.hasFiles && autoRefreshOK;
        
        if (allOK) {
            console.log('\n🎉 所有功能正常！');
            console.log('💡 建议: 设置测试自动刷新验证功能');
            
            // 询问是否设置测试
            console.log('\n❓ 是否设置30秒测试自动刷新？');
            console.log('💡 运行 setupTestAutoRefresh() 开始测试');
            
        } else {
            console.log('\n⚠️ 发现问题，建议修复：');
            
            if (!panelOK) {
                console.log('🔧 面板连接异常 - 尝试手动点击连接按钮');
            }
            if (!wsOK) {
                console.log('🔧 WebSocket连接异常 - 已尝试重置连接');
            }
            if (!refreshedTreeData.hasFiles) {
                console.log('🔧 文件数据缺失 - 检查服务器端文件扫描');
            }
            if (!refreshedTreeData.hasFolders) {
                console.log('🔧 文件夹数据缺失 - 检查服务器端目录结构');
            }
            if (!autoRefreshOK) {
                console.log('🔧 自动刷新异常 - 检查定时器设置');
            }
        }
        
        return {
            panelOK,
            wsOK,
            treeData: refreshedTreeData,
            autoRefreshOK,
            allOK
        };
        
    } catch (error) {
        console.error('❌ 诊断过程中出现错误:', error);
        return null;
    }
}

// 导出函数
window.checkAutoRefreshTimer = checkAutoRefreshTimer;
window.checkWebSocketStatus = checkWebSocketStatus;
window.checkFileTreeData = checkFileTreeData;
window.checkPanelConnection = checkPanelConnection;
window.forceRefreshFileTree = forceRefreshFileTree;
window.setupTestAutoRefresh = setupTestAutoRefresh;
window.fullDiagnosis = fullDiagnosis;

console.log('✅ 综合诊断脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - fullDiagnosis() - 完整诊断');
console.log('  - setupTestAutoRefresh() - 设置30秒测试自动刷新');

// 自动运行完整诊断
fullDiagnosis();

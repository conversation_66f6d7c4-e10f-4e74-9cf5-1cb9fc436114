// 数据对比测试脚本 - 对比popup和注入面板的数据获取

console.log('📊 开始数据对比测试...');

// 1. 获取原始文件树数据
function getRawTreeData() {
    console.log('\n=== 获取原始文件树数据 ===');
    
    return new Promise((resolve, reject) => {
        if (chrome && chrome.runtime) {
            chrome.runtime.sendMessage({ type: 'GET_ADMIN_TREE' }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error('❌ 获取数据失败:', chrome.runtime.lastError.message);
                    reject(chrome.runtime.lastError);
                } else {
                    console.log('✅ 原始数据获取成功');
                    console.log('📊 数据结构:', response);
                    resolve(response);
                }
            });
        } else {
            reject(new Error('Chrome API不可用'));
        }
    });
}

// 2. 模拟popup的数据处理逻辑
function processDataLikePopup(treeData) {
    console.log('\n=== 模拟popup数据处理 ===');
    
    if (!treeData || !treeData.success || !treeData.tree) {
        console.log('❌ 无效的树数据');
        return { pending: [], uploaded: [] };
    }
    
    const tree = treeData.tree;
    console.log('🌳 处理树数据:', tree);
    
    // 检查文件是否已上传（popup逻辑）
    const isFileUploaded = (file) => {
        if (!file) return false;
        
        const serverStatus = file.syncStatus || file.status;
        const uploadedStatuses = ['已同步', '已上传', 'uploaded'];
        const isUploaded = uploadedStatuses.includes(serverStatus);
        
        return isUploaded;
    };
    
    // 过滤文件树（popup逻辑）
    const filterTreeByUploadStatus = (tree, showUploaded) => {
        if (!tree) return null;
        
        const filtered = {
            files: [],
            folders: []
        };
        
        // 过滤文件
        if (tree.files && Array.isArray(tree.files)) {
            filtered.files = tree.files.filter(file => {
                const isUploaded = isFileUploaded(file);
                return showUploaded ? isUploaded : !isUploaded;
            });
        }
        
        // 递归过滤文件夹
        if (tree.folders && Array.isArray(tree.folders)) {
            tree.folders.forEach(folder => {
                const filteredFolder = filterTreeByUploadStatus(folder.children, showUploaded);
                
                // 只有当文件夹包含符合条件的文件或子文件夹时才包含它
                if (filteredFolder && (filteredFolder.files.length > 0 || filteredFolder.folders.length > 0)) {
                    filtered.folders.push({
                        ...folder,
                        children: filteredFolder
                    });
                }
            });
        }
        
        return filtered;
    };
    
    // 获取pending和uploaded文件
    const pendingTree = filterTreeByUploadStatus(tree, false);
    const uploadedTree = filterTreeByUploadStatus(tree, true);
    
    // 扁平化文件列表
    const flattenFiles = (tree) => {
        const files = [];
        
        const processTree = (node) => {
            if (!node) return;
            
            // 添加直接文件
            if (node.files && Array.isArray(node.files)) {
                files.push(...node.files);
            }
            
            // 递归处理文件夹
            if (node.folders && Array.isArray(node.folders)) {
                node.folders.forEach(folder => {
                    if (folder.children) {
                        processTree(folder.children);
                    }
                });
            }
        };
        
        processTree(tree);
        return files;
    };
    
    const pendingFiles = flattenFiles(pendingTree);
    const uploadedFiles = flattenFiles(uploadedTree);
    
    console.log('📊 popup逻辑处理结果:');
    console.log(`  pending文件: ${pendingFiles.length} 个`);
    console.log(`  uploaded文件: ${uploadedFiles.length} 个`);
    
    if (pendingFiles.length > 0) {
        console.log('  pending示例:', pendingFiles.slice(0, 3).map(f => ({
            name: f.name || f.filename,
            status: f.status || f.syncStatus
        })));
    }
    
    if (uploadedFiles.length > 0) {
        console.log('  uploaded示例:', uploadedFiles.slice(0, 3).map(f => ({
            name: f.name || f.filename,
            status: f.status || f.syncStatus
        })));
    }
    
    return { pending: pendingFiles, uploaded: uploadedFiles };
}

// 3. 模拟注入面板的数据处理逻辑
function processDataLikePanel(treeData) {
    console.log('\n=== 模拟注入面板数据处理 ===');
    
    if (!treeData || !treeData.success || !treeData.tree) {
        console.log('❌ 无效的树数据');
        return { pending: [], uploaded: [] };
    }
    
    const tree = treeData.tree;
    console.log('🌳 处理树数据:', tree);
    
    const pendingFiles = [];
    const uploadedFiles = [];

    // 检查文件是否已上传（注入面板逻辑）
    const isFileUploaded = (file) => {
        if (!file) return false;
        
        const serverStatus = file.syncStatus || file.status;
        const uploadedStatuses = ['已同步', '已上传', 'uploaded'];
        const isUploaded = uploadedStatuses.includes(serverStatus);
        
        return isUploaded;
    };

    // 递归处理文件树结构
    const processTree = (tree) => {
        if (!tree) return;

        // 处理直接文件
        if (tree.files && Array.isArray(tree.files)) {
            tree.files.forEach(file => {
                if (isFileUploaded(file)) {
                    uploadedFiles.push(file);
                } else {
                    pendingFiles.push(file);
                }
            });
        }

        // 递归处理文件夹
        if (tree.folders && Array.isArray(tree.folders)) {
            tree.folders.forEach(folder => {
                // 处理文件夹中的文件
                if (folder.children) {
                    processTree(folder.children);
                }
                // 也处理直接在folder下的files
                if (folder.files) {
                    folder.files.forEach(file => {
                        if (isFileUploaded(file)) {
                            uploadedFiles.push(file);
                        } else {
                            pendingFiles.push(file);
                        }
                    });
                }
            });
        }
    };

    // 开始处理
    processTree(tree);
    
    console.log('📊 注入面板逻辑处理结果:');
    console.log(`  pending文件: ${pendingFiles.length} 个`);
    console.log(`  uploaded文件: ${uploadedFiles.length} 个`);
    
    if (pendingFiles.length > 0) {
        console.log('  pending示例:', pendingFiles.slice(0, 3).map(f => ({
            name: f.name || f.filename,
            status: f.status || f.syncStatus
        })));
    }
    
    if (uploadedFiles.length > 0) {
        console.log('  uploaded示例:', uploadedFiles.slice(0, 3).map(f => ({
            name: f.name || f.filename,
            status: f.status || f.syncStatus
        })));
    }
    
    return { pending: pendingFiles, uploaded: uploadedFiles };
}

// 4. 对比两种处理结果
function compareResults(popupResult, panelResult) {
    console.log('\n=== 对比处理结果 ===');
    
    console.log('📊 数量对比:');
    console.log(`  popup pending: ${popupResult.pending.length}`);
    console.log(`  panel pending: ${panelResult.pending.length}`);
    console.log(`  popup uploaded: ${popupResult.uploaded.length}`);
    console.log(`  panel uploaded: ${panelResult.uploaded.length}`);
    
    // 检查差异
    const pendingDiff = Math.abs(popupResult.pending.length - panelResult.pending.length);
    const uploadedDiff = Math.abs(popupResult.uploaded.length - panelResult.uploaded.length);
    
    if (pendingDiff > 0 || uploadedDiff > 0) {
        console.log('⚠️ 发现数据处理差异:');
        console.log(`  pending差异: ${pendingDiff}`);
        console.log(`  uploaded差异: ${uploadedDiff}`);
        
        // 分析差异原因
        if (pendingDiff > 0) {
            console.log('🔍 分析pending差异...');
            const popupNames = new Set(popupResult.pending.map(f => f.name || f.filename));
            const panelNames = new Set(panelResult.pending.map(f => f.name || f.filename));
            
            const onlyInPopup = [...popupNames].filter(name => !panelNames.has(name));
            const onlyInPanel = [...panelNames].filter(name => !popupNames.has(name));
            
            if (onlyInPopup.length > 0) {
                console.log('  只在popup中的文件:', onlyInPopup.slice(0, 5));
            }
            if (onlyInPanel.length > 0) {
                console.log('  只在panel中的文件:', onlyInPanel.slice(0, 5));
            }
        }
    } else {
        console.log('✅ 两种处理逻辑结果一致');
    }
}

// 5. 主测试函数
async function dataComparisonTest() {
    console.log('🚀 开始数据对比测试...\n');
    
    try {
        // 获取原始数据
        const rawData = await getRawTreeData();
        
        // 用两种逻辑处理数据
        const popupResult = processDataLikePopup(rawData);
        const panelResult = processDataLikePanel(rawData);
        
        // 对比结果
        compareResults(popupResult, panelResult);
        
        // 检查当前面板状态
        console.log('\n=== 检查当前面板状态 ===');
        if (window.ylzInjectedPanel && window.ylzInjectedPanel.fileData) {
            const currentData = window.ylzInjectedPanel.fileData;
            console.log('📊 面板当前数据:');
            console.log(`  pending: ${currentData.pending?.length || 0}`);
            console.log(`  uploaded: ${currentData.uploaded?.length || 0}`);
            
            // 对比面板当前数据与预期结果
            if (currentData.pending?.length !== panelResult.pending.length ||
                currentData.uploaded?.length !== panelResult.uploaded.length) {
                console.log('⚠️ 面板当前数据与预期不符，可能需要重新处理');
                
                // 尝试手动更新面板数据
                console.log('🔄 尝试手动更新面板数据...');
                window.ylzInjectedPanel.updateFileTree(rawData.tree);
            }
        } else {
            console.log('❌ 面板实例或数据不存在');
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
    }
}

// 导出函数
window.dataComparisonTest = dataComparisonTest;
window.getRawTreeData = getRawTreeData;
window.processDataLikePopup = processDataLikePopup;
window.processDataLikePanel = processDataLikePanel;

console.log('✅ 数据对比测试脚本加载完成！');
console.log('📖 使用方法: dataComparisonTest()');

// 自动运行测试
dataComparisonTest();

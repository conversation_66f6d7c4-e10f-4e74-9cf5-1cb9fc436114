// 深度调试消息传递问题

console.log('🔬 开始深度调试消息传递...');

// 1. 检查面板的safeRuntimeMessage实际发送的数据
function debugPanelMessageSending() {
    console.log('\n=== 调试面板消息发送 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return false;
    }
    
    const panel = window.ylzInjectedPanel;
    
    // 创建测试上传请求
    const testFiles = [
        {
            name: 'debug-test.txt',
            path: 'debug-test.txt',
            size: 1024,
            type: 'text'
        }
    ];
    
    const uploadRequest = {
        type: 'START_YUNPAN_UPLOAD',
        data: {
            files: testFiles,
            source: 'debug_test'
        }
    };
    
    console.log('📊 准备发送的上传请求:', uploadRequest);
    
    // 监听面板发送的事件
    const messageHandler = (event) => {
        if (event.type === 'ylz_panel_message') {
            console.log('📤 面板发送的实际消息事件:', event.detail);
            console.log('  messageId:', event.detail.messageId);
            console.log('  message:', event.detail.message);
            console.log('  message.type:', event.detail.message?.type);
            console.log('  message.data:', event.detail.message?.data);
            console.log('  message.data.files:', event.detail.message?.data?.files);
        }
    };
    
    window.addEventListener('ylz_panel_message', messageHandler);
    
    // 发送测试消息
    setTimeout(() => {
        console.log('🚀 发送测试上传请求...');
        panel.safeRuntimeMessage(uploadRequest).then(response => {
            console.log('📥 收到响应:', response);
            window.removeEventListener('ylz_panel_message', messageHandler);
        }).catch(error => {
            console.error('❌ 发送失败:', error);
            window.removeEventListener('ylz_panel_message', messageHandler);
        });
    }, 1000);
    
    return true;
}

// 2. 手动模拟面板消息发送过程
function manualSimulatePanelMessage() {
    console.log('\n=== 手动模拟面板消息发送 ===');
    
    const testFiles = [
        {
            name: 'manual-test.txt',
            path: 'manual-test.txt',
            size: 2048,
            type: 'text'
        }
    ];
    
    const uploadMessage = {
        type: 'START_YUNPAN_UPLOAD',
        data: {
            files: testFiles,
            source: 'manual_simulation'
        }
    };
    
    const messageId = 'manual_' + Date.now();
    
    console.log('📤 手动发送消息:', {
        messageId: messageId,
        message: uploadMessage
    });
    
    // 监听响应
    const responseHandler = (event) => {
        console.log('📥 收到手动消息响应:', event.detail);
    };
    
    window.addEventListener('ylz_panel_response_' + messageId, responseHandler);
    
    // 发送消息事件
    const customEvent = new CustomEvent('ylz_panel_message', {
        detail: {
            messageId: messageId,
            message: uploadMessage
        }
    });
    
    window.dispatchEvent(customEvent);
    
    // 清理监听器
    setTimeout(() => {
        window.removeEventListener('ylz_panel_response_' + messageId, responseHandler);
    }, 5000);
    
    return true;
}

// 3. 检查content script是否正确接收消息
function checkContentScriptReceiving() {
    console.log('\n=== 检查content script消息接收 ===');
    
    // 创建一个测试消息
    const testMessage = {
        type: 'GET_CONNECTION_STATUS'
    };
    
    const messageId = 'content_test_' + Date.now();
    
    console.log('📤 发送测试消息到content script...');
    
    // 监听响应
    const responseHandler = (event) => {
        console.log('📥 Content script响应:', event.detail);
    };
    
    window.addEventListener('ylz_panel_response_' + messageId, responseHandler);
    
    // 发送消息
    const testEvent = new CustomEvent('ylz_panel_message', {
        detail: {
            messageId: messageId,
            message: testMessage
        }
    });
    
    window.dispatchEvent(testEvent);
    
    // 清理
    setTimeout(() => {
        window.removeEventListener('ylz_panel_response_' + messageId, responseHandler);
    }, 3000);
    
    return true;
}

// 4. 直接测试chrome.runtime.sendMessage
async function testDirectChromeMessage() {
    console.log('\n=== 直接测试chrome.runtime.sendMessage ===');
    
    const testFiles = [
        {
            name: 'direct-chrome-test.txt',
            path: 'direct-chrome-test.txt',
            size: 3072,
            type: 'text'
        }
    ];
    
    const uploadRequest = {
        type: 'START_YUNPAN_UPLOAD',
        data: {
            files: testFiles,
            source: 'direct_chrome_test'
        }
    };
    
    console.log('📤 直接发送到background:', uploadRequest);
    
    try {
        const response = await new Promise((resolve, reject) => {
            chrome.runtime.sendMessage(uploadRequest, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });
        
        console.log('📥 直接响应:', response);
        
        if (response && response.error === "上传请求数据为空") {
            console.log('❌ 即使直接发送也收到"数据为空"错误');
            console.log('🔍 这说明问题在background script的数据解析');
        }
        
        return response;
        
    } catch (error) {
        console.error('❌ 直接发送失败:', error);
        return null;
    }
}

// 5. 检查background script接收到的实际数据
async function inspectBackgroundReceiving() {
    console.log('\n=== 检查background script接收数据 ===');
    
    // 发送一个带有调试信息的请求
    const debugRequest = {
        type: 'START_YUNPAN_UPLOAD',
        data: {
            files: [
                {
                    name: 'inspect-test.txt',
                    path: 'inspect-test.txt',
                    size: 4096,
                    type: 'text'
                }
            ],
            source: 'background_inspection',
            debug: true,
            timestamp: Date.now()
        },
        debug: true
    };
    
    console.log('📤 发送调试请求:', debugRequest);
    
    try {
        const response = await new Promise((resolve) => {
            chrome.runtime.sendMessage(debugRequest, resolve);
        });
        
        console.log('📥 调试响应:', response);
        
        // 分析响应
        if (response && response.error === "上传请求数据为空") {
            console.log('🔍 分析: background script中request.data为undefined');
            console.log('💡 可能原因:');
            console.log('  1. 消息序列化/反序列化问题');
            console.log('  2. background script消息处理逻辑问题');
            console.log('  3. Chrome扩展消息传递限制');
        }
        
        return response;
        
    } catch (error) {
        console.error('❌ 调试请求失败:', error);
        return null;
    }
}

// 6. 测试不同的消息格式
async function testDifferentMessageFormats() {
    console.log('\n=== 测试不同的消息格式 ===');
    
    const testCases = [
        {
            name: '标准格式',
            message: {
                type: 'START_YUNPAN_UPLOAD',
                data: {
                    files: [{ name: 'test1.txt', size: 1024 }],
                    source: 'format_test_1'
                }
            }
        },
        {
            name: '扁平格式',
            message: {
                type: 'START_YUNPAN_UPLOAD',
                files: [{ name: 'test2.txt', size: 1024 }],
                source: 'format_test_2'
            }
        },
        {
            name: '嵌套data格式',
            message: {
                type: 'START_YUNPAN_UPLOAD',
                requestData: {
                    files: [{ name: 'test3.txt', size: 1024 }],
                    source: 'format_test_3'
                }
            }
        }
    ];
    
    for (const testCase of testCases) {
        console.log(`📤 测试${testCase.name}:`, testCase.message);
        
        try {
            const response = await new Promise((resolve) => {
                chrome.runtime.sendMessage(testCase.message, resolve);
            });
            
            console.log(`📥 ${testCase.name}响应:`, response);
            
            if (response && response.success) {
                console.log(`✅ ${testCase.name}成功`);
            } else {
                console.log(`❌ ${testCase.name}失败:`, response?.error);
            }
            
        } catch (error) {
            console.error(`❌ ${testCase.name}出错:`, error);
        }
        
        // 等待一下再测试下一个
        await new Promise(resolve => setTimeout(resolve, 500));
    }
}

// 7. 完整的深度调试
async function fullDeepDebug() {
    console.log('🚀 开始完整深度调试...\n');
    
    try {
        // 1. 检查面板消息发送
        console.log('1️⃣ 检查面板消息发送...');
        debugPanelMessageSending();
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 2. 手动模拟消息发送
        console.log('2️⃣ 手动模拟消息发送...');
        manualSimulatePanelMessage();
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 3. 检查content script接收
        console.log('3️⃣ 检查content script接收...');
        checkContentScriptReceiving();
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 4. 直接测试chrome消息
        console.log('4️⃣ 直接测试chrome消息...');
        await testDirectChromeMessage();
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 5. 检查background接收
        console.log('5️⃣ 检查background接收...');
        await inspectBackgroundReceiving();
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 6. 测试不同消息格式
        console.log('6️⃣ 测试不同消息格式...');
        await testDifferentMessageFormats();
        
        console.log('\n🎯 深度调试完成！');
        console.log('📊 请查看上面的详细日志来定位问题');
        
    } catch (error) {
        console.error('❌ 深度调试过程中出现错误:', error);
    }
}

// 8. 快速问题定位
async function quickProblemLocation() {
    console.log('\n⚡ 快速问题定位...');
    
    // 直接测试最简单的情况
    const simpleRequest = {
        type: 'START_YUNPAN_UPLOAD',
        data: {
            files: [{ name: 'quick.txt' }]
        }
    };
    
    console.log('📤 发送简单请求:', simpleRequest);
    
    const response = await new Promise((resolve) => {
        chrome.runtime.sendMessage(simpleRequest, resolve);
    });
    
    console.log('📥 简单响应:', response);
    
    if (response && response.error === "上传请求数据为空") {
        console.log('🎯 问题确认: background script无法读取request.data');
        console.log('💡 建议: 检查background.js中handleStartYunpanUpload函数的参数解析');
    }
}

// 导出函数
window.debugPanelMessageSending = debugPanelMessageSending;
window.manualSimulatePanelMessage = manualSimulatePanelMessage;
window.checkContentScriptReceiving = checkContentScriptReceiving;
window.testDirectChromeMessage = testDirectChromeMessage;
window.inspectBackgroundReceiving = inspectBackgroundReceiving;
window.testDifferentMessageFormats = testDifferentMessageFormats;
window.fullDeepDebug = fullDeepDebug;
window.quickProblemLocation = quickProblemLocation;

console.log('✅ 深度调试脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - fullDeepDebug() - 完整深度调试');
console.log('  - quickProblemLocation() - 快速问题定位');
console.log('  - testDirectChromeMessage() - 直接测试chrome消息');
console.log('  - testDifferentMessageFormats() - 测试不同消息格式');

// 自动运行快速问题定位
quickProblemLocation();

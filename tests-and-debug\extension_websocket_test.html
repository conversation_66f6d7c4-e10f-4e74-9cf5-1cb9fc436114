<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扩展WebSocket测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: scroll;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 扩展WebSocket连接测试</h1>
    
    <div class="test-container">
        <h3>📡 直接WebSocket测试</h3>
        <p>测试从普通网页直接连接到WebSocket服务器</p>
        <button onclick="testDirectConnection()">测试直接连接</button>
        <div id="directStatus" class="status info">等待测试</div>
    </div>

    <div class="test-container">
        <h3>🔌 通过扩展API测试</h3>
        <p>测试通过Chrome扩展API调用background script</p>
        <button onclick="testExtensionConnection()">测试扩展连接</button>
        <button onclick="testExtensionStatus()">获取扩展状态</button>
        <div id="extensionStatus" class="status info">等待测试</div>
    </div>

    <div class="test-container">
        <h3>🛠️ 扩展重置工具</h3>
        <p>完全重置扩展状态</p>
        <button onclick="resetExtension()">重置扩展</button>
        <button onclick="clearExtensionStorage()">清除扩展存储</button>
        <div id="resetStatus" class="status info">等待操作</div>
    </div>

    <div class="test-container">
        <h3>📋 测试日志</h3>
        <div id="testLog" class="log"></div>
        <button onclick="clearLog()">清除日志</button>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('testLog');
            const entry = document.createElement('div');
            entry.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#007bff';
            entry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[测试] ${message}`);
        }

        // 测试直接WebSocket连接
        function testDirectConnection() {
            log('开始直接WebSocket连接测试...');
            const status = document.getElementById('directStatus');
            status.className = 'status info';
            status.textContent = '连接中...';

            try {
                const ws = new WebSocket('ws://***************:6656');
                
                const timeout = setTimeout(() => {
                    ws.close();
                    status.className = 'status error';
                    status.textContent = '连接超时';
                    log('直接连接超时', 'error');
                }, 5000);

                ws.onopen = () => {
                    clearTimeout(timeout);
                    status.className = 'status success';
                    status.textContent = '✅ 直接连接成功';
                    log('直接WebSocket连接成功！', 'success');
                    
                    // 发送测试消息
                    ws.send(JSON.stringify({ type: 'ping', from: 'direct-test' }));
                };

                ws.onmessage = (event) => {
                    log(`收到消息: ${event.data}`, 'success');
                };

                ws.onclose = (event) => {
                    log(`连接关闭: code=${event.code}, reason="${event.reason}"`, 'info');
                };

                ws.onerror = (error) => {
                    clearTimeout(timeout);
                    status.className = 'status error';
                    status.textContent = '❌ 直接连接失败';
                    log(`直接连接错误: ${error}`, 'error');
                };

            } catch (error) {
                status.className = 'status error';
                status.textContent = '❌ 连接异常';
                log(`直接连接异常: ${error.message}`, 'error');
            }
        }

        // 测试扩展连接
        function testExtensionConnection() {
            log('开始扩展连接测试...');
            const status = document.getElementById('extensionStatus');
            status.className = 'status info';
            status.textContent = '测试中...';

            try {
                chrome.runtime.sendMessage({ 
                    type: 'GET_CONNECTION_STATUS' 
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        status.className = 'status error';
                        status.textContent = '❌ 扩展通信失败';
                        log(`扩展通信错误: ${chrome.runtime.lastError.message}`, 'error');
                        return;
                    }

                    if (response) {
                        if (response.connected) {
                            status.className = 'status success';
                            status.textContent = '✅ 扩展已连接';
                            log('扩展WebSocket连接正常', 'success');
                        } else {
                            status.className = 'status error';
                            status.textContent = '❌ 扩展未连接';
                            log('扩展WebSocket未连接', 'error');
                        }
                    } else {
                        status.className = 'status error';
                        status.textContent = '❌ 无响应';
                        log('扩展无响应', 'error');
                    }
                });
            } catch (error) {
                status.className = 'status error';
                status.textContent = '❌ 测试异常';
                log(`扩展测试异常: ${error.message}`, 'error');
            }
        }

        // 获取扩展状态
        function testExtensionStatus() {
            log('获取扩展详细状态...');
            
            chrome.runtime.sendMessage({ 
                type: 'SEND_WS_MESSAGE',
                wsMessage: { type: 'ping' }
            }, (response) => {
                if (chrome.runtime.lastError) {
                    log(`扩展通信错误: ${chrome.runtime.lastError.message}`, 'error');
                    return;
                }
                
                if (response && response.success) {
                    log('扩展ping测试成功', 'success');
                } else {
                    log('扩展ping测试失败', 'error');
                }
            });
        }

        // 重置扩展
        function resetExtension() {
            log('重置扩展状态...');
            const status = document.getElementById('resetStatus');
            
            chrome.runtime.sendMessage({ 
                type: 'RESET_CONNECTION' 
            }, (response) => {
                if (chrome.runtime.lastError) {
                    status.className = 'status error';
                    status.textContent = '❌ 重置失败';
                    log(`扩展重置失败: ${chrome.runtime.lastError.message}`, 'error');
                    return;
                }
                
                status.className = 'status success';
                status.textContent = '✅ 重置完成';
                log('扩展已重置', 'success');
            });
        }

        // 清除扩展存储
        function clearExtensionStorage() {
            log('清除扩展存储...');
            
            chrome.storage.local.clear(() => {
                if (chrome.runtime.lastError) {
                    log(`清除存储失败: ${chrome.runtime.lastError.message}`, 'error');
                } else {
                    log('扩展存储已清除', 'success');
                }
            });
        }

        // 清除日志
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
            log('日志已清除');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('WebSocket扩展测试工具已加载');
            
            // 检查是否在扩展环境中
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                log('检测到Chrome扩展环境', 'success');
            } else {
                log('非扩展环境，某些功能可能不可用', 'error');
            }
        });
    </script>
</body>
</html> 
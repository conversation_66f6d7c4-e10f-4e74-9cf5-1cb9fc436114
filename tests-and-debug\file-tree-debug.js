// 文件树数据调试脚本

console.log('🌳 开始文件树数据调试...');

// 1. 检查原始数据
function checkRawData() {
    console.log('\n=== 检查原始数据 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return null;
    }
    
    const panel = window.ylzInjectedPanel;
    
    // 检查fileData
    if (panel.fileData) {
        console.log('📊 fileData 存在:');
        console.log('  pending:', panel.fileData.pending?.length || 0);
        console.log('  uploaded:', panel.fileData.uploaded?.length || 0);
        
        if (panel.fileData.pending?.length > 0) {
            console.log('  第一个pending文件:', panel.fileData.pending[0]);
        }
        if (panel.fileData.uploaded?.length > 0) {
            console.log('  第一个uploaded文件:', panel.fileData.uploaded[0]);
        }
    } else {
        console.log('❌ fileData 不存在');
    }
    
    return panel.fileData;
}

// 2. 检查容器元素
function checkContainers() {
    console.log('\n=== 检查容器元素 ===');
    
    const containers = [
        { id: 'ylz-pendingTreeContainer', name: '未上传容器' },
        { id: 'ylz-uploadedTreeContainer', name: '已上传容器' }
    ];
    
    containers.forEach(container => {
        const element = document.getElementById(container.id);
        if (element) {
            console.log(`✅ ${container.name} 存在:`);
            console.log(`  innerHTML长度: ${element.innerHTML.length}`);
            console.log(`  子元素数量: ${element.children.length}`);
            console.log(`  内容预览: ${element.textContent.substring(0, 100)}...`);
            
            // 检查是否有文件项
            const items = element.querySelectorAll('[style*="border-bottom"]');
            console.log(`  文件项数量: ${items.length}`);
            
            if (items.length === 0 && element.textContent.includes('暂无文件')) {
                console.log('  ⚠️ 显示"暂无文件"状态');
            }
        } else {
            console.log(`❌ ${container.name} 不存在`);
        }
    });
}

// 3. 检查树实例
function checkTreeInstances() {
    console.log('\n=== 检查树实例 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return;
    }
    
    const panel = window.ylzInjectedPanel;
    
    if (panel.treeTrees) {
        console.log('✅ treeTrees 对象存在');
        
        ['pending', 'uploaded'].forEach(type => {
            if (panel.treeTrees[type]) {
                console.log(`✅ ${type} 树实例存在`);
                
                const instance = panel.treeTrees[type];
                console.log(`  容器: ${instance.container ? '存在' : '不存在'}`);
                console.log(`  选项: ${JSON.stringify(instance.options)}`);
                
                // 检查方法
                const methods = ['render', 'refresh', 'formatFileSize'];
                methods.forEach(method => {
                    console.log(`  ${method}: ${typeof instance[method] === 'function' ? '✅' : '❌'}`);
                });
            } else {
                console.log(`❌ ${type} 树实例不存在`);
            }
        });
    } else {
        console.log('❌ treeTrees 对象不存在');
    }
}

// 4. 手动触发数据渲染
function manualRender() {
    console.log('\n=== 手动触发数据渲染 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return;
    }
    
    const panel = window.ylzInjectedPanel;
    
    // 获取最新数据
    panel.safeRuntimeMessage({ type: 'GET_ADMIN_TREE' })
        .then(response => {
            if (response?.success && response.tree) {
                console.log('✅ 获取到最新文件树数据');
                console.log('📊 数据结构:', response.tree);
                
                // 手动更新文件树
                panel.updateFileTree(response.tree);
                
                setTimeout(() => {
                    console.log('🔄 渲染完成，检查结果...');
                    checkContainers();
                }, 2000);
            } else {
                console.log('❌ 获取文件树数据失败');
            }
        })
        .catch(error => {
            console.error('❌ 获取数据出错:', error);
        });
}

// 5. 创建测试数据并渲染
function testWithMockData() {
    console.log('\n=== 使用测试数据渲染 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return;
    }
    
    const panel = window.ylzInjectedPanel;
    
    // 创建测试数据
    const mockData = {
        files: [
            { name: '测试文档1.pdf', type: 'file', size: 1024000, status: '未同步' },
            { name: '测试文档2.docx', type: 'file', size: 512000, status: '已同步' },
            { name: '图片文件.jpg', type: 'file', size: 256000, status: '未同步' }
        ],
        folders: [
            { 
                name: '测试文件夹', 
                type: 'folder',
                files: [
                    { name: '子文件1.txt', type: 'file', size: 1024, status: '未同步' },
                    { name: '子文件2.txt', type: 'file', size: 2048, status: '已同步' }
                ]
            }
        ]
    };
    
    console.log('🧪 使用测试数据:', mockData);
    
    // 更新文件树
    panel.updateFileTree(mockData);
    
    setTimeout(() => {
        console.log('🔄 测试渲染完成，检查结果...');
        checkContainers();
    }, 2000);
}

// 6. 重新初始化树组件
function reinitializeTrees() {
    console.log('\n=== 重新初始化树组件 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return;
    }
    
    const panel = window.ylzInjectedPanel;
    
    // 清除现有树实例
    panel.treeTrees = {};
    
    // 重新创建树组件
    ['pending', 'uploaded'].forEach(async (type) => {
        const containerId = `ylz-${type}TreeContainer`;
        const container = document.getElementById(containerId);
        
        if (container && window.InjectedPanelTree) {
            try {
                console.log(`🔄 重新创建 ${type} 树实例...`);
                
                panel.treeTrees[type] = new window.InjectedPanelTree(container, {
                    showThumbnails: true,
                    allowSelection: true,
                    expandable: true
                });
                
                console.log(`✅ ${type} 树实例创建成功`);
                
                // 如果有数据，立即渲染
                if (panel.fileData) {
                    const files = type === 'pending' ? panel.fileData.pending : panel.fileData.uploaded;
                    if (files && files.length > 0) {
                        console.log(`🎨 渲染 ${type} 数据，文件数量: ${files.length}`);
                        panel.treeTrees[type].render(files);
                    }
                }
                
            } catch (error) {
                console.error(`❌ 创建 ${type} 树实例失败:`, error);
            }
        }
    });
    
    setTimeout(() => {
        console.log('🔄 重新初始化完成，检查结果...');
        checkTreeInstances();
        checkContainers();
    }, 3000);
}

// 7. 完整调试流程
function fullDebug() {
    console.log('🚀 开始完整文件树调试...\n');
    
    // 按顺序执行检查
    const rawData = checkRawData();
    checkContainers();
    checkTreeInstances();
    
    // 如果有数据但没有正确显示，尝试修复
    if (rawData && (rawData.pending?.length > 0 || rawData.uploaded?.length > 0)) {
        const containers = document.querySelectorAll('#ylz-pendingTreeContainer, #ylz-uploadedTreeContainer');
        let hasEmptyContainers = false;
        
        containers.forEach(container => {
            if (container.textContent.includes('暂无文件') || container.children.length === 0) {
                hasEmptyContainers = true;
            }
        });
        
        if (hasEmptyContainers) {
            console.log('\n⚠️ 发现数据存在但显示为空的情况，尝试修复...');
            
            setTimeout(() => {
                reinitializeTrees();
            }, 1000);
            
            setTimeout(() => {
                manualRender();
            }, 3000);
        } else {
            console.log('\n✅ 数据显示正常');
        }
    } else {
        console.log('\n🔄 没有数据，尝试获取最新数据...');
        setTimeout(() => {
            manualRender();
        }, 1000);
    }
}

// 导出函数
window.fileTreeDebug = fullDebug;
window.checkRawData = checkRawData;
window.checkContainers = checkContainers;
window.checkTreeInstances = checkTreeInstances;
window.manualRender = manualRender;
window.testWithMockData = testWithMockData;
window.reinitializeTrees = reinitializeTrees;

console.log('✅ 文件树调试脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - fileTreeDebug() - 运行完整调试');
console.log('  - manualRender() - 手动触发渲染');
console.log('  - reinitializeTrees() - 重新初始化树组件');
console.log('  - testWithMockData() - 使用测试数据');

// 自动运行调试
fullDebug();

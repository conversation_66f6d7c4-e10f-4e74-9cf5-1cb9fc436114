// 修复验证测试脚本

console.log('🔧 开始验证修复...');

// 1. 测试获取允许刷新当前页面状态
async function testGetAllowActiveRefresh() {
    console.log('\n=== 测试获取允许刷新当前页面状态 ===');
    
    return new Promise((resolve) => {
        chrome.runtime.sendMessage({ type: 'GET_ALLOW_ACTIVE_REFRESH' }, (response) => {
            if (response && response.success !== undefined) {
                console.log('✅ 获取状态成功:');
                console.log(`  允许刷新当前页面: ${response.allowed ? '是' : '否'}`);
                resolve(response);
            } else {
                console.log('❌ 获取状态失败:', response);
                resolve(null);
            }
        });
    });
}

// 2. 测试设置允许刷新当前页面状态
async function testSetAllowActiveRefresh(allowed) {
    console.log(`\n=== 测试设置允许刷新当前页面: ${allowed ? '启用' : '禁用'} ===`);
    
    return new Promise((resolve) => {
        chrome.runtime.sendMessage({ 
            type: 'SET_ALLOW_ACTIVE_REFRESH', 
            allowed: allowed 
        }, (response) => {
            if (response && response.success !== undefined) {
                console.log('✅ 设置成功:');
                console.log(`  状态: ${response.allowed ? '已启用' : '已禁用'}`);
                console.log(`  消息: ${response.message}`);
                resolve(response);
            } else {
                console.log('❌ 设置失败:', response);
                resolve(null);
            }
        });
    });
}

// 3. 测试面板UI是否正常工作
function testPanelUI() {
    console.log('\n=== 测试面板UI ===');
    
    const panel = document.getElementById('ylz-injected-panel');
    if (!panel) {
        console.log('❌ 面板不存在');
        return false;
    }
    
    const checkbox = panel.querySelector('#ylz-allowActiveRefresh');
    if (!checkbox) {
        console.log('❌ 勾选框不存在');
        return false;
    }
    
    console.log('✅ UI元素检查通过:');
    console.log(`  勾选框存在: 是`);
    console.log(`  当前状态: ${checkbox.checked ? '已勾选' : '未勾选'}`);
    
    return true;
}

// 4. 测试面板方法是否可用
function testPanelMethods() {
    console.log('\n=== 测试面板方法 ===');
    
    if (window.ylzInjectedPanel && typeof window.ylzInjectedPanel.setAllowActiveRefresh === 'function') {
        console.log('✅ 面板方法可用');
        return true;
    } else {
        console.log('❌ 面板方法不可用');
        return false;
    }
}

// 5. 完整的修复验证测试
async function fullFixVerificationTest() {
    console.log('🚀 开始完整修复验证测试...\n');
    
    try {
        // 1. 测试UI
        const uiOK = testPanelUI();
        
        // 2. 测试面板方法
        const methodsOK = testPanelMethods();
        
        // 3. 测试后台通信 - 获取状态
        const getResult = await testGetAllowActiveRefresh();
        
        // 4. 测试后台通信 - 设置为启用
        const setTrueResult = await testSetAllowActiveRefresh(true);
        
        // 等待一秒
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 5. 验证设置是否生效
        const verifyTrueResult = await testGetAllowActiveRefresh();
        
        // 6. 测试后台通信 - 设置为禁用
        const setFalseResult = await testSetAllowActiveRefresh(false);
        
        // 等待一秒
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 7. 验证设置是否生效
        const verifyFalseResult = await testGetAllowActiveRefresh();
        
        // 8. 生成测试报告
        console.log('\n📋 === 修复验证报告 ===');
        console.log(`✅ UI元素: ${uiOK ? '正常' : '异常'}`);
        console.log(`✅ 面板方法: ${methodsOK ? '正常' : '异常'}`);
        console.log(`✅ 获取状态: ${getResult ? '正常' : '异常'}`);
        console.log(`✅ 设置启用: ${setTrueResult ? '正常' : '异常'}`);
        console.log(`✅ 验证启用: ${verifyTrueResult?.allowed === true ? '正常' : '异常'}`);
        console.log(`✅ 设置禁用: ${setFalseResult ? '正常' : '异常'}`);
        console.log(`✅ 验证禁用: ${verifyFalseResult?.allowed === false ? '正常' : '异常'}`);
        
        const allOK = uiOK && methodsOK && getResult && setTrueResult && 
                     verifyTrueResult?.allowed === true && setFalseResult && 
                     verifyFalseResult?.allowed === false;
        
        if (allOK) {
            console.log('\n🎉 修复验证通过！所有功能正常工作');
            console.log('💡 现在可以正常使用勾选框控制是否刷新当前页面了');
        } else {
            console.log('\n⚠️ 修复验证失败，仍有问题需要解决');
        }
        
        return allOK;
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
        return false;
    }
}

// 6. 快速UI测试
function quickUITest() {
    console.log('\n⚡ 快速UI测试...');
    
    const checkbox = document.querySelector('#ylz-allowActiveRefresh');
    if (!checkbox) {
        console.log('❌ 勾选框不存在');
        return;
    }
    
    const originalState = checkbox.checked;
    console.log(`原始状态: ${originalState ? '已勾选' : '未勾选'}`);
    
    // 模拟点击
    console.log('🧪 模拟点击勾选框...');
    checkbox.click();
    
    setTimeout(() => {
        const newState = checkbox.checked;
        console.log(`点击后状态: ${newState ? '已勾选' : '未勾选'}`);
        
        if (newState !== originalState) {
            console.log('✅ UI交互正常');
            
            // 恢复原状态
            setTimeout(() => {
                console.log('🔄 恢复原状态...');
                checkbox.click();
            }, 2000);
        } else {
            console.log('❌ UI交互异常');
        }
    }, 1000);
}

// 7. 检查错误是否修复
function checkErrorFixed() {
    console.log('\n🔍 检查错误是否修复...');
    
    // 检查是否还有chrome.storage.local相关的错误
    const originalError = console.error;
    let hasStorageError = false;
    
    console.error = function(...args) {
        const message = args.join(' ');
        if (message.includes('chrome.storage') || message.includes('Cannot read properties of undefined')) {
            hasStorageError = true;
            console.log('❌ 仍有存储相关错误:', message);
        }
        originalError.apply(console, args);
    };
    
    // 触发一次设置操作
    const checkbox = document.querySelector('#ylz-allowActiveRefresh');
    if (checkbox) {
        checkbox.click();
        
        setTimeout(() => {
            console.error = originalError;
            
            if (!hasStorageError) {
                console.log('✅ 存储错误已修复');
            } else {
                console.log('❌ 存储错误仍然存在');
            }
        }, 2000);
    } else {
        console.error = originalError;
        console.log('❌ 无法测试，勾选框不存在');
    }
}

// 导出函数
window.testGetAllowActiveRefresh = testGetAllowActiveRefresh;
window.testSetAllowActiveRefresh = testSetAllowActiveRefresh;
window.testPanelUI = testPanelUI;
window.testPanelMethods = testPanelMethods;
window.fullFixVerificationTest = fullFixVerificationTest;
window.quickUITest = quickUITest;
window.checkErrorFixed = checkErrorFixed;

console.log('✅ 修复验证脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - fullFixVerificationTest() - 完整修复验证');
console.log('  - quickUITest() - 快速UI测试');
console.log('  - checkErrorFixed() - 检查错误是否修复');

// 自动运行完整验证
fullFixVerificationTest();

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整面板测试 - yunpan.gdcourts.gov.cn:82</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .test-panel {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .test-button.success {
            background: #28a745;
        }
        
        .test-button.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .test-button.danger {
            background: #dc3545;
        }
        
        .log-area {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .status-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-info { background: #17a2b8; }
        
        .mock-content {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .file-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .file-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 完整版注入面板测试</h1>
            <p>测试完整功能的文件云流转助手面板</p>
        </div>
        
        <div class="status-grid">
            <div class="status-item">
                <h4>面板状态</h4>
                <div id="panelStatus">
                    <span class="status-indicator status-warning"></span>
                    <span>检测中...</span>
                </div>
            </div>
            <div class="status-item">
                <h4>连接状态</h4>
                <div id="connectionStatus">
                    <span class="status-indicator status-warning"></span>
                    <span>检测中...</span>
                </div>
            </div>
            <div class="status-item">
                <h4>文件树状态</h4>
                <div id="treeStatus">
                    <span class="status-indicator status-warning"></span>
                    <span>检测中...</span>
                </div>
            </div>
            <div class="status-item">
                <h4>功能状态</h4>
                <div id="functionStatus">
                    <span class="status-indicator status-warning"></span>
                    <span>检测中...</span>
                </div>
            </div>
        </div>
        
        <div class="test-panel">
            <h3>🔍 面板检测</h3>
            <button class="test-button" onclick="checkFullPanel()">检查完整面板</button>
            <button class="test-button" onclick="testPanelFeatures()">测试面板功能</button>
            <button class="test-button" onclick="testFileTree()">测试文件树</button>
            <button class="test-button" onclick="testUploadFeature()">测试上传功能</button>
        </div>
        
        <div class="test-panel">
            <h3>🎛️ 面板控制</h3>
            <button class="test-button" onclick="showPanel()">显示面板</button>
            <button class="test-button" onclick="hidePanel()">隐藏面板</button>
            <button class="test-button" onclick="minimizePanel()">最小化面板</button>
            <button class="test-button" onclick="resetPanelPosition()">重置位置</button>
        </div>
        
        <div class="test-panel">
            <h3>📡 通信测试</h3>
            <button class="test-button" onclick="testBackgroundComm()">测试Background通信</button>
            <button class="test-button" onclick="testWebSocketConn()">测试WebSocket连接</button>
            <button class="test-button" onclick="refreshFileData()">刷新文件数据</button>
            <button class="test-button danger" onclick="clearAllLogs()">清除所有日志</button>
        </div>
        
        <div class="test-panel">
            <h3>📝 测试日志</h3>
            <div class="log-area" id="logArea">等待测试开始...\n</div>
        </div>
        
        <div class="mock-content">
            <h3>📁 模拟云盘环境</h3>
            <p>这里模拟云盘页面内容，完整版面板应该出现在页面右上角，包含文件树和上传功能。</p>
            
            <div class="file-grid">
                <div class="file-item">
                    <div style="font-size: 2em; margin-bottom: 10px;">📄</div>
                    <div>重要报告.pdf</div>
                    <div style="font-size: 11px; color: #ccc; margin-top: 5px;">2.5MB</div>
                </div>
                <div class="file-item">
                    <div style="font-size: 2em; margin-bottom: 10px;">📊</div>
                    <div>数据分析.xlsx</div>
                    <div style="font-size: 11px; color: #ccc; margin-top: 5px;">1.8MB</div>
                </div>
                <div class="file-item">
                    <div style="font-size: 2em; margin-bottom: 10px;">🖼️</div>
                    <div>截图.png</div>
                    <div style="font-size: 11px; color: #ccc; margin-top: 5px;">856KB</div>
                </div>
                <div class="file-item">
                    <div style="font-size: 2em; margin-bottom: 10px;">📝</div>
                    <div>会议纪要.docx</div>
                    <div style="font-size: 11px; color: #ccc; margin-top: 5px;">1.2MB</div>
                </div>
                <div class="file-item">
                    <div style="font-size: 2em; margin-bottom: 10px;">🎬</div>
                    <div>演示视频.mp4</div>
                    <div style="font-size: 11px; color: #ccc; margin-top: 5px;">15.6MB</div>
                </div>
                <div class="file-item">
                    <div style="font-size: 2em; margin-bottom: 10px;">📦</div>
                    <div>资料包.zip</div>
                    <div style="font-size: 11px; color: #ccc; margin-top: 5px;">8.9MB</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let logArea = document.getElementById('logArea');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logArea.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            if (element) {
                const indicator = element.querySelector('.status-indicator');
                const text = element.querySelector('span:last-child');
                
                indicator.className = `status-indicator status-${status}`;
                text.textContent = message;
            }
        }
        
        function checkFullPanel() {
            log('开始检查完整面板...');
            
            const panel = document.getElementById('ylz-injected-panel');
            if (panel) {
                log('✅ 找到完整面板元素', 'success');
                updateStatus('panelStatus', 'success', '面板已加载');
                
                // 检查面板组件
                const components = [
                    { id: 'ylz-connectionStatusDot', name: '连接状态指示器' },
                    { id: 'ylz-pendingTab', name: '未上传标签页' },
                    { id: 'ylz-uploadedTab', name: '已上传标签页' },
                    { id: 'ylz-uploadAll', name: '上传按钮' },
                    { id: 'ylz-refreshList', name: '刷新按钮' },
                    { id: 'ylz-pendingTreeContainer', name: '文件树容器' }
                ];
                
                components.forEach(comp => {
                    const element = panel.querySelector(`#${comp.id}`);
                    log(`${comp.name}: ${element ? '✅' : '❌'}`);
                });
                
                // 检查全局实例
                if (window.ylzInjectedPanel) {
                    log('✅ 找到面板实例', 'success');
                    updateStatus('functionStatus', 'success', '功能正常');
                } else {
                    log('❌ 未找到面板实例', 'error');
                    updateStatus('functionStatus', 'error', '实例缺失');
                }
            } else {
                log('❌ 未找到完整面板元素', 'error');
                updateStatus('panelStatus', 'error', '面板未加载');
            }
        }
        
        function testPanelFeatures() {
            log('测试面板功能...');
            
            const panel = document.getElementById('ylz-injected-panel');
            if (!panel) {
                log('❌ 面板不存在，无法测试', 'error');
                return;
            }
            
            // 测试按钮
            const buttons = [
                { id: 'ylz-minimize-btn', name: '最小化按钮' },
                { id: 'ylz-close-btn', name: '关闭按钮' },
                { id: 'ylz-uploadAll', name: '上传按钮' },
                { id: 'ylz-refreshList', name: '刷新按钮' }
            ];
            
            buttons.forEach(btn => {
                const element = panel.querySelector(`#${btn.id}`);
                if (element) {
                    log(`✅ ${btn.name}存在且可点击`);
                } else {
                    log(`❌ ${btn.name}不存在`, 'error');
                }
            });
        }
        
        function testFileTree() {
            log('测试文件树功能...');
            
            if (chrome && chrome.runtime) {
                chrome.runtime.sendMessage({ type: 'GET_ADMIN_TREE' }, (response) => {
                    if (chrome.runtime.lastError) {
                        log(`❌ 文件树获取失败: ${chrome.runtime.lastError.message}`, 'error');
                        updateStatus('treeStatus', 'error', '获取失败');
                    } else {
                        log(`✅ 文件树数据: ${response?.success ? '获取成功' : '获取失败'}`, response?.success ? 'success' : 'error');
                        updateStatus('treeStatus', response?.success ? 'success' : 'error', response?.success ? '数据正常' : '数据异常');
                        
                        if (response?.tree) {
                            log(`📊 文件树大小: ${JSON.stringify(response.tree).length} 字符`);
                        }
                    }
                });
            } else {
                log('❌ Chrome扩展API不可用', 'error');
            }
        }
        
        function testUploadFeature() {
            log('测试上传功能...');
            
            if (chrome && chrome.runtime) {
                chrome.runtime.sendMessage({ type: 'START_YUNPAN_UPLOAD' }, (response) => {
                    if (chrome.runtime.lastError) {
                        log(`❌ 上传测试失败: ${chrome.runtime.lastError.message}`, 'error');
                    } else {
                        log(`✅ 上传功能${response?.success ? '正常' : '异常'}`, response?.success ? 'success' : 'error');
                    }
                });
            } else {
                log('❌ Chrome扩展API不可用', 'error');
            }
        }
        
        function showPanel() {
            const panel = document.getElementById('ylz-injected-panel');
            if (panel) {
                panel.style.display = 'block';
                panel.classList.remove('hidden');
                log('✅ 面板已显示');
            } else {
                log('❌ 面板不存在', 'error');
            }
        }
        
        function hidePanel() {
            const panel = document.getElementById('ylz-injected-panel');
            if (panel) {
                panel.style.display = 'none';
                log('✅ 面板已隐藏');
            } else {
                log('❌ 面板不存在', 'error');
            }
        }
        
        function minimizePanel() {
            const panel = document.getElementById('ylz-injected-panel');
            if (panel) {
                const minimizeBtn = panel.querySelector('#ylz-minimize-btn');
                if (minimizeBtn) {
                    minimizeBtn.click();
                    log('✅ 触发最小化');
                } else {
                    log('❌ 最小化按钮不存在', 'error');
                }
            } else {
                log('❌ 面板不存在', 'error');
            }
        }
        
        function resetPanelPosition() {
            const panel = document.getElementById('ylz-injected-panel');
            if (panel) {
                panel.style.top = '20px';
                panel.style.right = '20px';
                panel.style.left = 'auto';
                panel.style.bottom = 'auto';
                log('✅ 面板位置已重置');
            } else {
                log('❌ 面板不存在', 'error');
            }
        }
        
        function testBackgroundComm() {
            log('测试Background通信...');
            
            if (chrome && chrome.runtime) {
                chrome.runtime.sendMessage({ type: 'GET_CONNECTION_STATUS' }, (response) => {
                    if (chrome.runtime.lastError) {
                        log(`❌ 通信失败: ${chrome.runtime.lastError.message}`, 'error');
                        updateStatus('connectionStatus', 'error', '通信失败');
                    } else {
                        const connected = response?.connected || false;
                        log(`✅ 连接状态: ${connected ? '已连接' : '未连接'}`, connected ? 'success' : 'warning');
                        updateStatus('connectionStatus', connected ? 'success' : 'warning', connected ? '已连接' : '未连接');
                    }
                });
            } else {
                log('❌ Chrome扩展API不可用', 'error');
                updateStatus('connectionStatus', 'error', 'API不可用');
            }
        }
        
        function testWebSocketConn() {
            log('测试WebSocket连接...');
            testBackgroundComm(); // WebSocket状态通过background获取
        }
        
        function refreshFileData() {
            log('刷新文件数据...');
            
            if (chrome && chrome.runtime) {
                chrome.runtime.sendMessage({ 
                    type: 'SEND_WS_MESSAGE', 
                    wsMessage: { type: 'get_full_admin_file_tree' }
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        log(`❌ 刷新失败: ${chrome.runtime.lastError.message}`, 'error');
                    } else {
                        log('✅ 刷新请求已发送', 'success');
                    }
                });
            } else {
                log('❌ Chrome扩展API不可用', 'error');
            }
        }
        
        function clearAllLogs() {
            logArea.textContent = '日志已清除\n';
            log('系统重新开始记录');
        }
        
        // 页面加载完成后自动检查
        window.addEventListener('load', () => {
            log('页面加载完成，开始自动检测...');
            log(`当前URL: ${window.location.href}`);
            
            // 延迟检查，等待面板注入
            setTimeout(() => {
                checkFullPanel();
                testBackgroundComm();
            }, 3000);
            
            // 定期检查面板状态
            setInterval(() => {
                const panel = document.getElementById('ylz-injected-panel');
                if (panel) {
                    updateStatus('panelStatus', 'success', '运行中');
                } else {
                    updateStatus('panelStatus', 'error', '未加载');
                }
            }, 5000);
        });
        
        // 监听面板注入事件
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.id === 'ylz-injected-panel') {
                        log('🎉 检测到完整面板注入!', 'success');
                        setTimeout(() => {
                            checkFullPanel();
                        }, 1000);
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注入面板测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .test-button.secondary {
            background: #6c757d;
        }
        
        .test-button.secondary:hover {
            background: #545b62;
        }
        
        .test-result {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.success {
            background: #28a745;
        }
        
        .status-indicator.error {
            background: #dc3545;
        }
        
        .status-indicator.warning {
            background: #ffc107;
        }
        
        .info-box {
            background: rgba(255, 255, 255, 0.15);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        
        .page-content {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            min-height: 300px;
        }
        
        .mock-yunpan-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .mock-file-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .mock-file-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .file-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 注入面板测试页面</h1>
        
        <div class="info-box">
            <h3>📋 测试说明</h3>
            <p>这个页面用于测试注入面板的功能。请确保：</p>
            <ul>
                <li>已安装文件云流转助手扩展</li>
                <li>扩展已启用并有必要权限</li>
                <li>当前URL包含 "yunpan.gdcourts.gov.cn:82"（可通过修改URL模拟）</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🔍 面板状态检测</h3>
            <button class="test-button" onclick="checkPanelStatus()">检查面板状态</button>
            <button class="test-button secondary" onclick="togglePanel()">切换面板显示</button>
            <button class="test-button secondary" onclick="reinitializePanel()">重新初始化</button>
            <div class="test-result" id="panelStatusResult">点击按钮开始测试...</div>
        </div>
        
        <div class="test-section">
            <h3>🎛️ 面板控制测试</h3>
            <button class="test-button" onclick="testMinimize()">测试最小化</button>
            <button class="test-button" onclick="testDrag()">测试拖拽</button>
            <button class="test-button" onclick="testClose()">测试关闭</button>
            <button class="test-button secondary" onclick="testRestore()">恢复面板</button>
            <div class="test-result" id="controlTestResult">等待测试...</div>
        </div>
        
        <div class="test-section">
            <h3>📡 通信测试</h3>
            <button class="test-button" onclick="testConnection()">测试连接状态</button>
            <button class="test-button" onclick="testFileTree()">测试文件树</button>
            <button class="test-button" onclick="testUpload()">测试上传功能</button>
            <div class="test-result" id="communicationResult">等待测试...</div>
        </div>
        
        <div class="test-section">
            <h3>🐛 调试信息</h3>
            <button class="test-button secondary" onclick="showDebugInfo()">显示调试信息</button>
            <button class="test-button secondary" onclick="clearLogs()">清除日志</button>
            <div class="test-result" id="debugInfo">调试信息将显示在这里...</div>
        </div>
        
        <!-- 模拟云盘内容 -->
        <div class="page-content">
            <h3>📁 模拟云盘内容</h3>
            <p>这里模拟云盘页面的内容，注入面板应该出现在页面右上角。</p>
            
            <div class="mock-yunpan-content">
                <div class="mock-file-item">
                    <div class="file-icon">📄</div>
                    <div>报告.pdf</div>
                </div>
                <div class="mock-file-item">
                    <div class="file-icon">📊</div>
                    <div>数据.xlsx</div>
                </div>
                <div class="mock-file-item">
                    <div class="file-icon">🖼️</div>
                    <div>图片.jpg</div>
                </div>
                <div class="mock-file-item">
                    <div class="file-icon">📝</div>
                    <div>文档.docx</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 测试函数
        function checkPanelStatus() {
            const result = document.getElementById('panelStatusResult');
            result.innerHTML = '正在检查面板状态...\n';
            
            // 检查面板是否存在
            const panel = document.getElementById('ylz-injected-panel');
            if (panel) {
                result.innerHTML += '✅ 面板元素已找到\n';
                result.innerHTML += `📍 面板位置: ${panel.style.left || 'auto'}, ${panel.style.top || 'auto'}\n`;
                result.innerHTML += `👁️ 面板可见性: ${panel.style.display !== 'none' ? '可见' : '隐藏'}\n`;
                result.innerHTML += `📏 面板尺寸: ${panel.offsetWidth}x${panel.offsetHeight}\n`;
                
                // 检查面板类
                if (window.ylzInjectedPanel) {
                    result.innerHTML += '✅ 面板类实例已找到\n';
                } else {
                    result.innerHTML += '❌ 面板类实例未找到\n';
                }
            } else {
                result.innerHTML += '❌ 面板元素未找到\n';
                result.innerHTML += '💡 提示: 请确保URL包含 "yunpan.gdcourts.gov.cn:82"\n';
            }
        }
        
        function togglePanel() {
            const result = document.getElementById('panelStatusResult');
            const panel = document.getElementById('ylz-injected-panel');
            
            if (panel) {
                const isHidden = panel.style.display === 'none';
                panel.style.display = isHidden ? 'block' : 'none';
                result.innerHTML += `🔄 面板${isHidden ? '显示' : '隐藏'}成功\n`;
            } else {
                result.innerHTML += '❌ 未找到面板元素\n';
            }
        }
        
        function reinitializePanel() {
            const result = document.getElementById('panelStatusResult');
            result.innerHTML = '🔄 正在重新初始化面板...\n';
            
            // 移除现有面板
            const existingPanel = document.getElementById('ylz-injected-panel');
            if (existingPanel) {
                existingPanel.remove();
                result.innerHTML += '🗑️ 已移除现有面板\n';
            }
            
            // 触发重新初始化
            if (typeof initYlzInjectedPanel === 'function') {
                initYlzInjectedPanel();
                result.innerHTML += '✅ 重新初始化完成\n';
            } else {
                result.innerHTML += '❌ 初始化函数未找到\n';
            }
        }
        
        function testMinimize() {
            const result = document.getElementById('controlTestResult');
            const minimizeBtn = document.querySelector('#ylz-minimize-btn');
            
            if (minimizeBtn) {
                minimizeBtn.click();
                result.innerHTML = '✅ 最小化按钮点击成功\n';
            } else {
                result.innerHTML = '❌ 未找到最小化按钮\n';
            }
        }
        
        function testDrag() {
            const result = document.getElementById('controlTestResult');
            const panel = document.getElementById('ylz-injected-panel');
            
            if (panel) {
                // 模拟拖拽到新位置
                panel.style.left = '100px';
                panel.style.top = '100px';
                result.innerHTML = '✅ 面板位置已更改到 (100, 100)\n';
            } else {
                result.innerHTML = '❌ 未找到面板元素\n';
            }
        }
        
        function testClose() {
            const result = document.getElementById('controlTestResult');
            const closeBtn = document.querySelector('#ylz-close-btn');
            
            if (closeBtn) {
                closeBtn.click();
                result.innerHTML = '✅ 关闭按钮点击成功\n';
            } else {
                result.innerHTML = '❌ 未找到关闭按钮\n';
            }
        }
        
        function testRestore() {
            const result = document.getElementById('controlTestResult');
            const panel = document.getElementById('ylz-injected-panel');
            
            if (panel) {
                panel.style.display = 'block';
                panel.style.right = '20px';
                panel.style.top = '20px';
                panel.style.left = 'auto';
                result.innerHTML = '✅ 面板已恢复到默认位置\n';
            } else {
                result.innerHTML = '❌ 未找到面板元素\n';
            }
        }
        
        function testConnection() {
            const result = document.getElementById('communicationResult');
            result.innerHTML = '🔄 正在测试连接...\n';
            
            if (chrome && chrome.runtime) {
                chrome.runtime.sendMessage({ type: 'GET_CONNECTION_STATUS' }, (response) => {
                    if (chrome.runtime.lastError) {
                        result.innerHTML += `❌ 连接错误: ${chrome.runtime.lastError.message}\n`;
                    } else {
                        result.innerHTML += `✅ 连接状态: ${response?.connected ? '已连接' : '未连接'}\n`;
                    }
                });
            } else {
                result.innerHTML += '❌ Chrome扩展API不可用\n';
            }
        }
        
        function testFileTree() {
            const result = document.getElementById('communicationResult');
            result.innerHTML += '🔄 正在获取文件树...\n';
            
            if (chrome && chrome.runtime) {
                chrome.runtime.sendMessage({ type: 'GET_ADMIN_TREE' }, (response) => {
                    if (chrome.runtime.lastError) {
                        result.innerHTML += `❌ 文件树错误: ${chrome.runtime.lastError.message}\n`;
                    } else {
                        result.innerHTML += `✅ 文件树获取${response?.success ? '成功' : '失败'}\n`;
                        if (response?.tree) {
                            result.innerHTML += `📊 文件数量: ${JSON.stringify(response.tree).length} 字符\n`;
                        }
                    }
                });
            } else {
                result.innerHTML += '❌ Chrome扩展API不可用\n';
            }
        }
        
        function testUpload() {
            const result = document.getElementById('communicationResult');
            result.innerHTML += '🔄 正在测试上传功能...\n';
            
            if (chrome && chrome.runtime) {
                chrome.runtime.sendMessage({ type: 'START_YUNPAN_UPLOAD' }, (response) => {
                    if (chrome.runtime.lastError) {
                        result.innerHTML += `❌ 上传错误: ${chrome.runtime.lastError.message}\n`;
                    } else {
                        result.innerHTML += `✅ 上传测试${response?.success ? '成功' : '失败'}\n`;
                    }
                });
            } else {
                result.innerHTML += '❌ Chrome扩展API不可用\n';
            }
        }
        
        function showDebugInfo() {
            const result = document.getElementById('debugInfo');
            result.innerHTML = '🔍 调试信息:\n';
            result.innerHTML += `🌐 当前URL: ${window.location.href}\n`;
            result.innerHTML += `📱 用户代理: ${navigator.userAgent}\n`;
            result.innerHTML += `📏 窗口尺寸: ${window.innerWidth}x${window.innerHeight}\n`;
            result.innerHTML += `🎯 目标网站检测: ${window.location.href.includes('yunpan.gdcourts.gov.cn:82') ? '✅' : '❌'}\n`;
            result.innerHTML += `🔌 Chrome扩展API: ${chrome && chrome.runtime ? '✅' : '❌'}\n`;
            result.innerHTML += `🎛️ 面板实例: ${window.ylzInjectedPanel ? '✅' : '❌'}\n`;
            
            // 显示控制台日志
            result.innerHTML += '\n📋 最近的控制台消息:\n';
            // 这里可以添加更多调试信息
        }
        
        function clearLogs() {
            document.getElementById('debugInfo').innerHTML = '日志已清除';
            document.getElementById('panelStatusResult').innerHTML = '等待测试...';
            document.getElementById('controlTestResult').innerHTML = '等待测试...';
            document.getElementById('communicationResult').innerHTML = '等待测试...';
        }
        
        // 页面加载完成后自动检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkPanelStatus();
            }, 3000);
        });
        
        // 监听面板相关事件
        document.addEventListener('DOMNodeInserted', (e) => {
            if (e.target.id === 'ylz-injected-panel') {
                console.log('🎉 检测到面板注入');
                setTimeout(checkPanelStatus, 1000);
            }
        });
    </script>
</body>
</html>

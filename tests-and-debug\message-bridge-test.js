// 消息桥接测试脚本

console.log('🌉 开始消息桥接测试...');

// 1. 测试面板到content script的消息传递
async function testPanelToContentMessage() {
    console.log('\n=== 测试面板到content script的消息传递 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return false;
    }
    
    const panel = window.ylzInjectedPanel;
    
    try {
        console.log('📤 发送测试消息...');
        const response = await panel.safeRuntimeMessage({
            type: 'GET_CONNECTION_STATUS'
        });
        
        console.log('📥 收到响应:', response);
        
        if (response !== null && response !== undefined) {
            console.log('✅ 消息桥接工作正常');
            return true;
        } else {
            console.log('❌ 收到空响应');
            return false;
        }
        
    } catch (error) {
        console.error('❌ 消息传递失败:', error);
        return false;
    }
}

// 2. 测试上传请求消息格式
async function testUploadMessageFormat() {
    console.log('\n=== 测试上传请求消息格式 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return false;
    }
    
    const panel = window.ylzInjectedPanel;
    
    // 创建测试文件数据
    const testFiles = [
        {
            name: 'test-upload.txt',
            path: 'test-upload.txt',
            size: 1024,
            type: 'text'
        }
    ];
    
    const uploadRequest = {
        type: 'START_YUNPAN_UPLOAD',
        data: {
            files: testFiles,
            source: 'message_bridge_test'
        }
    };
    
    try {
        console.log('📤 发送上传请求:', uploadRequest);
        const response = await panel.safeRuntimeMessage(uploadRequest);
        
        console.log('📥 上传响应:', response);
        
        if (response && typeof response === 'object') {
            if (response.success) {
                console.log('✅ 上传请求成功');
            } else {
                console.log('⚠️ 上传请求被拒绝:', response.error);
                console.log('🔍 错误详情:', response.details);
            }
            return true;
        } else {
            console.log('❌ 收到无效响应');
            return false;
        }
        
    } catch (error) {
        console.error('❌ 上传请求失败:', error);
        return false;
    }
}

// 3. 测试直接的chrome.runtime.sendMessage
async function testDirectRuntimeMessage() {
    console.log('\n=== 测试直接的chrome.runtime.sendMessage ===');
    
    const testMessage = {
        type: 'GET_CONNECTION_STATUS'
    };
    
    try {
        console.log('📤 发送直接消息:', testMessage);
        const response = await new Promise((resolve) => {
            chrome.runtime.sendMessage(testMessage, resolve);
        });
        
        console.log('📥 直接响应:', response);
        
        if (response !== null && response !== undefined) {
            console.log('✅ 直接消息传递工作正常');
            return true;
        } else {
            console.log('❌ 直接消息收到空响应');
            return false;
        }
        
    } catch (error) {
        console.error('❌ 直接消息传递失败:', error);
        return false;
    }
}

// 4. 测试上传请求的直接发送
async function testDirectUploadMessage() {
    console.log('\n=== 测试上传请求的直接发送 ===');
    
    const testFiles = [
        {
            name: 'direct-test.txt',
            path: 'direct-test.txt',
            size: 2048,
            type: 'text'
        }
    ];
    
    const uploadRequest = {
        type: 'START_YUNPAN_UPLOAD',
        data: {
            files: testFiles,
            source: 'direct_test'
        }
    };
    
    try {
        console.log('📤 直接发送上传请求:', uploadRequest);
        const response = await new Promise((resolve) => {
            chrome.runtime.sendMessage(uploadRequest, resolve);
        });
        
        console.log('📥 直接上传响应:', response);
        
        if (response && typeof response === 'object') {
            if (response.success) {
                console.log('✅ 直接上传请求成功');
            } else {
                console.log('⚠️ 直接上传请求被拒绝:', response.error);
                console.log('🔍 错误详情:', response.details);
            }
            return true;
        } else {
            console.log('❌ 直接上传收到无效响应');
            return false;
        }
        
    } catch (error) {
        console.error('❌ 直接上传请求失败:', error);
        return false;
    }
}

// 5. 检查content script是否正确加载
function checkContentScriptStatus() {
    console.log('\n=== 检查content script状态 ===');
    
    // 检查是否有面板消息监听器
    const hasEventListeners = window.getEventListeners && 
                              window.getEventListeners(window)['ylz_panel_message'];
    
    console.log('📊 Content Script状态:');
    console.log(`  面板消息监听器: ${hasEventListeners ? '存在' : '未知'}`);
    
    // 发送测试事件
    try {
        const testEvent = new CustomEvent('ylz_panel_message', {
            detail: {
                messageId: 'test_' + Date.now(),
                message: { type: 'PING' }
            }
        });
        
        console.log('📤 发送测试事件...');
        window.dispatchEvent(testEvent);
        
        console.log('✅ 测试事件发送成功');
        return true;
        
    } catch (error) {
        console.error('❌ 发送测试事件失败:', error);
        return false;
    }
}

// 6. 完整的消息桥接测试
async function fullMessageBridgeTest() {
    console.log('🚀 开始完整的消息桥接测试...\n');
    
    try {
        // 1. 检查content script状态
        const contentScriptOK = checkContentScriptStatus();
        
        // 2. 测试直接消息传递
        const directMessageOK = await testDirectRuntimeMessage();
        
        // 3. 测试面板消息传递
        const panelMessageOK = await testPanelToContentMessage();
        
        // 4. 测试直接上传请求
        const directUploadOK = await testDirectUploadMessage();
        
        // 5. 测试面板上传请求
        const panelUploadOK = await testUploadMessageFormat();
        
        // 6. 生成测试报告
        console.log('\n📋 === 消息桥接测试报告 ===');
        console.log(`✅ Content Script: ${contentScriptOK ? '正常' : '异常'}`);
        console.log(`✅ 直接消息传递: ${directMessageOK ? '正常' : '异常'}`);
        console.log(`✅ 面板消息传递: ${panelMessageOK ? '正常' : '异常'}`);
        console.log(`✅ 直接上传请求: ${directUploadOK ? '正常' : '异常'}`);
        console.log(`✅ 面板上传请求: ${panelUploadOK ? '正常' : '异常'}`);
        
        const allOK = directMessageOK && panelMessageOK && directUploadOK && panelUploadOK;
        
        if (allOK) {
            console.log('\n🎉 消息桥接测试全部通过！');
            console.log('💡 现在面板应该能够正常发送上传请求');
        } else {
            console.log('\n⚠️ 消息桥接存在问题');
            
            if (!directMessageOK) {
                console.log('🔧 建议: 检查background script是否正常运行');
            }
            if (!panelMessageOK) {
                console.log('🔧 建议: 检查content script的面板消息监听器');
            }
            if (!directUploadOK || !panelUploadOK) {
                console.log('🔧 建议: 检查上传请求的数据格式和处理逻辑');
            }
        }
        
        return allOK;
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
        return false;
    }
}

// 7. 快速修复测试
async function quickBridgeTest() {
    console.log('\n⚡ 快速桥接测试...');
    
    try {
        // 直接测试面板上传请求
        const result = await testUploadMessageFormat();
        
        if (result) {
            console.log('🎉 快速测试成功！消息桥接已修复');
        } else {
            console.log('❌ 快速测试失败，需要进一步调试');
        }
        
        return result;
        
    } catch (error) {
        console.error('❌ 快速测试出错:', error);
        return false;
    }
}

// 8. 调试消息传递过程
async function debugMessageFlow() {
    console.log('\n🔍 调试消息传递过程...');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return;
    }
    
    const panel = window.ylzInjectedPanel;
    
    // 监听所有相关事件
    const messageId = 'debug_' + Date.now();
    
    console.log('🔍 监听响应事件:', 'ylz_panel_response_' + messageId);
    
    const responseHandler = (event) => {
        console.log('📥 收到调试响应:', event.detail);
    };
    
    window.addEventListener('ylz_panel_response_' + messageId, responseHandler);
    
    // 手动发送消息事件
    const testMessage = {
        type: 'GET_CONNECTION_STATUS'
    };
    
    const customEvent = new CustomEvent('ylz_panel_message', {
        detail: {
            messageId: messageId,
            message: testMessage
        }
    });
    
    console.log('📤 发送调试消息事件:', customEvent.detail);
    window.dispatchEvent(customEvent);
    
    // 等待响应
    setTimeout(() => {
        window.removeEventListener('ylz_panel_response_' + messageId, responseHandler);
        console.log('✅ 调试消息传递完成');
    }, 3000);
}

// 导出函数
window.testPanelToContentMessage = testPanelToContentMessage;
window.testUploadMessageFormat = testUploadMessageFormat;
window.testDirectRuntimeMessage = testDirectRuntimeMessage;
window.testDirectUploadMessage = testDirectUploadMessage;
window.checkContentScriptStatus = checkContentScriptStatus;
window.fullMessageBridgeTest = fullMessageBridgeTest;
window.quickBridgeTest = quickBridgeTest;
window.debugMessageFlow = debugMessageFlow;

console.log('✅ 消息桥接测试脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - fullMessageBridgeTest() - 完整桥接测试');
console.log('  - quickBridgeTest() - 快速桥接测试');
console.log('  - debugMessageFlow() - 调试消息流程');
console.log('  - testUploadMessageFormat() - 测试上传消息格式');

// 自动运行快速测试
quickBridgeTest();

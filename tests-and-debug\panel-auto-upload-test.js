// 面板自动上传功能测试脚本

console.log('🔄 开始面板自动上传功能测试...');

// 1. 检查面板自动上传状态
function checkPanelAutoUploadStatus() {
    console.log('\n=== 检查面板自动上传状态 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return false;
    }
    
    const panel = window.ylzInjectedPanel;
    console.log('📊 面板自动上传状态:');
    console.log(`  启用: ${panel.autoUploadEnabled ? '✅' : '❌'}`);
    console.log(`  上传中: ${panel.uploadInProgress ? '是' : '否'}`);
    console.log(`  上次文件计数: ${panel.lastFileCount}`);
    console.log(`  当前待上传文件数: ${panel.fileData.pendingCount || 0}`);
    
    return panel.autoUploadEnabled;
}

// 2. 启用面板自动上传
async function enablePanelAutoUpload() {
    console.log('\n=== 启用面板自动上传 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return false;
    }
    
    try {
        const panel = window.ylzInjectedPanel;
        
        // 1. 设置面板状态
        panel.autoUploadEnabled = true;
        console.log('✅ 面板自动上传状态已设置为启用');
        
        // 2. 更新UI开关
        const autoUploadSwitch = document.querySelector('#ylz-autoUploadSwitch');
        if (autoUploadSwitch) {
            autoUploadSwitch.checked = true;
            console.log('✅ UI开关已更新');
        }
        
        // 3. 保存到本地存储
        await new Promise((resolve) => {
            chrome.storage.local.set({ autoUploadEnabled: true }, resolve);
        });
        console.log('✅ 本地存储已更新');
        
        // 4. 通知后台
        const response = await panel.safeRuntimeMessage({
            type: 'SET_AUTO_UPLOAD_STATE',
            enabled: true
        });
        
        if (response && response.success) {
            console.log('✅ 后台状态已更新');
        } else {
            console.log('⚠️ 后台状态更新失败');
        }
        
        console.log('🎉 面板自动上传已全面启用');
        return true;
        
    } catch (error) {
        console.error('❌ 启用面板自动上传失败:', error);
        return false;
    }
}

// 3. 手动触发面板自动上传
async function triggerPanelAutoUpload() {
    console.log('\n=== 手动触发面板自动上传 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return false;
    }
    
    const panel = window.ylzInjectedPanel;
    
    if (!panel.autoUploadEnabled) {
        console.log('❌ 面板自动上传未启用');
        return false;
    }
    
    if (panel.uploadInProgress) {
        console.log('⚠️ 上传正在进行中');
        return false;
    }
    
    const pendingCount = panel.fileData.pendingCount || 0;
    if (pendingCount === 0) {
        console.log('⚠️ 没有待上传文件');
        return false;
    }
    
    console.log(`🚀 开始上传 ${pendingCount} 个文件...`);
    
    try {
        await panel.triggerAutoUpload();
        console.log('✅ 自动上传已触发');
        return true;
    } catch (error) {
        console.error('❌ 触发自动上传失败:', error);
        return false;
    }
}

// 4. 检查待上传文件列表
function checkPendingFiles() {
    console.log('\n=== 检查待上传文件列表 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return [];
    }
    
    const panel = window.ylzInjectedPanel;
    const unuploadedFiles = panel.getUnuploadedFilesList();
    
    console.log(`📁 待上传文件数量: ${unuploadedFiles.length}`);
    
    if (unuploadedFiles.length > 0) {
        console.log('📄 待上传文件列表:');
        unuploadedFiles.forEach((file, index) => {
            console.log(`  ${index + 1}. ${file.name} (${file.size ? (file.size / 1024 / 1024).toFixed(1) + 'MB' : '未知大小'})`);
        });
    } else {
        console.log('📄 没有待上传文件');
    }
    
    return unuploadedFiles;
}

// 5. 模拟文件变化触发自动上传
async function simulateFileChange() {
    console.log('\n=== 模拟文件变化触发自动上传 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return false;
    }
    
    const panel = window.ylzInjectedPanel;
    
    // 模拟文件数量变化
    const currentCount = panel.fileData.pendingCount || 0;
    const simulatedCount = currentCount + 1;
    
    console.log(`📊 模拟文件数量变化: ${currentCount} -> ${simulatedCount}`);
    
    try {
        await panel.checkAutoUpload(simulatedCount);
        console.log('✅ 文件变化检查已触发');
        return true;
    } catch (error) {
        console.error('❌ 模拟文件变化失败:', error);
        return false;
    }
}

// 6. 监控上传状态
function monitorUploadStatus() {
    console.log('\n=== 开始监控上传状态 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return;
    }
    
    const panel = window.ylzInjectedPanel;
    let checkCount = 0;
    const maxChecks = 30; // 最多检查30次（30秒）
    
    const monitor = setInterval(() => {
        checkCount++;
        
        console.log(`⏰ 监控 ${checkCount}/30: 上传状态 = ${panel.uploadInProgress ? '进行中' : '空闲'}`);
        
        if (!panel.uploadInProgress) {
            console.log('✅ 上传已完成或未在进行');
            clearInterval(monitor);
            
            // 检查结果
            setTimeout(() => {
                const newPendingCount = panel.fileData.pendingCount || 0;
                console.log(`📊 上传后待上传文件数: ${newPendingCount}`);
            }, 1000);
        }
        
        if (checkCount >= maxChecks) {
            console.log('⏰ 监控超时，停止监控');
            clearInterval(monitor);
        }
    }, 1000);
    
    console.log('🔍 上传状态监控已启动（最多30秒）');
}

// 7. 完整的面板自动上传测试
async function fullPanelAutoUploadTest() {
    console.log('🚀 开始完整的面板自动上传测试...\n');
    
    try {
        // 1. 检查面板状态
        const panelExists = checkPanelAutoUploadStatus();
        if (!panelExists) {
            console.log('❌ 面板不存在，无法进行测试');
            return false;
        }
        
        // 2. 检查待上传文件
        const pendingFiles = checkPendingFiles();
        if (pendingFiles.length === 0) {
            console.log('⚠️ 没有待上传文件，无法测试上传功能');
            console.log('💡 建议: 添加一些文件到云盘目录后重试');
            return false;
        }
        
        // 3. 启用自动上传
        const enableResult = await enablePanelAutoUpload();
        if (!enableResult) {
            console.log('❌ 启用自动上传失败');
            return false;
        }
        
        // 4. 等待2秒
        console.log('\n⏰ 等待2秒...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 5. 手动触发上传
        const triggerResult = await triggerPanelAutoUpload();
        if (!triggerResult) {
            console.log('❌ 触发自动上传失败');
            return false;
        }
        
        // 6. 监控上传状态
        monitorUploadStatus();
        
        console.log('\n🎉 面板自动上传测试已启动！');
        console.log('📊 请观察控制台输出和面板通知');
        
        return true;
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
        return false;
    }
}

// 8. 快速启用并测试
async function quickEnableAndTest() {
    console.log('\n⚡ 快速启用并测试面板自动上传...');
    
    try {
        // 1. 启用自动上传
        const enableResult = await enablePanelAutoUpload();
        
        if (enableResult) {
            // 2. 等待1秒
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 3. 检查文件并触发
            const pendingFiles = checkPendingFiles();
            
            if (pendingFiles.length > 0) {
                console.log('🚀 发现待上传文件，触发自动上传...');
                await triggerPanelAutoUpload();
                monitorUploadStatus();
            } else {
                console.log('⚠️ 没有待上传文件');
            }
        }
        
    } catch (error) {
        console.error('❌ 快速测试失败:', error);
    }
}

// 9. 测试文件变化检测
async function testFileChangeDetection() {
    console.log('\n🧪 测试文件变化检测...');
    
    try {
        // 1. 启用自动上传
        await enablePanelAutoUpload();
        
        // 2. 等待1秒
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 3. 模拟文件变化
        await simulateFileChange();
        
        // 4. 监控状态
        monitorUploadStatus();
        
        console.log('✅ 文件变化检测测试已启动');
        
    } catch (error) {
        console.error('❌ 文件变化检测测试失败:', error);
    }
}

// 导出函数
window.checkPanelAutoUploadStatus = checkPanelAutoUploadStatus;
window.enablePanelAutoUpload = enablePanelAutoUpload;
window.triggerPanelAutoUpload = triggerPanelAutoUpload;
window.checkPendingFiles = checkPendingFiles;
window.simulateFileChange = simulateFileChange;
window.monitorUploadStatus = monitorUploadStatus;
window.fullPanelAutoUploadTest = fullPanelAutoUploadTest;
window.quickEnableAndTest = quickEnableAndTest;
window.testFileChangeDetection = testFileChangeDetection;

console.log('✅ 面板自动上传测试脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - fullPanelAutoUploadTest() - 完整测试');
console.log('  - quickEnableAndTest() - 快速启用并测试');
console.log('  - testFileChangeDetection() - 测试文件变化检测');
console.log('  - enablePanelAutoUpload() - 启用自动上传');
console.log('  - triggerPanelAutoUpload() - 手动触发上传');

// 自动运行快速测试
quickEnableAndTest();

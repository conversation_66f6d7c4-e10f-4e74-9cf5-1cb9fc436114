// 面板调试脚本 - 在控制台中运行此脚本来调试面板问题

console.log('🔍 开始面板调试...');

// 1. 检查面板元素
function checkPanelElement() {
    console.log('\n=== 检查面板元素 ===');
    const panel = document.getElementById('ylz-injected-panel');
    
    if (panel) {
        console.log('✅ 面板元素存在');
        console.log('📍 面板位置:', {
            left: panel.style.left,
            top: panel.style.top,
            right: panel.style.right,
            bottom: panel.style.bottom
        });
        console.log('📏 面板尺寸:', {
            width: panel.offsetWidth,
            height: panel.offsetHeight
        });
        console.log('👁️ 面板可见性:', {
            display: panel.style.display,
            visibility: panel.style.visibility,
            opacity: panel.style.opacity
        });
        
        // 检查子元素
        const importantElements = [
            'ylz-connectionStatusDot',
            'ylz-connectionStatusText', 
            'ylz-uploadAll',
            'ylz-refreshList',
            'ylz-pendingTab',
            'ylz-uploadedTab'
        ];
        
        console.log('🔍 检查重要子元素:');
        importantElements.forEach(id => {
            const element = panel.querySelector(`#${id}`);
            console.log(`  ${id}: ${element ? '✅' : '❌'}`);
        });
        
    } else {
        console.log('❌ 面板元素不存在');
    }
    
    return !!panel;
}

// 2. 检查面板实例
function checkPanelInstance() {
    console.log('\n=== 检查面板实例 ===');
    
    if (window.ylzInjectedPanel) {
        console.log('✅ 面板实例存在');
        console.log('📊 实例状态:', {
            connectionStatus: window.ylzInjectedPanel.connectionStatus,
            yunpanStatus: window.ylzInjectedPanel.yunpanStatus,
            currentTab: window.ylzInjectedPanel.currentTab,
            isMinimized: window.ylzInjectedPanel.isMinimized
        });
        
        console.log('📁 文件数据:', {
            pendingCount: window.ylzInjectedPanel.fileData?.pending?.length || 0,
            uploadedCount: window.ylzInjectedPanel.fileData?.uploaded?.length || 0
        });
        
        return true;
    } else {
        console.log('❌ 面板实例不存在');
        console.log('🔍 检查全局变量:', Object.keys(window).filter(key => key.includes('ylz')));
        return false;
    }
}

// 3. 测试Chrome扩展API
function testChromeAPI() {
    console.log('\n=== 测试Chrome扩展API ===');
    
    if (chrome && chrome.runtime) {
        console.log('✅ Chrome扩展API可用');
        
        // 测试消息发送
        console.log('📡 测试消息发送...');
        chrome.runtime.sendMessage({ type: 'GET_CONNECTION_STATUS' }, (response) => {
            if (chrome.runtime.lastError) {
                console.log('❌ 消息发送失败:', chrome.runtime.lastError.message);
            } else {
                console.log('✅ 消息发送成功:', response);
            }
        });
        
        return true;
    } else {
        console.log('❌ Chrome扩展API不可用');
        return false;
    }
}

// 4. 检查页面环境
function checkPageEnvironment() {
    console.log('\n=== 检查页面环境 ===');
    
    console.log('🌐 页面信息:', {
        url: window.location.href,
        title: document.title,
        readyState: document.readyState
    });
    
    const isTargetSite = window.location.href.includes('yunpan.gdcourts.gov.cn') ||
                        window.location.href.includes('injected-panel-test.html');
    
    console.log('🎯 目标网站检测:', isTargetSite ? '✅' : '❌');
    
    // 检查相关脚本
    const scripts = Array.from(document.scripts).filter(script => 
        script.src.includes('injected-panel') || script.src.includes('ylz')
    );
    
    console.log('📜 相关脚本:', scripts.map(s => s.src));
    
    return isTargetSite;
}

// 5. 尝试手动初始化
function manualInitialize() {
    console.log('\n=== 尝试手动初始化 ===');
    
    try {
        if (typeof initYlzInjectedPanel === 'function') {
            console.log('🔄 调用初始化函数...');
            initYlzInjectedPanel();
            
            // 等待一段时间后检查结果
            setTimeout(() => {
                console.log('⏰ 初始化后检查:');
                checkPanelInstance();
            }, 2000);
            
        } else {
            console.log('❌ 初始化函数不存在');
            
            // 尝试重新加载脚本
            console.log('🔄 尝试重新加载面板脚本...');
            const script = document.createElement('script');
            script.src = chrome.runtime.getURL('injected-panel.js');
            script.onload = () => {
                console.log('✅ 脚本重新加载成功');
                setTimeout(() => {
                    if (typeof initYlzInjectedPanel === 'function') {
                        initYlzInjectedPanel();
                    }
                }, 1000);
            };
            script.onerror = () => {
                console.log('❌ 脚本重新加载失败');
            };
            document.head.appendChild(script);
        }
    } catch (error) {
        console.log('❌ 手动初始化失败:', error);
    }
}

// 6. 生成诊断报告
function generateDiagnosticReport() {
    console.log('\n=== 📋 诊断报告 ===');
    
    const report = {
        timestamp: new Date().toISOString(),
        pageInfo: {
            url: window.location.href,
            title: document.title,
            readyState: document.readyState
        },
        panelElement: !!document.getElementById('ylz-injected-panel'),
        panelInstance: !!window.ylzInjectedPanel,
        chromeAPI: !!(chrome && chrome.runtime),
        targetSite: window.location.href.includes('yunpan.gdcourts.gov.cn') ||
                   window.location.href.includes('injected-panel-test.html'),
        scripts: Array.from(document.scripts)
            .filter(s => s.src.includes('injected-panel') || s.src.includes('ylz'))
            .map(s => s.src),
        errors: []
    };
    
    // 检查常见问题
    if (!report.panelElement && !report.panelInstance) {
        report.errors.push('面板完全未初始化');
    } else if (report.panelElement && !report.panelInstance) {
        report.errors.push('面板HTML存在但实例未创建');
    } else if (!report.panelElement && report.panelInstance) {
        report.errors.push('面板实例存在但HTML元素缺失');
    }
    
    if (!report.chromeAPI) {
        report.errors.push('Chrome扩展API不可用');
    }
    
    if (!report.targetSite) {
        report.errors.push('非目标网站');
    }
    
    console.log('📊 完整报告:', report);
    
    // 保存到全局变量供复制
    window.ylzDiagnosticReport = report;
    console.log('💾 报告已保存到 window.ylzDiagnosticReport');
    
    return report;
}

// 7. 主要调试函数
function debugPanel() {
    console.log('🚀 开始完整面板调试...\n');
    
    const results = {
        pageEnvironment: checkPageEnvironment(),
        chromeAPI: testChromeAPI(),
        panelElement: checkPanelElement(),
        panelInstance: checkPanelInstance()
    };
    
    console.log('\n📊 调试结果汇总:', results);
    
    // 如果有问题，尝试修复
    if (!results.panelInstance) {
        console.log('\n🔧 检测到问题，尝试修复...');
        manualInitialize();
    }
    
    // 生成诊断报告
    setTimeout(() => {
        generateDiagnosticReport();
    }, 3000);
    
    return results;
}

// 导出调试函数到全局
window.debugPanel = debugPanel;
window.checkPanelElement = checkPanelElement;
window.checkPanelInstance = checkPanelInstance;
window.testChromeAPI = testChromeAPI;
window.manualInitialize = manualInitialize;

console.log('✅ 调试脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - debugPanel() - 运行完整调试');
console.log('  - checkPanelElement() - 检查面板元素');
console.log('  - checkPanelInstance() - 检查面板实例');
console.log('  - testChromeAPI() - 测试扩展API');
console.log('  - manualInitialize() - 手动初始化');

// 自动运行调试
debugPanel();

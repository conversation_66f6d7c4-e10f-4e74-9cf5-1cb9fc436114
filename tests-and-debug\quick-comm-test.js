// 快速通信测试 - 在控制台中运行

console.log('🔗 快速通信测试开始...');

// 1. 检查事件监听器是否存在
function checkEventListeners() {
    console.log('\n=== 检查事件监听器 ===');
    
    // 检查是否有ylz相关的事件监听器
    const events = ['ylz_panel_message', 'ylz_background_message'];
    
    events.forEach(eventName => {
        // 创建测试事件
        const testEvent = new CustomEvent(eventName, { detail: { test: true } });
        
        // 记录是否有监听器
        let hasListener = false;
        const testHandler = () => { hasListener = true; };
        
        // 临时添加监听器来检测
        window.addEventListener(eventName, testHandler);
        window.dispatchEvent(testEvent);
        window.removeEventListener(eventName, testHandler);
        
        console.log(`${eventName}: ${hasListener ? '✅ 有监听器' : '❌ 无监听器'}`);
    });
}

// 2. 测试消息发送
function testMessageSending() {
    console.log('\n=== 测试消息发送 ===');
    
    const messageId = 'test_' + Date.now();
    let responseReceived = false;
    
    // 设置响应监听器
    const responseHandler = (event) => {
        responseReceived = true;
        console.log('✅ 收到响应:', event.detail);
        window.removeEventListener('ylz_panel_response_' + messageId, responseHandler);
    };
    
    window.addEventListener('ylz_panel_response_' + messageId, responseHandler);
    
    // 发送测试消息
    const testMessage = new CustomEvent('ylz_panel_message', {
        detail: {
            messageId: messageId,
            message: { type: 'GET_CONNECTION_STATUS' }
        }
    });
    
    console.log('📤 发送测试消息...');
    window.dispatchEvent(testMessage);
    
    // 3秒后检查结果
    setTimeout(() => {
        if (!responseReceived) {
            console.log('❌ 3秒内未收到响应');
            window.removeEventListener('ylz_panel_response_' + messageId, responseHandler);
        }
    }, 3000);
}

// 3. 检查Chrome API
function checkChromeAPI() {
    console.log('\n=== 检查Chrome API ===');
    
    if (chrome && chrome.runtime) {
        console.log('✅ Chrome API可用');
        
        // 测试直接发送消息
        chrome.runtime.sendMessage({ type: 'GET_CONNECTION_STATUS' }, (response) => {
            if (chrome.runtime.lastError) {
                console.log('❌ 直接消息发送失败:', chrome.runtime.lastError.message);
            } else {
                console.log('✅ 直接消息发送成功:', response);
            }
        });
    } else {
        console.log('❌ Chrome API不可用');
    }
}

// 4. 检查面板状态
function checkPanelStatus() {
    console.log('\n=== 检查面板状态 ===');
    
    const panel = document.getElementById('ylz-injected-panel');
    console.log('面板元素:', panel ? '✅ 存在' : '❌ 不存在');
    
    console.log('面板实例:', window.ylzInjectedPanel ? '✅ 存在' : '❌ 不存在');
    
    if (window.ylzInjectedPanel) {
        console.log('面板状态:', {
            connectionStatus: window.ylzInjectedPanel.connectionStatus,
            currentTab: window.ylzInjectedPanel.currentTab
        });
    }
}

// 5. 手动触发通信设置
function manualSetupCommunication() {
    console.log('\n=== 手动设置通信 ===');
    
    // 检查是否有setupPanelCommunication函数
    if (typeof setupPanelCommunication === 'function') {
        console.log('🔄 调用setupPanelCommunication...');
        setupPanelCommunication();
    } else {
        console.log('❌ setupPanelCommunication函数不存在');
        
        // 手动设置基本通信
        console.log('🔧 手动设置基本通信...');
        
        window.addEventListener('ylz_panel_message', async (event) => {
            const { messageId, message } = event.detail;
            console.log('[手动通信] 收到面板消息:', message);
            
            try {
                if (chrome && chrome.runtime) {
                    const response = await new Promise((resolve, reject) => {
                        chrome.runtime.sendMessage(message, (response) => {
                            if (chrome.runtime.lastError) {
                                reject(chrome.runtime.lastError);
                            } else {
                                resolve(response);
                            }
                        });
                    });
                    
                    // 发送响应
                    const responseEvent = new CustomEvent('ylz_panel_response_' + messageId, {
                        detail: response
                    });
                    window.dispatchEvent(responseEvent);
                    console.log('[手动通信] 已发送响应:', response);
                } else {
                    throw new Error('Chrome API不可用');
                }
            } catch (error) {
                console.error('[手动通信] 处理消息失败:', error);
                
                const errorEvent = new CustomEvent('ylz_panel_response_' + messageId, {
                    detail: { error: error.message }
                });
                window.dispatchEvent(errorEvent);
            }
        });
        
        console.log('✅ 手动通信设置完成');
    }
}

// 6. 完整测试
function quickCommTest() {
    console.log('🚀 开始快速通信测试...\n');
    
    checkPanelStatus();
    checkChromeAPI();
    checkEventListeners();
    
    // 如果没有监听器，尝试手动设置
    setTimeout(() => {
        manualSetupCommunication();
        
        // 设置完成后测试消息发送
        setTimeout(() => {
            testMessageSending();
        }, 1000);
    }, 1000);
}

// 7. 修复函数
function fixCommunication() {
    console.log('\n🔧 尝试修复通信...');
    
    // 1. 手动设置通信
    manualSetupCommunication();
    
    // 2. 如果面板实例存在，重新初始化数据
    if (window.ylzInjectedPanel) {
        console.log('🔄 重新初始化面板数据...');
        setTimeout(() => {
            window.ylzInjectedPanel.initializeData();
        }, 1000);
    }
    
    // 3. 测试修复结果
    setTimeout(() => {
        console.log('✅ 修复完成，测试结果:');
        testMessageSending();
    }, 2000);
}

// 导出函数
window.quickCommTest = quickCommTest;
window.checkEventListeners = checkEventListeners;
window.testMessageSending = testMessageSending;
window.checkChromeAPI = checkChromeAPI;
window.checkPanelStatus = checkPanelStatus;
window.manualSetupCommunication = manualSetupCommunication;
window.fixCommunication = fixCommunication;

console.log('✅ 快速通信测试脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - quickCommTest() - 运行完整测试');
console.log('  - fixCommunication() - 尝试修复通信');
console.log('  - testMessageSending() - 测试消息发送');

// 自动运行测试
quickCommTest();

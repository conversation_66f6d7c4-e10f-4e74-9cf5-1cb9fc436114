<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单面板测试 - yunpan.gdcourts.gov.cn:82</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .status-panel {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .log-area {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .mock-content {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .file-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .file-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-connected { background: #28a745; }
        .status-disconnected { background: #dc3545; }
        .status-unknown { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 注入面板测试页面</h1>
            <p>模拟 yunpan.gdcourts.gov.cn:82 环境</p>
        </div>
        
        <div class="status-panel">
            <h3>📊 面板状态</h3>
            <div id="panelStatus">
                <span class="status-indicator status-unknown"></span>
                <span>检测中...</span>
            </div>
            <div style="margin-top: 10px;">
                <button class="test-button" onclick="checkPanel()">检查面板</button>
                <button class="test-button" onclick="testConnection()">测试连接</button>
                <button class="test-button" onclick="testUpload()">测试上传</button>
                <button class="test-button" onclick="clearLog()">清除日志</button>
            </div>
        </div>
        
        <div class="status-panel">
            <h3>📝 测试日志</h3>
            <div class="log-area" id="logArea">等待测试...</div>
        </div>
        
        <div class="mock-content">
            <h3>📁 模拟云盘内容</h3>
            <p>这里模拟云盘页面内容，注入面板应该出现在页面右上角</p>
            
            <div class="file-grid">
                <div class="file-item">
                    <div style="font-size: 2em; margin-bottom: 10px;">📄</div>
                    <div>测试文档.pdf</div>
                </div>
                <div class="file-item">
                    <div style="font-size: 2em; margin-bottom: 10px;">📊</div>
                    <div>数据表格.xlsx</div>
                </div>
                <div class="file-item">
                    <div style="font-size: 2em; margin-bottom: 10px;">🖼️</div>
                    <div>示例图片.jpg</div>
                </div>
                <div class="file-item">
                    <div style="font-size: 2em; margin-bottom: 10px;">📝</div>
                    <div>工作文档.docx</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let logArea = document.getElementById('logArea');
        let panelStatus = document.getElementById('panelStatus');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logArea.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function updatePanelStatus(status, message) {
            const indicator = panelStatus.querySelector('.status-indicator');
            const text = panelStatus.querySelector('span:last-child');
            
            indicator.className = `status-indicator status-${status}`;
            text.textContent = message;
        }
        
        function checkPanel() {
            log('开始检查面板状态...');
            
            const panel = document.getElementById('ylz-injected-panel');
            if (panel) {
                log('✅ 找到注入面板元素', 'success');
                log(`面板位置: ${panel.style.left || 'auto'}, ${panel.style.top || 'auto'}`);
                log(`面板可见性: ${panel.style.display !== 'none' ? '可见' : '隐藏'}`);
                log(`面板尺寸: ${panel.offsetWidth}x${panel.offsetHeight}`);
                updatePanelStatus('connected', '面板已激活');
                
                // 检查面板功能
                const minimizeBtn = panel.querySelector('#ylz-minimize-btn');
                const closeBtn = panel.querySelector('#ylz-close-btn');
                const refreshBtn = panel.querySelector('#ylz-refreshList');
                
                log(`最小化按钮: ${minimizeBtn ? '✅' : '❌'}`);
                log(`关闭按钮: ${closeBtn ? '✅' : '❌'}`);
                log(`刷新按钮: ${refreshBtn ? '✅' : '❌'}`);
                
                // 检查全局实例
                if (window.ylzInjectedPanel) {
                    log('✅ 找到面板实例', 'success');
                } else {
                    log('❌ 未找到面板实例', 'error');
                }
            } else {
                log('❌ 未找到注入面板元素', 'error');
                updatePanelStatus('disconnected', '面板未激活');
                log('提示: 请确保扩展已安装并启用');
            }
        }
        
        function testConnection() {
            log('测试与background的连接...');
            
            if (chrome && chrome.runtime) {
                chrome.runtime.sendMessage({ type: 'GET_CONNECTION_STATUS' }, (response) => {
                    if (chrome.runtime.lastError) {
                        log(`❌ 连接错误: ${chrome.runtime.lastError.message}`, 'error');
                    } else {
                        log(`✅ 连接状态: ${response?.connected ? '已连接' : '未连接'}`, 'success');
                    }
                });
            } else {
                log('❌ Chrome扩展API不可用', 'error');
            }
        }
        
        function testUpload() {
            log('测试上传功能...');
            
            if (chrome && chrome.runtime) {
                chrome.runtime.sendMessage({ type: 'START_YUNPAN_UPLOAD' }, (response) => {
                    if (chrome.runtime.lastError) {
                        log(`❌ 上传测试错误: ${chrome.runtime.lastError.message}`, 'error');
                    } else {
                        log(`✅ 上传测试${response?.success ? '成功' : '失败'}`, response?.success ? 'success' : 'error');
                    }
                });
            } else {
                log('❌ Chrome扩展API不可用', 'error');
            }
        }
        
        function clearLog() {
            logArea.textContent = '日志已清除\n';
        }
        
        // 页面加载完成后自动检查
        window.addEventListener('load', () => {
            log('页面加载完成，开始初始化检查...');
            log(`当前URL: ${window.location.href}`);
            log(`是否包含目标域名: ${window.location.href.includes('yunpan.gdcourts.gov.cn') ? '是' : '否'}`);
            
            // 延迟检查，等待面板注入
            setTimeout(() => {
                checkPanel();
            }, 3000);
        });
        
        // 监听面板注入事件
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.id === 'ylz-injected-panel') {
                        log('🎉 检测到面板注入!', 'success');
                        setTimeout(() => {
                            checkPanel();
                        }, 1000);
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // 定期检查面板状态
        setInterval(() => {
            const panel = document.getElementById('ylz-injected-panel');
            if (panel) {
                updatePanelStatus('connected', '面板运行中');
            } else {
                updatePanelStatus('disconnected', '面板未激活');
            }
        }, 5000);
    </script>
</body>
</html>

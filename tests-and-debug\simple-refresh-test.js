// 简化的自动刷新测试脚本 - 直接在控制台运行

console.log('🔧 简化自动刷新测试开始...');

// 1. 直接检查自动刷新状态
function checkStatus() {
    console.log('\n=== 检查自动刷新状态 ===');
    chrome.runtime.sendMessage({ type: 'GET_AUTO_REFRESH_STATUS' }, (response) => {
        console.log('📊 状态响应:', response);
        if (response && response.success) {
            console.log(`  启用: ${response.enabled ? '✅' : '❌'}`);
            console.log(`  间隔: ${response.interval}秒`);
            console.log(`  目标标签页: ${response.targetTabId || 'null'}`);
            console.log(`  上次刷新: ${response.lastRefreshTime ? new Date(response.lastRefreshTime).toLocaleTimeString() : '从未'}`);
        }
    });
}

// 2. 强制设置30秒间隔
function setRefresh30() {
    console.log('\n=== 设置30秒自动刷新 ===');
    chrome.runtime.sendMessage({ 
        type: 'SET_AUTO_REFRESH', 
        interval: 30 
    }, (response) => {
        console.log('🔧 设置响应:', response);
        if (response && response.success) {
            console.log(`✅ ${response.message}`);
            console.log('⏰ 请等待30秒观察控制台日志...');
            
            // 5秒后检查状态
            setTimeout(() => {
                console.log('\n📊 5秒后状态检查:');
                checkStatus();
            }, 5000);
        }
    });
}

// 3. 检查云盘标签页
function checkTabs() {
    console.log('\n=== 检查云盘标签页 ===');
    chrome.tabs.query({url: ['*://yunpan.gdcourts.gov.cn:82/*']}, (tabs) => {
        console.log(`📋 找到 ${tabs.length} 个云盘标签页:`);
        tabs.forEach((tab, index) => {
            console.log(`  ${index + 1}. ID: ${tab.id}, 活跃: ${tab.active ? '✅' : '❌'}`);
            console.log(`     URL: ${tab.url}`);
        });
    });
}

// 4. 手动触发刷新测试
function manualRefresh() {
    console.log('\n=== 手动刷新测试 ===');
    chrome.tabs.query({url: ['*://yunpan.gdcourts.gov.cn:82/*']}, async (tabs) => {
        if (tabs.length === 0) {
            console.log('❌ 没有云盘标签页');
            return;
        }
        
        const targetTab = tabs.find(tab => !tab.active) || tabs[0];
        console.log(`🎯 选择标签页 ${targetTab.id} 进行测试`);
        
        try {
            await chrome.tabs.reload(targetTab.id);
            console.log('✅ 手动刷新成功');
        } catch (error) {
            console.log('❌ 手动刷新失败:', error);
        }
    });
}

// 5. 监听刷新通知
function startMonitoring() {
    console.log('\n=== 开始监听刷新通知 ===');
    
    const listener = (message, sender, sendResponse) => {
        if (message.type === 'PAGE_REFRESHED') {
            console.log('📡 收到页面刷新通知:');
            console.log(`   时间: ${new Date(message.timestamp).toLocaleTimeString()}`);
            console.log(`   间隔: ${message.interval}秒`);
        }
    };
    
    chrome.runtime.onMessage.addListener(listener);
    console.log('👂 监听器已启动，30秒后自动停止');
    
    setTimeout(() => {
        chrome.runtime.onMessage.removeListener(listener);
        console.log('🔇 监听器已停止');
    }, 30000);
}

// 6. 完整测试流程
function fullTest() {
    console.log('🚀 开始完整自动刷新测试...\n');
    
    // 步骤1: 检查当前状态
    checkStatus();
    
    // 步骤2: 检查标签页
    setTimeout(() => {
        checkTabs();
    }, 1000);
    
    // 步骤3: 开始监听
    setTimeout(() => {
        startMonitoring();
    }, 2000);
    
    // 步骤4: 设置30秒刷新
    setTimeout(() => {
        setRefresh30();
    }, 3000);
    
    // 步骤5: 手动测试
    setTimeout(() => {
        console.log('\n💡 如果想要手动测试刷新，请运行: manualRefresh()');
    }, 5000);
}

// 导出到全局
window.checkStatus = checkStatus;
window.setRefresh30 = setRefresh30;
window.checkTabs = checkTabs;
window.manualRefresh = manualRefresh;
window.startMonitoring = startMonitoring;
window.fullTest = fullTest;

console.log('✅ 简化测试脚本加载完成！');
console.log('📖 可用命令:');
console.log('  - fullTest() - 完整测试');
console.log('  - setRefresh30() - 设置30秒刷新');
console.log('  - checkStatus() - 检查状态');
console.log('  - checkTabs() - 检查标签页');
console.log('  - manualRefresh() - 手动刷新测试');

// 自动运行完整测试
fullTest();

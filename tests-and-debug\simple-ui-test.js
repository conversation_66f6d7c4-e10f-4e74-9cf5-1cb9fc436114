// 简单的UI测试脚本

console.log('🎛️ 开始UI功能测试...');

// 1. 检查UI元素是否存在
function checkUIElements() {
    console.log('\n=== 检查UI元素 ===');
    
    const panel = document.getElementById('ylz-injected-panel');
    if (!panel) {
        console.log('❌ 面板不存在');
        return false;
    }
    
    const autoRefreshSelect = panel.querySelector('#ylz-autoRefreshSelect');
    const allowActiveRefreshCheckbox = panel.querySelector('#ylz-allowActiveRefresh');
    
    console.log('📊 UI元素检查:');
    console.log(`  自动刷新选择器: ${autoRefreshSelect ? '✅ 存在' : '❌ 不存在'}`);
    console.log(`  刷新当前页面勾选框: ${allowActiveRefreshCheckbox ? '✅ 存在' : '❌ 不存在'}`);
    
    if (autoRefreshSelect) {
        console.log(`  当前选中值: ${autoRefreshSelect.value}秒`);
    }
    
    if (allowActiveRefreshCheckbox) {
        console.log(`  勾选框状态: ${allowActiveRefreshCheckbox.checked ? '✅ 已勾选' : '❌ 未勾选'}`);
    }
    
    return autoRefreshSelect && allowActiveRefreshCheckbox;
}

// 2. 测试勾选框功能
function testCheckboxFunction() {
    console.log('\n=== 测试勾选框功能 ===');
    
    const allowActiveRefreshCheckbox = document.querySelector('#ylz-allowActiveRefresh');
    if (!allowActiveRefreshCheckbox) {
        console.log('❌ 勾选框不存在');
        return;
    }
    
    const originalState = allowActiveRefreshCheckbox.checked;
    console.log(`原始状态: ${originalState ? '已勾选' : '未勾选'}`);
    
    // 模拟点击
    console.log('🧪 模拟点击勾选框...');
    allowActiveRefreshCheckbox.click();
    
    setTimeout(() => {
        const newState = allowActiveRefreshCheckbox.checked;
        console.log(`点击后状态: ${newState ? '已勾选' : '未勾选'}`);
        console.log(`状态变化: ${newState !== originalState ? '✅ 成功' : '❌ 失败'}`);
        
        // 恢复原状态
        setTimeout(() => {
            if (newState !== originalState) {
                console.log('🔄 恢复原状态...');
                allowActiveRefreshCheckbox.click();
            }
        }, 2000);
    }, 1000);
}

// 3. 测试自动刷新选择器
function testAutoRefreshSelect() {
    console.log('\n=== 测试自动刷新选择器 ===');
    
    const autoRefreshSelect = document.querySelector('#ylz-autoRefreshSelect');
    if (!autoRefreshSelect) {
        console.log('❌ 自动刷新选择器不存在');
        return;
    }
    
    const originalValue = autoRefreshSelect.value;
    console.log(`原始值: ${originalValue}秒`);
    
    // 测试设置为60秒
    console.log('🧪 设置为60秒...');
    autoRefreshSelect.value = '60';
    autoRefreshSelect.dispatchEvent(new Event('change'));
    
    setTimeout(() => {
        console.log('✅ 自动刷新选择器测试完成');
        
        // 恢复原值
        setTimeout(() => {
            console.log('🔄 恢复原值...');
            autoRefreshSelect.value = originalValue;
            autoRefreshSelect.dispatchEvent(new Event('change'));
        }, 3000);
    }, 2000);
}

// 4. 检查存储状态
async function checkStorageStatus() {
    console.log('\n=== 检查存储状态 ===');
    
    try {
        const settings = await chrome.storage.local.get([
            'allowRefreshActiveTab',
            'autoPageRefreshEnabled',
            'autoPageRefreshInterval'
        ]);
        
        console.log('💾 存储状态:');
        console.log(`  允许刷新当前页面: ${settings.allowRefreshActiveTab === true ? '✅ 是' : '❌ 否'}`);
        console.log(`  自动刷新启用: ${settings.autoPageRefreshEnabled === true ? '✅ 是' : '❌ 否'}`);
        console.log(`  刷新间隔: ${settings.autoPageRefreshInterval || '未设置'}秒`);
        
        return settings;
    } catch (error) {
        console.error('❌ 获取存储状态失败:', error);
        return null;
    }
}

// 5. 检查后台自动刷新状态
function checkBackendStatus() {
    console.log('\n=== 检查后台自动刷新状态 ===');
    
    return new Promise((resolve) => {
        chrome.runtime.sendMessage({ type: 'GET_AUTO_REFRESH_STATUS' }, (response) => {
            if (response && response.success) {
                console.log('📡 后台状态:');
                console.log(`  启用: ${response.enabled ? '✅' : '❌'}`);
                console.log(`  间隔: ${response.interval}秒`);
                console.log(`  目标标签页: ${response.targetTabId || 'null'}`);
                console.log(`  上次刷新: ${response.lastRefreshTime ? new Date(response.lastRefreshTime).toLocaleTimeString() : '从未'}`);
                resolve(response);
            } else {
                console.log('❌ 获取后台状态失败');
                resolve(null);
            }
        });
    });
}

// 6. 完整UI测试
async function fullUITest() {
    console.log('🚀 开始完整UI测试...\n');
    
    try {
        // 1. 检查UI元素
        const uiOK = checkUIElements();
        
        if (!uiOK) {
            console.log('❌ UI元素检查失败，停止测试');
            return;
        }
        
        // 2. 检查存储状态
        const storageStatus = await checkStorageStatus();
        
        // 3. 检查后台状态
        const backendStatus = await checkBackendStatus();
        
        // 4. 测试勾选框功能
        setTimeout(() => {
            testCheckboxFunction();
        }, 2000);
        
        // 5. 测试自动刷新选择器
        setTimeout(() => {
            testAutoRefreshSelect();
        }, 6000);
        
        // 6. 生成测试报告
        setTimeout(() => {
            console.log('\n📋 === UI测试报告 ===');
            console.log(`✅ UI元素: ${uiOK ? '正常' : '异常'}`);
            console.log(`✅ 存储功能: ${storageStatus ? '正常' : '异常'}`);
            console.log(`✅ 后台通信: ${backendStatus ? '正常' : '异常'}`);
            console.log('✅ 交互测试: 已执行');
            
            if (uiOK && storageStatus && backendStatus) {
                console.log('\n🎉 UI功能测试通过！');
                console.log('💡 使用说明:');
                console.log('  - 选择自动刷新间隔（关闭、60秒、5分钟、15分钟、30分钟）');
                console.log('  - 勾选"刷新当前页面"允许刷新正在使用的标签页');
                console.log('  - 不勾选则只刷新后台标签页（默认安全模式）');
            } else {
                console.log('\n⚠️ UI功能存在问题，需要进一步检查');
            }
        }, 12000);
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
    }
}

// 7. 快速设置测试
function quickSetTest() {
    console.log('\n⚡ 快速设置测试...');
    
    // 设置60秒刷新 + 允许刷新当前页面
    const autoRefreshSelect = document.querySelector('#ylz-autoRefreshSelect');
    const allowActiveRefreshCheckbox = document.querySelector('#ylz-allowActiveRefresh');
    
    if (autoRefreshSelect && allowActiveRefreshCheckbox) {
        console.log('🔧 设置60秒刷新 + 允许刷新当前页面');
        
        autoRefreshSelect.value = '60';
        autoRefreshSelect.dispatchEvent(new Event('change'));
        
        if (!allowActiveRefreshCheckbox.checked) {
            allowActiveRefreshCheckbox.click();
        }
        
        console.log('✅ 快速设置完成，60秒后页面将自动刷新');
    } else {
        console.log('❌ UI元素不存在');
    }
}

// 导出函数
window.checkUIElements = checkUIElements;
window.testCheckboxFunction = testCheckboxFunction;
window.testAutoRefreshSelect = testAutoRefreshSelect;
window.checkStorageStatus = checkStorageStatus;
window.checkBackendStatus = checkBackendStatus;
window.fullUITest = fullUITest;
window.quickSetTest = quickSetTest;

console.log('✅ UI测试脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - fullUITest() - 完整UI测试');
console.log('  - quickSetTest() - 快速设置测试（60秒刷新+允许刷新当前页面）');
console.log('  - checkUIElements() - 检查UI元素');

// 自动运行完整测试
fullUITest();

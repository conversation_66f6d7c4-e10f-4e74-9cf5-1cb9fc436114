<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-box {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .result {
            background: #ecf0f1;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .success { background: #d5edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>简化上传测试</h1>
    
    <div class="test-box">
        <h3>1. 测试文件数据获取</h3>
        <button onclick="testGetFileData()">测试获取文件数据</button>
        <div id="fileDataResult" class="result"></div>
    </div>
    
    <div class="test-box">
        <h3>2. 测试云盘页面通信</h3>
        <button onclick="testYunpanCommunication()">测试云盘页面通信</button>
        <div id="yunpanCommResult" class="result"></div>
    </div>
    
    <div class="test-box">
        <h3>3. 测试完整上传流程</h3>
        <button onclick="testCompleteUpload()">测试完整上传</button>
        <div id="uploadResult" class="result"></div>
    </div>
    
    <div class="test-box">
        <h3>4. 打开云盘页面</h3>
        <button onclick="openYunpanPage()">打开云盘页面</button>
        <div id="openResult" class="result"></div>
    </div>

    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.textContent = `[${timestamp}] ${message}`;
            element.className = `result ${type}`;
        }
        
        async function testGetFileData() {
            log('fileDataResult', '正在测试文件数据获取...', 'info');
            
            try {
                // 首先获取文件列表
                const treeResponse = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({ type: 'GET_ADMIN_TREE' }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                if (!treeResponse.success || !treeResponse.tree) {
                    throw new Error('无法获取文件列表');
                }
                
                // 找到第一个文件
                let testFile = null;
                const findFirstFile = (node) => {
                    if (node.files && node.files.length > 0) {
                        return node.files[0];
                    }
                    if (node.folders) {
                        for (const folder of node.folders) {
                            const found = findFirstFile(folder.children);
                            if (found) return found;
                        }
                    }
                    return null;
                };
                
                testFile = findFirstFile(treeResponse.tree);
                
                if (!testFile) {
                    throw new Error('没有找到可测试的文件');
                }
                
                log('fileDataResult', `找到测试文件: ${testFile.name}，正在获取数据...`, 'info');
                
                // 测试获取文件数据
                const fileDataResponse = await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('获取文件数据超时'));
                    }, 10000);
                    
                    chrome.runtime.sendMessage({
                        type: 'GET_FILE_DATA',
                        filename: testFile.name,
                        path: testFile.path
                    }, (response) => {
                        clearTimeout(timeout);
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                if (fileDataResponse.success) {
                    const data = fileDataResponse.data;
                    log('fileDataResult', 
                        `文件数据获取成功!\n` +
                        `文件名: ${data.name}\n` +
                        `MIME类型: ${data.mimeType}\n` +
                        `内容大小: ${data.content?.length || 0} 字符\n` +
                        `内容类型: ${typeof data.content}\n` +
                        `内容预览: ${data.content ? data.content.substring(0, 100) + '...' : '无内容'}`,
                        'success'
                    );
                } else {
                    throw new Error(fileDataResponse.error || '文件数据获取失败');
                }
                
            } catch (error) {
                log('fileDataResult', `文件数据获取失败: ${error.message}`, 'error');
            }
        }
        
        async function testYunpanCommunication() {
            log('yunpanCommResult', '正在测试云盘页面通信...', 'info');
            
            try {
                const response = await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('通信测试超时'));
                    }, 5000);
                    
                    chrome.runtime.sendMessage({
                        type: 'CHECK_YUNPAN_PAGE'
                    }, (response) => {
                        clearTimeout(timeout);
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                if (response.available) {
                    log('yunpanCommResult', 
                        `云盘页面通信成功!\n` +
                        `页面URL: ${response.tabUrl}\n` +
                        `标签页ID: ${response.tabId}\n` +
                        `脚本响应: ${JSON.stringify(response.scriptResponse)}`,
                        'success'
                    );
                } else {
                    log('yunpanCommResult', 
                        `云盘页面不可用:\n` +
                        `原因: ${response.message}\n` +
                        `建议: ${response.details?.suggestion || '请打开云盘页面'}`,
                        'error'
                    );
                }
                
            } catch (error) {
                log('yunpanCommResult', `云盘页面通信失败: ${error.message}`, 'error');
            }
        }
        
        async function testCompleteUpload() {
            log('uploadResult', '正在测试完整上传流程...', 'info');
            
            try {
                // 创建测试文件
                const testFiles = [{
                    name: '测试文件.txt',
                    path: '测试文件.txt',
                    size: 100,
                    type: 'txt'
                }];
                
                log('uploadResult', `开始上传测试文件: ${testFiles[0].name}`, 'info');
                
                const response = await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('上传测试超时（30秒）'));
                    }, 30000);
                    
                    chrome.runtime.sendMessage({
                        type: 'START_YUNPAN_UPLOAD',
                        data: { files: testFiles }
                    }, (response) => {
                        clearTimeout(timeout);
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                if (response.success) {
                    const stats = response.result;
                    log('uploadResult', 
                        `上传测试完成!\n` +
                        `总计: ${stats.total}\n` +
                        `成功: ${stats.completed}\n` +
                        `失败: ${stats.failed}\n` +
                        `详细结果: ${JSON.stringify(response, null, 2)}`,
                        stats.failed > 0 ? 'error' : 'success'
                    );
                } else {
                    log('uploadResult', 
                        `上传测试失败:\n` +
                        `错误: ${response.error}\n` +
                        `详情: ${JSON.stringify(response.details || {}, null, 2)}`,
                        'error'
                    );
                }
                
            } catch (error) {
                log('uploadResult', `上传测试异常: ${error.message}`, 'error');
            }
        }
        
        async function openYunpanPage() {
            log('openResult', '正在打开云盘页面...', 'info');
            
            try {
                const tab = await chrome.tabs.create({
                    url: 'https://yunpan.gdcourts.gov.cn/',
                    active: true
                });
                
                log('openResult', 
                    `云盘页面已打开!\n` +
                    `标签页ID: ${tab.id}\n` +
                    `页面URL: ${tab.url}\n` +
                    `请等待页面加载完成，然后测试通信`,
                    'success'
                );
                
            } catch (error) {
                log('openResult', `打开云盘页面失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html> 
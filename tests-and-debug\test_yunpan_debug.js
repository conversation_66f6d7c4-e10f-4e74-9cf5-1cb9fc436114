// 云盘页面DOM检测测试脚本
// 此脚本可以在云盘页面的控制台中运行，用于诊断DOM元素查找问题

console.log('=== 云盘页面DOM检测开始 ===');

// 1. 检测页面基本信息
console.log('页面信息:');
console.log('- 标题:', document.title);
console.log('- URL:', window.location.href);
console.log('- 就绪状态:', document.readyState);

// 2. 检测文件列表容器
console.log('\n文件列表容器检测:');
const listSelectors = [
    '.list-container',
    '.file-list',
    '.file-list-content',
    '.content-list',
    '.file-grid',
    '.file-items',
    '.main-content',
    '.file-browser',
    '[data-role="file-list"]'
];

listSelectors.forEach(selector => {
    const element = document.querySelector(selector);
    if (element) {
        console.log(`✓ 找到: ${selector}`, {
            visible: element.offsetParent !== null,
            childrenCount: element.children.length,
            className: element.className,
            id: element.id
        });
    } else {
        console.log(`✗ 未找到: ${selector}`);
    }
});

// 3. 检测新建按钮
console.log('\n新建按钮检测:');
const newButtonSelectors = [
    '.new-btn',
    '.create-folder-btn',
    '.create-btn',
    '.add-btn',
    '.new-folder',
    'button[title*="新建"]',
    'button[aria-label*="新建"]',
    '[data-action="new"]',
    '[data-action="create"]'
];

newButtonSelectors.forEach(selector => {
    const element = document.querySelector(selector);
    if (element) {
        console.log(`✓ 找到新建按钮: ${selector}`, {
            text: element.textContent.trim(),
            visible: element.offsetParent !== null,
            disabled: element.disabled
        });
    } else {
        console.log(`✗ 未找到新建按钮: ${selector}`);
    }
});

// 4. 通过文本查找新建按钮
console.log('\n通过文本查找新建按钮:');
const allButtons = Array.from(document.querySelectorAll('button, [role="button"]'));
const newButtons = allButtons.filter(btn => btn.textContent && btn.textContent.includes('新建'));
console.log(`找到 ${newButtons.length} 个包含"新建"的按钮:`);
newButtons.forEach((btn, index) => {
    console.log(`  ${index + 1}. "${btn.textContent.trim()}" (visible: ${btn.offsetParent !== null})`);
});

// 5. 检测输入框
console.log('\n输入框检测:');
const inputSelectors = [
    'input[placeholder*="文件夹"]',
    'input[placeholder*="名称"]',
    'input[placeholder*="folder"]',
    '.folder-name-input',
    '.rename-input',
    '.name-input',
    '.modal input[type="text"]',
    '.dialog input[type="text"]',
    'input[type="text"]'
];

inputSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    if (elements.length > 0) {
        console.log(`✓ 找到输入框: ${selector} (${elements.length}个)`);
        Array.from(elements).forEach((input, index) => {
            console.log(`  ${index + 1}. placeholder="${input.placeholder}", visible=${input.offsetParent !== null}, disabled=${input.disabled}`);
        });
    } else {
        console.log(`✗ 未找到输入框: ${selector}`);
    }
});

// 6. 检测模态框
console.log('\n模态框检测:');
const modalSelectors = [
    '.modal',
    '.dialog', 
    '.popup',
    '.ant-modal',
    '.el-dialog',
    '.overlay',
    '[role="dialog"]',
    '.layer'
];

modalSelectors.forEach(selector => {
    const element = document.querySelector(selector);
    if (element) {
        console.log(`✓ 找到模态框: ${selector}`, {
            visible: element.offsetParent !== null,
            className: element.className,
            childrenCount: element.children.length
        });
        
        // 检查模态框中的输入框
        const modalInputs = element.querySelectorAll('input[type="text"], input:not([type])');
        if (modalInputs.length > 0) {
            console.log(`  └─ 模态框中的输入框: ${modalInputs.length}个`);
        }
    } else {
        console.log(`✗ 未找到模态框: ${selector}`);
    }
});

// 7. 检测文件项
console.log('\n文件项检测:');
const itemSelectors = [
    '.file-item',
    '.list-item', 
    '.file-card',
    '.folder-item',
    '.item',
    '[data-type="folder"]',
    '.file-row'
];

let totalItems = 0;
itemSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    if (elements.length > 0) {
        console.log(`✓ 找到文件项: ${selector} (${elements.length}个)`);
        totalItems += elements.length;
    } else {
        console.log(`✗ 未找到文件项: ${selector}`);
    }
});

console.log(`\n总文件项数: ${totalItems}`);

// 8. 模拟点击新建按钮测试
console.log('\n=== 模拟操作测试 ===');
const testNewButton = () => {
    console.log('尝试点击新建按钮...');
    
    // 找到第一个可用的新建按钮
    let newButton = null;
    for (const selector of newButtonSelectors) {
        newButton = document.querySelector(selector);
        if (newButton && newButton.offsetParent !== null) {
            console.log(`使用选择器找到按钮: ${selector}`);
            break;
        }
    }
    
    if (!newButton) {
        const textButtons = allButtons.filter(btn => 
            btn.textContent && btn.textContent.includes('新建') && btn.offsetParent !== null
        );
        if (textButtons.length > 0) {
            newButton = textButtons[0];
            console.log('通过文本找到按钮');
        }
    }
    
    if (newButton) {
        console.log('准备点击按钮...');
        newButton.click();
        
        // 等待一下再检查结果
        setTimeout(() => {
            console.log('点击后检查页面状态...');
            
            // 检查是否出现了新的输入框
            const newInputs = document.querySelectorAll('input[type="text"]:not([data-checked])');
            newInputs.forEach(input => {
                input.setAttribute('data-checked', 'true');
                console.log('新出现的输入框:', {
                    placeholder: input.placeholder,
                    name: input.name,
                    visible: input.offsetParent !== null
                });
            });
            
            // 检查是否出现了新的模态框
            modalSelectors.forEach(selector => {
                const modal = document.querySelector(selector);
                if (modal && modal.offsetParent !== null && !modal.hasAttribute('data-checked')) {
                    modal.setAttribute('data-checked', 'true');
                    console.log(`新出现的模态框: ${selector}`);
                }
            });
            
        }, 1000);
        
    } else {
        console.log('未找到可点击的新建按钮');
    }
};

// 提供手动测试函数
window.testYunpanNewButton = testNewButton;

console.log('\n=== 检测完成 ===');
console.log('如需测试点击新建按钮，请运行: testYunpanNewButton()'); 
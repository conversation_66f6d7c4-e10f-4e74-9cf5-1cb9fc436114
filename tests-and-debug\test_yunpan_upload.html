<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云盘上传功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .test-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background: #3498db;
            color: white;
        }
        .btn-success {
            background: #27ae60;
            color: white;
        }
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log-container {
            max-height: 300px;
            overflow-y: auto;
            background: #2c3e50;
            color: #ecf0f1;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-entry.error {
            color: #e74c3c;
        }
        .log-entry.warn {
            color: #f39c12;
        }
        .log-entry.success {
            color: #27ae60;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-indicator.connected {
            background: #27ae60;
        }
        .status-indicator.disconnected {
            background: #e74c3c;
        }
        .status-indicator.unknown {
            background: #95a5a6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>云盘上传功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 扩展状态检查</div>
            <button class="btn btn-primary" onclick="checkExtensionStatus()">检查扩展状态</button>
            <div id="extensionStatusResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. 云盘页面检测</div>
            <button class="btn btn-primary" onclick="checkYunpanPage()">检测云盘页面</button>
            <button class="btn btn-warning" onclick="openYunpanPage()">打开云盘页面</button>
            <div id="yunpanPageResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. 文件列表获取</div>
            <button class="btn btn-primary" onclick="getFileList()">获取文件列表</button>
            <div id="fileListResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">4. 模拟上传测试</div>
            <button class="btn btn-success" onclick="simulateUpload()">模拟文件上传</button>
            <div id="uploadResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <div class="test-title">5. 实时日志</div>
            <button class="btn btn-primary" onclick="clearLogs()">清空日志</button>
            <button class="btn btn-warning" onclick="toggleAutoScroll()">切换自动滚动</button>
            <div id="logContainer" class="log-container"></div>
        </div>
    </div>

    <script>
        let autoScroll = true;
        let logCount = 0;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            
            logCount++;
            if (logCount > 500) {
                // 保持日志条数在合理范围
                const firstChild = logContainer.firstChild;
                if (firstChild) {
                    logContainer.removeChild(firstChild);
                    logCount--;
                }
            }
            
            if (autoScroll) {
                logContainer.scrollTop = logContainer.scrollHeight;
            }
        }
        
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
            logCount = 0;
            log('日志已清空');
        }
        
        function toggleAutoScroll() {
            autoScroll = !autoScroll;
            log(`自动滚动已${autoScroll ? '开启' : '关闭'}`);
        }
        
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = content;
            element.className = `result ${type}`;
        }
        
        async function checkExtensionStatus() {
            log('开始检查扩展状态...');
            showResult('extensionStatusResult', '检查中...', 'info');
            
            try {
                // 检查是否在扩展环境中
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    throw new Error('不在Chrome扩展环境中');
                }
                
                // 检查扩展ID
                const extensionId = chrome.runtime.id;
                log(`扩展ID: ${extensionId}`);
                
                // 检查权限
                const permissions = await chrome.permissions.getAll();
                log(`扩展权限: ${JSON.stringify(permissions)}`);
                
                // 检查与background的通信
                const response = await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('与background通信超时'));
                    }, 5000);
                    
                    chrome.runtime.sendMessage({
                        type: 'GET_CONNECTION_STATUS'
                    }, (response) => {
                        clearTimeout(timeout);
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                const status = `扩展状态正常
扩展ID: ${extensionId}
Background连接: ${response?.connected ? '已连接' : '未连接'}
权限: ${permissions.permissions?.join(', ') || '无'}`;
                
                showResult('extensionStatusResult', status, 'success');
                log('扩展状态检查完成', 'success');
                
            } catch (error) {
                const errorMsg = `扩展状态检查失败: ${error.message}`;
                showResult('extensionStatusResult', errorMsg, 'error');
                log(errorMsg, 'error');
            }
        }
        
        async function checkYunpanPage() {
            log('开始检测云盘页面...');
            showResult('yunpanPageResult', '检测中...', 'info');
            
            try {
                const response = await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('检测超时'));
                    }, 10000);
                    
                    chrome.runtime.sendMessage({
                        type: 'CHECK_YUNPAN_PAGE'
                    }, (response) => {
                        clearTimeout(timeout);
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                if (response.available) {
                    const status = `云盘页面状态: 可用
页面URL: ${response.tabUrl || '未知'}
标签页ID: ${response.tabId || '未知'}
脚本响应: ${JSON.stringify(response.scriptResponse)}`;
                    
                    showResult('yunpanPageResult', status, 'success');
                    log('云盘页面检测成功', 'success');
                } else {
                    const status = `云盘页面状态: 不可用
原因: ${response.message}
建议: ${response.details?.suggestion || '请手动打开云盘页面'}`;
                    
                    showResult('yunpanPageResult', status, 'error');
                    log(`云盘页面不可用: ${response.message}`, 'warn');
                }
                
            } catch (error) {
                const errorMsg = `云盘页面检测失败: ${error.message}`;
                showResult('yunpanPageResult', errorMsg, 'error');
                log(errorMsg, 'error');
            }
        }
        
        async function openYunpanPage() {
            log('尝试打开云盘页面...');
            try {
                const newTab = await chrome.tabs.create({
                    url: 'https://yunpan.gdcourts.gov.cn/',
                    active: true
                });
                log(`云盘页面已打开，标签页ID: ${newTab.id}`, 'success');
                showResult('yunpanPageResult', '云盘页面已打开，请等待页面加载完成后重新检测', 'info');
            } catch (error) {
                const errorMsg = `打开云盘页面失败: ${error.message}`;
                log(errorMsg, 'error');
                showResult('yunpanPageResult', errorMsg, 'error');
            }
        }
        
        async function getFileList() {
            log('开始获取文件列表...');
            showResult('fileListResult', '获取中...', 'info');
            
            try {
                const response = await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('获取文件列表超时'));
                    }, 10000);
                    
                    chrome.runtime.sendMessage({
                        type: 'GET_ADMIN_TREE'
                    }, (response) => {
                        clearTimeout(timeout);
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                if (response.success && response.tree) {
                    const tree = response.tree;
                    const fileCount = countFiles(tree);
                    const folderCount = countFolders(tree);
                    
                    const status = `文件列表获取成功
文件数量: ${fileCount}
文件夹数量: ${folderCount}
数据结构: ${JSON.stringify(tree, null, 2).substring(0, 500)}...`;
                    
                    showResult('fileListResult', status, 'success');
                    log(`文件列表获取成功：${fileCount}个文件，${folderCount}个文件夹`, 'success');
                } else {
                    const errorMsg = `文件列表获取失败: ${response.error || '未知错误'}`;
                    showResult('fileListResult', errorMsg, 'error');
                    log(errorMsg, 'warn');
                }
                
            } catch (error) {
                const errorMsg = `获取文件列表失败: ${error.message}`;
                showResult('fileListResult', errorMsg, 'error');
                log(errorMsg, 'error');
            }
        }
        
        function countFiles(tree) {
            if (!tree) return 0;
            let count = 0;
            if (tree.files && Array.isArray(tree.files)) {
                count += tree.files.length;
            }
            if (tree.folders && Array.isArray(tree.folders)) {
                tree.folders.forEach(folder => {
                    count += countFiles(folder.children);
                });
            }
            return count;
        }
        
        function countFolders(tree) {
            if (!tree) return 0;
            let count = 0;
            if (tree.folders && Array.isArray(tree.folders)) {
                count += tree.folders.length;
                tree.folders.forEach(folder => {
                    count += countFolders(folder.children);
                });
            }
            return count;
        }
        
        async function simulateUpload() {
            log('开始模拟文件上传...');
            showResult('uploadResult', '上传中...', 'info');
            
            try {
                // 创建模拟文件数据
                const mockFiles = [
                    {
                        name: '测试文件1.txt',
                        path: '测试文件1.txt',
                        size: 1024,
                        type: 'txt'
                    },
                    {
                        name: '测试图片.jpg',
                        path: '测试图片.jpg',
                        size: 2048,
                        type: 'jpg'
                    }
                ];
                
                log(`准备上传 ${mockFiles.length} 个模拟文件`);
                
                const response = await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('上传超时（60秒）'));
                    }, 60000);
                    
                    chrome.runtime.sendMessage({
                        type: 'START_YUNPAN_UPLOAD',
                        data: {
                            files: mockFiles
                        }
                    }, (response) => {
                        clearTimeout(timeout);
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                if (response.success) {
                    const stats = response.result;
                    const status = `模拟上传完成
总计: ${stats.total}
成功: ${stats.completed}
失败: ${stats.failed}
结果: ${JSON.stringify(response, null, 2)}`;
                    
                    showResult('uploadResult', status, 'success');
                    log(`模拟上传完成：成功${stats.completed}个，失败${stats.failed}个`, 'success');
                } else {
                    const errorMsg = `模拟上传失败: ${response.error}
详情: ${JSON.stringify(response.details || {}, null, 2)}`;
                    
                    showResult('uploadResult', errorMsg, 'error');
                    log(`模拟上传失败: ${response.error}`, 'error');
                }
                
            } catch (error) {
                const errorMsg = `模拟上传异常: ${error.message}`;
                showResult('uploadResult', errorMsg, 'error');
                log(errorMsg, 'error');
            }
        }
        
        // 页面加载时自动开始基础检查
        window.addEventListener('load', () => {
            log('云盘上传功能测试页面已加载');
            log('建议按顺序执行测试：1→2→3→4');
            
            // 自动执行扩展状态检查
            setTimeout(checkExtensionStatus, 1000);
        });
    </script>
</body>
</html> 
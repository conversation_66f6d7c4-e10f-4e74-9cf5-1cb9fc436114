// 定时器冲突修复测试脚本

console.log('🔧 开始定时器冲突修复测试...');

// 1. 测试自动刷新设置不会重复重置
async function testAutoRefreshNoReset() {
    console.log('\n=== 测试自动刷新不重复重置 ===');
    
    // 第一次设置60秒自动刷新
    console.log('🔧 第一次设置60秒自动刷新...');
    const response1 = await new Promise((resolve) => {
        chrome.runtime.sendMessage({ 
            type: 'SET_AUTO_REFRESH', 
            interval: 60 
        }, resolve);
    });
    
    console.log('第一次设置结果:', response1);
    
    // 等待2秒
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 获取状态
    const status1 = await new Promise((resolve) => {
        chrome.runtime.sendMessage({ type: 'GET_AUTO_REFRESH_STATUS' }, resolve);
    });
    
    console.log('第一次设置后状态:', status1);
    const firstTimerId = status1?.timerId;
    
    // 第二次设置相同的60秒自动刷新（应该不会重置）
    console.log('🔧 第二次设置相同的60秒自动刷新...');
    const response2 = await new Promise((resolve) => {
        chrome.runtime.sendMessage({ 
            type: 'SET_AUTO_REFRESH', 
            interval: 60 
        }, resolve);
    });
    
    console.log('第二次设置结果:', response2);
    
    // 等待2秒
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 再次获取状态
    const status2 = await new Promise((resolve) => {
        chrome.runtime.sendMessage({ type: 'GET_AUTO_REFRESH_STATUS' }, resolve);
    });
    
    console.log('第二次设置后状态:', status2);
    const secondTimerId = status2?.timerId;
    
    // 分析结果
    if (firstTimerId && secondTimerId && firstTimerId === secondTimerId) {
        console.log('✅ 定时器未被重置，修复成功！');
        return true;
    } else {
        console.log('❌ 定时器被重置，仍有问题');
        console.log(`  第一次定时器ID: ${firstTimerId}`);
        console.log(`  第二次定时器ID: ${secondTimerId}`);
        return false;
    }
}

// 2. 测试WebSocket重连功能
async function testWebSocketReconnection() {
    console.log('\n=== 测试WebSocket重连功能 ===');
    
    // 检查当前连接状态
    const status1 = await new Promise((resolve) => {
        chrome.runtime.sendMessage({ type: 'GET_CONNECTION_STATUS' }, resolve);
    });
    
    console.log('当前连接状态:', status1);
    
    if (status1?.connected) {
        console.log('✅ 当前已连接');
        
        // 测试重置连接
        console.log('🔧 测试重置连接...');
        const resetResult = await new Promise((resolve) => {
            chrome.runtime.sendMessage({ type: 'RESET_CONNECTION' }, resolve);
        });
        
        console.log('重置连接结果:', resetResult);
        
        // 等待3秒让连接重新建立
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 再次检查连接状态
        const status2 = await new Promise((resolve) => {
            chrome.runtime.sendMessage({ type: 'GET_CONNECTION_STATUS' }, resolve);
        });
        
        console.log('重置后连接状态:', status2);
        
        if (status2?.connected) {
            console.log('✅ 重连成功');
            return true;
        } else {
            console.log('❌ 重连失败');
            return false;
        }
    } else {
        console.log('❌ 当前未连接，无法测试重连');
        return false;
    }
}

// 3. 测试面板连接监控
function testPanelConnectionMonitoring() {
    console.log('\n=== 测试面板连接监控 ===');
    
    if (window.ylzInjectedPanel) {
        console.log('✅ 面板实例存在');
        
        // 检查是否有连接监控定时器
        if (window.ylzInjectedPanel.connectionMonitorTimer) {
            console.log('✅ 连接监控定时器已启动');
            return true;
        } else {
            console.log('❌ 连接监控定时器未启动');
            return false;
        }
    } else {
        console.log('❌ 面板实例不存在');
        return false;
    }
}

// 4. 测试UI设置不会干扰定时器
async function testUISettingNoInterference() {
    console.log('\n=== 测试UI设置不干扰定时器 ===');
    
    // 设置60秒自动刷新
    console.log('🔧 通过后台设置60秒自动刷新...');
    await new Promise((resolve) => {
        chrome.runtime.sendMessage({ 
            type: 'SET_AUTO_REFRESH', 
            interval: 60 
        }, resolve);
    });
    
    // 获取初始状态
    const initialStatus = await new Promise((resolve) => {
        chrome.runtime.sendMessage({ type: 'GET_AUTO_REFRESH_STATUS' }, resolve);
    });
    
    console.log('初始状态:', initialStatus);
    const initialTimerId = initialStatus?.timerId;
    
    // 通过UI设置相同的间隔
    const autoRefreshSelect = document.querySelector('#ylz-autoRefreshSelect');
    if (autoRefreshSelect) {
        console.log('🎛️ 通过UI设置相同的60秒间隔...');
        autoRefreshSelect.value = '60';
        autoRefreshSelect.dispatchEvent(new Event('change'));
        
        // 等待2秒
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 再次获取状态
        const afterUIStatus = await new Promise((resolve) => {
            chrome.runtime.sendMessage({ type: 'GET_AUTO_REFRESH_STATUS' }, resolve);
        });
        
        console.log('UI设置后状态:', afterUIStatus);
        const afterUITimerId = afterUIStatus?.timerId;
        
        if (initialTimerId && afterUITimerId && initialTimerId === afterUITimerId) {
            console.log('✅ UI设置未干扰定时器，修复成功！');
            return true;
        } else {
            console.log('❌ UI设置干扰了定时器');
            console.log(`  初始定时器ID: ${initialTimerId}`);
            console.log(`  UI设置后定时器ID: ${afterUITimerId}`);
            return false;
        }
    } else {
        console.log('❌ 未找到UI选择器');
        return false;
    }
}

// 5. 完整的修复验证测试
async function fullFixVerificationTest() {
    console.log('🚀 开始完整修复验证测试...\n');
    
    try {
        // 1. 测试自动刷新不重复重置
        const noResetOK = await testAutoRefreshNoReset();
        
        // 2. 测试WebSocket重连
        const reconnectOK = await testWebSocketReconnection();
        
        // 3. 测试面板连接监控
        const monitoringOK = testPanelConnectionMonitoring();
        
        // 4. 测试UI设置不干扰定时器
        const noInterferenceOK = await testUISettingNoInterference();
        
        // 生成测试报告
        console.log('\n📋 === 修复验证报告 ===');
        console.log(`✅ 自动刷新不重复重置: ${noResetOK ? '通过' : '失败'}`);
        console.log(`✅ WebSocket重连功能: ${reconnectOK ? '通过' : '失败'}`);
        console.log(`✅ 面板连接监控: ${monitoringOK ? '通过' : '失败'}`);
        console.log(`✅ UI设置不干扰定时器: ${noInterferenceOK ? '通过' : '失败'}`);
        
        const allOK = noResetOK && reconnectOK && monitoringOK && noInterferenceOK;
        
        if (allOK) {
            console.log('\n🎉 所有修复验证通过！');
            console.log('💡 现在可以正常使用自动刷新功能了');
            
            // 设置一个短间隔进行最终测试
            console.log('\n🧪 设置30秒自动刷新进行最终测试...');
            
            // 确保勾选"刷新当前页面"
            const allowActiveRefreshCheckbox = document.querySelector('#ylz-allowActiveRefresh');
            if (allowActiveRefreshCheckbox && !allowActiveRefreshCheckbox.checked) {
                allowActiveRefreshCheckbox.click();
            }
            
            // 设置30秒刷新
            const autoRefreshSelect = document.querySelector('#ylz-autoRefreshSelect');
            if (autoRefreshSelect) {
                autoRefreshSelect.value = '60'; // 使用60秒便于观察
                autoRefreshSelect.dispatchEvent(new Event('change'));
                
                console.log('✅ 已设置60秒自动刷新 + 允许刷新当前页面');
                console.log('⏰ 请等待60秒观察页面是否自动刷新');
            }
            
        } else {
            console.log('\n⚠️ 部分修复验证失败，需要进一步检查');
        }
        
        return allOK;
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
        return false;
    }
}

// 6. 快速设置测试
function quickSetupTest() {
    console.log('\n⚡ 快速设置测试...');
    
    // 勾选"刷新当前页面"
    const allowActiveRefreshCheckbox = document.querySelector('#ylz-allowActiveRefresh');
    if (allowActiveRefreshCheckbox && !allowActiveRefreshCheckbox.checked) {
        console.log('✅ 勾选"刷新当前页面"');
        allowActiveRefreshCheckbox.click();
    }
    
    // 设置60秒自动刷新
    const autoRefreshSelect = document.querySelector('#ylz-autoRefreshSelect');
    if (autoRefreshSelect) {
        console.log('🔧 设置60秒自动刷新');
        autoRefreshSelect.value = '60';
        autoRefreshSelect.dispatchEvent(new Event('change'));
        
        console.log('✅ 快速设置完成');
        console.log('⏰ 60秒后页面应该自动刷新');
        
        // 启动倒计时
        let countdown = 60;
        const countdownTimer = setInterval(() => {
            countdown--;
            if (countdown > 0) {
                console.log(`⏰ 倒计时: ${countdown}秒`);
            } else {
                console.log('⏰ 时间到！检查页面是否刷新...');
                clearInterval(countdownTimer);
            }
        }, 1000);
        
    } else {
        console.log('❌ 未找到自动刷新选择器');
    }
}

// 导出函数
window.testAutoRefreshNoReset = testAutoRefreshNoReset;
window.testWebSocketReconnection = testWebSocketReconnection;
window.testPanelConnectionMonitoring = testPanelConnectionMonitoring;
window.testUISettingNoInterference = testUISettingNoInterference;
window.fullFixVerificationTest = fullFixVerificationTest;
window.quickSetupTest = quickSetupTest;

console.log('✅ 定时器冲突修复测试脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - fullFixVerificationTest() - 完整修复验证');
console.log('  - quickSetupTest() - 快速设置并开始倒计时');

// 自动运行完整验证
fullFixVerificationTest();

// 文件树组件测试脚本

console.log('🌳 开始文件树组件测试...');

// 1. 检查文件树组件
function checkTreeComponent() {
    console.log('\n=== 检查文件树组件 ===');
    
    if (window.InjectedPanelTree) {
        console.log('✅ InjectedPanelTree 类存在');
        
        // 检查方法
        const prototype = window.InjectedPanelTree.prototype;
        const methods = ['render', 'refresh', 'formatFileSize'];
        
        methods.forEach(method => {
            if (typeof prototype[method] === 'function') {
                console.log(`✅ ${method} 方法存在`);
            } else {
                console.log(`❌ ${method} 方法缺失`);
            }
        });
        
        return true;
    } else {
        console.log('❌ InjectedPanelTree 类不存在');
        return false;
    }
}

// 2. 测试文件树渲染
function testTreeRendering() {
    console.log('\n=== 测试文件树渲染 ===');
    
    if (!window.InjectedPanelTree) {
        console.log('❌ 文件树组件不存在，无法测试');
        return;
    }
    
    // 创建测试容器
    const testContainer = document.createElement('div');
    testContainer.id = 'test-tree-container';
    testContainer.style.cssText = `
        position: fixed;
        top: 100px;
        right: 100px;
        width: 300px;
        height: 200px;
        background: white;
        border: 2px solid #007bff;
        border-radius: 8px;
        padding: 10px;
        z-index: 9999;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    
    // 添加标题
    const title = document.createElement('div');
    title.textContent = '🧪 文件树测试';
    title.style.cssText = 'font-weight: bold; margin-bottom: 10px; color: #007bff;';
    testContainer.appendChild(title);
    
    // 创建树容器
    const treeContainer = document.createElement('div');
    treeContainer.style.cssText = 'height: 150px; overflow: auto; border: 1px solid #ddd; border-radius: 4px;';
    testContainer.appendChild(treeContainer);
    
    // 添加关闭按钮
    const closeBtn = document.createElement('button');
    closeBtn.textContent = '关闭测试';
    closeBtn.style.cssText = `
        position: absolute;
        top: 5px;
        right: 5px;
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 3px;
        padding: 2px 6px;
        font-size: 12px;
        cursor: pointer;
    `;
    closeBtn.onclick = () => testContainer.remove();
    testContainer.appendChild(closeBtn);
    
    document.body.appendChild(testContainer);
    
    // 创建测试数据
    const testFiles = [
        { name: '测试文档.pdf', type: 'file', size: 1024000 },
        { name: '图片文件.jpg', type: 'file', size: 512000 },
        { name: '测试文件夹', type: 'folder' },
        { name: '数据表格.xlsx', type: 'file', size: 2048000 }
    ];
    
    try {
        // 创建文件树实例
        const tree = new window.InjectedPanelTree(treeContainer, {
            showThumbnails: true,
            allowSelection: true,
            expandable: true
        });
        
        console.log('✅ 文件树实例创建成功');
        
        // 测试render方法
        tree.render(testFiles);
        console.log('✅ render方法调用成功');
        
        // 测试refresh方法
        setTimeout(() => {
            tree.refresh([...testFiles, { name: '新增文件.txt', type: 'file', size: 1024 }]);
            console.log('✅ refresh方法调用成功');
        }, 2000);
        
        // 测试空数据
        setTimeout(() => {
            tree.render([]);
            console.log('✅ 空数据渲染成功');
        }, 4000);
        
        // 恢复原数据
        setTimeout(() => {
            tree.render(testFiles);
            console.log('✅ 数据恢复成功');
        }, 6000);
        
        // 自动关闭测试窗口
        setTimeout(() => {
            testContainer.remove();
            console.log('🧪 文件树测试完成');
        }, 10000);
        
    } catch (error) {
        console.error('❌ 文件树测试失败:', error);
        testContainer.remove();
    }
}

// 3. 检查面板中的文件树
function checkPanelTrees() {
    console.log('\n=== 检查面板中的文件树 ===');
    
    const containers = [
        { id: 'ylz-pendingTreeContainer', name: '未上传文件树' },
        { id: 'ylz-uploadedTreeContainer', name: '已上传文件树' }
    ];
    
    containers.forEach(container => {
        const element = document.getElementById(container.id);
        if (element) {
            console.log(`📁 ${container.name}:`);
            console.log(`  容器存在: ✅`);
            console.log(`  内容: ${element.innerHTML ? '有内容' : '空'}`);
            console.log(`  子元素数量: ${element.children.length}`);
            
            // 检查是否有文件项
            const fileItems = element.querySelectorAll('[style*="border-bottom"]');
            if (fileItems.length > 0) {
                console.log(`  文件项数量: ${fileItems.length}`);
                console.log(`  第一个文件: ${fileItems[0].textContent.trim()}`);
            } else {
                console.log(`  文件项: 无`);
            }
        } else {
            console.log(`❌ ${container.name} 容器不存在`);
        }
    });
}

// 4. 测试面板树实例
function testPanelTreeInstances() {
    console.log('\n=== 测试面板树实例 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return;
    }
    
    const panel = window.ylzInjectedPanel;
    
    if (panel.treeTrees) {
        console.log('✅ treeTrees 对象存在');
        
        const types = ['pending', 'uploaded'];
        types.forEach(type => {
            if (panel.treeTrees[type]) {
                console.log(`✅ ${type} 树实例存在`);
                
                // 检查方法
                const instance = panel.treeTrees[type];
                if (typeof instance.render === 'function') {
                    console.log(`  ✅ ${type} render方法可用`);
                } else {
                    console.log(`  ❌ ${type} render方法缺失`);
                }
                
                if (typeof instance.refresh === 'function') {
                    console.log(`  ✅ ${type} refresh方法可用`);
                } else {
                    console.log(`  ❌ ${type} refresh方法缺失`);
                }
            } else {
                console.log(`❌ ${type} 树实例不存在`);
            }
        });
    } else {
        console.log('❌ treeTrees 对象不存在');
    }
}

// 5. 修复面板树实例
function fixPanelTrees() {
    console.log('\n=== 修复面板树实例 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在，无法修复');
        return;
    }
    
    const panel = window.ylzInjectedPanel;
    
    // 重新初始化树实例
    if (panel.treeTrees) {
        console.log('🔄 重新初始化树实例...');
        
        const types = ['pending', 'uploaded'];
        types.forEach(type => {
            const containerId = type === 'pending' ? 'ylz-pendingTreeContainer' : 'ylz-uploadedTreeContainer';
            const container = document.getElementById(containerId);
            
            if (container && window.InjectedPanelTree) {
                try {
                    panel.treeTrees[type] = new window.InjectedPanelTree(container, {
                        showThumbnails: true,
                        allowSelection: true,
                        expandable: true
                    });
                    
                    console.log(`✅ ${type} 树实例重新创建成功`);
                    
                    // 绑定事件
                    container.addEventListener('itemClick', (e) => {
                        console.log(`📁 ${type} 文件项点击:`, e.detail.item);
                    });
                    
                } catch (error) {
                    console.error(`❌ ${type} 树实例创建失败:`, error);
                }
            }
        });
        
        console.log('✅ 树实例修复完成');
        
        // 重新渲染数据
        setTimeout(() => {
            if (panel.fileData) {
                console.log('🔄 重新渲染文件数据...');
                panel.updateFileTree(panel.fileData);
            }
        }, 1000);
    }
}

// 6. 主测试函数
function treeComponentTest() {
    console.log('🚀 开始文件树组件测试...\n');
    
    const componentExists = checkTreeComponent();
    
    if (componentExists) {
        checkPanelTrees();
        testPanelTreeInstances();
        
        // 如果有问题，尝试修复
        setTimeout(() => {
            fixPanelTrees();
        }, 1000);
        
        // 运行渲染测试
        setTimeout(() => {
            testTreeRendering();
        }, 2000);
    } else {
        console.log('❌ 文件树组件不存在，无法进行测试');
    }
}

// 导出函数
window.treeComponentTest = treeComponentTest;
window.checkTreeComponent = checkTreeComponent;
window.testTreeRendering = testTreeRendering;
window.checkPanelTrees = checkPanelTrees;
window.testPanelTreeInstances = testPanelTreeInstances;
window.fixPanelTrees = fixPanelTrees;

console.log('✅ 文件树组件测试脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - treeComponentTest() - 运行完整测试');
console.log('  - fixPanelTrees() - 修复面板树实例');
console.log('  - testTreeRendering() - 测试渲染功能');

// 自动运行测试
treeComponentTest();

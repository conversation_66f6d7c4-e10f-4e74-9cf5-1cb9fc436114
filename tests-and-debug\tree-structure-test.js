// 树状结构显示测试脚本

console.log('🌳 开始树状结构显示测试...');

// 1. 检查当前文件树数据结构
function checkCurrentTreeData() {
    console.log('\n=== 检查当前文件树数据结构 ===');
    
    if (window.ylzInjectedPanel) {
        const fileData = window.ylzInjectedPanel.fileData;
        console.log('📊 当前文件数据:', fileData);
        
        if (fileData.pending) {
            console.log('📁 待上传树结构:', fileData.pending);
            console.log(`  文件夹数量: ${fileData.pending.folders?.length || 0}`);
            console.log(`  文件数量: ${fileData.pending.files?.length || 0}`);
        }
        
        if (fileData.uploaded) {
            console.log('📁 已上传树结构:', fileData.uploaded);
            console.log(`  文件夹数量: ${fileData.uploaded.folders?.length || 0}`);
            console.log(`  文件数量: ${fileData.uploaded.files?.length || 0}`);
        }
        
        return fileData;
    } else {
        console.log('❌ 面板实例不存在');
        return null;
    }
}

// 2. 检查容器渲染状态
function checkContainerRendering() {
    console.log('\n=== 检查容器渲染状态 ===');
    
    const panel = document.getElementById('ylz-injected-panel');
    if (!panel) {
        console.log('❌ 面板不存在');
        return false;
    }
    
    const pendingContainer = panel.querySelector('#ylz-pendingTreeContainer');
    const uploadedContainer = panel.querySelector('#ylz-uploadedTreeContainer');
    
    console.log('📁 容器状态:');
    console.log(`  待上传容器: ${pendingContainer ? '存在' : '不存在'}`);
    console.log(`  已上传容器: ${uploadedContainer ? '存在' : '不存在'}`);
    
    if (pendingContainer) {
        const items = pendingContainer.querySelectorAll('div[style*="padding"]');
        console.log(`  待上传容器项目数: ${items.length}`);
        console.log(`  待上传容器HTML长度: ${pendingContainer.innerHTML.length}`);
        
        // 检查是否有缩进（树状结构的标志）
        const indentedItems = Array.from(items).filter(item => 
            item.style.paddingLeft && parseInt(item.style.paddingLeft) > 12
        );
        console.log(`  有缩进的项目数: ${indentedItems.length}`);
    }
    
    if (uploadedContainer) {
        const items = uploadedContainer.querySelectorAll('div[style*="padding"]');
        console.log(`  已上传容器项目数: ${items.length}`);
        console.log(`  已上传容器HTML长度: ${uploadedContainer.innerHTML.length}`);
        
        // 检查是否有缩进
        const indentedItems = Array.from(items).filter(item => 
            item.style.paddingLeft && parseInt(item.style.paddingLeft) > 12
        );
        console.log(`  有缩进的项目数: ${indentedItems.length}`);
    }
    
    return { pendingContainer, uploadedContainer };
}

// 3. 强制重新渲染文件树
async function forceRerenderTree() {
    console.log('\n=== 强制重新渲染文件树 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return false;
    }
    
    try {
        // 获取最新的文件树数据
        const response = await new Promise((resolve) => {
            chrome.runtime.sendMessage({ type: 'GET_ADMIN_TREE' }, resolve);
        });
        
        console.log('📡 获取到的文件树数据:', response);
        
        if (response && response.success && response.tree) {
            console.log('🔄 重新渲染文件树...');
            window.ylzInjectedPanel.updateFileTree(response.tree);
            
            // 等待渲染完成
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            console.log('✅ 重新渲染完成');
            return true;
        } else {
            console.log('❌ 获取文件树数据失败');
            return false;
        }
    } catch (error) {
        console.error('❌ 重新渲染失败:', error);
        return false;
    }
}

// 4. 模拟popup的树状结构数据
function createMockTreeData() {
    console.log('\n=== 创建模拟树状结构数据 ===');
    
    const mockData = {
        files: [
            {
                name: "2024-03-25-np8i3j.pdf",
                filename: "2024-03-25-np8i3j.pdf",
                size: 783584,
                syncStatus: "已同步",
                type: "pdf"
            }
        ],
        folders: [
            {
                name: "临时上传入口-30分钟更新",
                filename: "临时上传入口-30分钟更新",
                isFolder: true,
                isPortal: true,
                fileCount: 2,
                children: {
                    files: [
                        {
                            name: "IMG_4934.png",
                            filename: "IMG_4934.png",
                            size: 1150000,
                            syncStatus: "等待中",
                            type: "image"
                        }
                    ],
                    folders: [
                        {
                            name: "684d3655-2401-4fee-b443-77ce23e6145c",
                            filename: "684d3655-2401-4fee-b443-77ce23e6145c",
                            isFolder: true,
                            fileCount: 1,
                            children: {
                                files: [
                                    {
                                        name: "微信图片_20250722140544.jpg",
                                        filename: "微信图片_20250722140544.jpg",
                                        size: 808000,
                                        syncStatus: "等待中",
                                        type: "image"
                                    }
                                ],
                                folders: []
                            }
                        }
                    ]
                }
            },
            {
                name: "96小时更新文件上传入口",
                filename: "96小时更新文件上传入口",
                isFolder: true,
                isPortal: true,
                fileCount: 14,
                children: {
                    files: [],
                    folders: []
                }
            }
        ]
    };
    
    console.log('📊 模拟数据结构:', mockData);
    return mockData;
}

// 5. 测试树状结构渲染
async function testTreeStructureRendering() {
    console.log('\n=== 测试树状结构渲染 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return false;
    }
    
    try {
        // 使用模拟数据测试
        const mockData = createMockTreeData();
        
        console.log('🧪 使用模拟数据测试渲染...');
        window.ylzInjectedPanel.updateFileTree(mockData);
        
        // 等待渲染完成
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 检查渲染结果
        const containers = checkContainerRendering();
        
        if (containers.pendingContainer || containers.uploadedContainer) {
            console.log('✅ 树状结构渲染测试完成');
            return true;
        } else {
            console.log('❌ 树状结构渲染失败');
            return false;
        }
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
        return false;
    }
}

// 6. 完整的树状结构测试
async function fullTreeStructureTest() {
    console.log('🚀 开始完整的树状结构测试...\n');
    
    try {
        // 1. 检查当前数据
        const currentData = checkCurrentTreeData();
        
        // 2. 检查容器状态
        const containerStatus = checkContainerRendering();
        
        // 3. 强制重新渲染
        const rerenderOK = await forceRerenderTree();
        
        // 4. 再次检查容器状态
        const newContainerStatus = checkContainerRendering();
        
        // 5. 测试模拟数据渲染
        const mockTestOK = await testTreeStructureRendering();
        
        // 6. 生成测试报告
        console.log('\n📋 === 树状结构测试报告 ===');
        console.log(`✅ 数据结构检查: ${currentData ? '正常' : '异常'}`);
        console.log(`✅ 容器状态检查: ${containerStatus.pendingContainer && containerStatus.uploadedContainer ? '正常' : '异常'}`);
        console.log(`✅ 重新渲染: ${rerenderOK ? '成功' : '失败'}`);
        console.log(`✅ 模拟数据测试: ${mockTestOK ? '成功' : '失败'}`);
        
        const allOK = currentData && containerStatus.pendingContainer && rerenderOK && mockTestOK;
        
        if (allOK) {
            console.log('\n🎉 树状结构显示功能正常！');
            console.log('💡 现在面板应该按照popup的目录树结构显示文件');
            console.log('📁 文件夹会显示层级关系和缩进');
            console.log('📄 文件会显示在对应的文件夹下');
        } else {
            console.log('\n⚠️ 树状结构显示存在问题');
            
            if (!currentData) {
                console.log('🔧 建议: 检查面板实例是否正确初始化');
            }
            if (!containerStatus.pendingContainer) {
                console.log('🔧 建议: 检查HTML容器是否存在');
            }
            if (!rerenderOK) {
                console.log('🔧 建议: 检查数据获取和渲染逻辑');
            }
            if (!mockTestOK) {
                console.log('🔧 建议: 检查树状结构渲染代码');
            }
        }
        
        return allOK;
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
        return false;
    }
}

// 7. 快速查看当前显示效果
function quickViewCurrentDisplay() {
    console.log('\n👀 快速查看当前显示效果...');
    
    const panel = document.getElementById('ylz-injected-panel');
    if (!panel) {
        console.log('❌ 面板不存在');
        return;
    }
    
    const pendingContainer = panel.querySelector('#ylz-pendingTreeContainer');
    const uploadedContainer = panel.querySelector('#ylz-uploadedTreeContainer');
    
    console.log('📁 当前显示内容:');
    
    if (pendingContainer && pendingContainer.innerHTML.length > 0) {
        console.log('📋 待上传内容:');
        const items = pendingContainer.querySelectorAll('div[style*="padding"]');
        items.forEach((item, index) => {
            const indent = item.style.paddingLeft ? parseInt(item.style.paddingLeft) : 12;
            const level = Math.max(0, (indent - 12) / 20);
            const prefix = '  '.repeat(level) + (level > 0 ? '└─ ' : '');
            const text = item.textContent.trim();
            console.log(`  ${prefix}${text}`);
        });
    } else {
        console.log('📋 待上传: 无内容');
    }
    
    if (uploadedContainer && uploadedContainer.innerHTML.length > 0) {
        console.log('📋 已上传内容:');
        const items = uploadedContainer.querySelectorAll('div[style*="padding"]');
        items.forEach((item, index) => {
            if (index < 10) { // 只显示前10项
                const indent = item.style.paddingLeft ? parseInt(item.style.paddingLeft) : 12;
                const level = Math.max(0, (indent - 12) / 20);
                const prefix = '  '.repeat(level) + (level > 0 ? '└─ ' : '');
                const text = item.textContent.trim();
                console.log(`  ${prefix}${text}`);
            }
        });
        if (items.length > 10) {
            console.log(`  ... 还有 ${items.length - 10} 项`);
        }
    } else {
        console.log('📋 已上传: 无内容');
    }
}

// 导出函数
window.checkCurrentTreeData = checkCurrentTreeData;
window.checkContainerRendering = checkContainerRendering;
window.forceRerenderTree = forceRerenderTree;
window.createMockTreeData = createMockTreeData;
window.testTreeStructureRendering = testTreeStructureRendering;
window.fullTreeStructureTest = fullTreeStructureTest;
window.quickViewCurrentDisplay = quickViewCurrentDisplay;

console.log('✅ 树状结构测试脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - fullTreeStructureTest() - 完整树状结构测试');
console.log('  - quickViewCurrentDisplay() - 快速查看当前显示');
console.log('  - forceRerenderTree() - 强制重新渲染');

// 自动运行快速查看
quickViewCurrentDisplay();

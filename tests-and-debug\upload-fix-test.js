// 上传修复测试脚本

console.log('🔧 开始上传修复测试...');

// 1. 测试上传请求数据格式
async function testUploadRequestFormat() {
    console.log('\n=== 测试上传请求数据格式 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return false;
    }
    
    const panel = window.ylzInjectedPanel;
    
    // 获取待上传文件列表
    const unuploadedFiles = panel.getUnuploadedFilesList();
    console.log('📁 待上传文件:', unuploadedFiles);
    
    if (unuploadedFiles.length === 0) {
        console.log('⚠️ 没有待上传文件，创建测试数据');
        // 创建测试文件数据
        const testFiles = [
            {
                name: 'test-file-1.txt',
                path: 'test-file-1.txt',
                size: 1024,
                type: 'text'
            },
            {
                name: 'test-file-2.jpg',
                path: 'test-file-2.jpg',
                size: 2048,
                type: 'image'
            }
        ];
        
        console.log('🧪 使用测试文件数据:', testFiles);
        return testFiles;
    }
    
    return unuploadedFiles;
}

// 2. 手动发送上传请求测试
async function testManualUploadRequest() {
    console.log('\n=== 手动发送上传请求测试 ===');
    
    const testFiles = await testUploadRequestFormat();
    
    if (!testFiles || testFiles.length === 0) {
        console.log('❌ 没有测试文件');
        return false;
    }
    
    // 构造上传请求
    const uploadRequest = {
        type: 'START_YUNPAN_UPLOAD',
        data: {
            files: testFiles,
            source: 'manual_test'
        }
    };
    
    console.log('📤 发送上传请求:', uploadRequest);
    
    try {
        const response = await new Promise((resolve) => {
            chrome.runtime.sendMessage(uploadRequest, resolve);
        });
        
        console.log('📥 上传响应:', response);
        
        if (response && response.success) {
            console.log('✅ 上传请求成功');
            return true;
        } else {
            console.log('❌ 上传请求失败:', response?.error);
            console.log('🔍 错误详情:', response?.details);
            return false;
        }
        
    } catch (error) {
        console.error('❌ 发送上传请求出错:', error);
        return false;
    }
}

// 3. 测试面板的safeRuntimeMessage方法
async function testSafeRuntimeMessage() {
    console.log('\n=== 测试面板的safeRuntimeMessage方法 ===');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return false;
    }
    
    const panel = window.ylzInjectedPanel;
    
    // 测试简单消息
    try {
        console.log('📤 测试简单消息...');
        const testResponse = await panel.safeRuntimeMessage({
            type: 'GET_CONNECTION_STATUS'
        });
        
        console.log('📥 简单消息响应:', testResponse);
        
        if (testResponse) {
            console.log('✅ safeRuntimeMessage方法工作正常');
        } else {
            console.log('⚠️ safeRuntimeMessage返回空响应');
        }
        
    } catch (error) {
        console.error('❌ safeRuntimeMessage测试失败:', error);
        return false;
    }
    
    // 测试上传消息
    const testFiles = await testUploadRequestFormat();
    
    if (testFiles && testFiles.length > 0) {
        try {
            console.log('📤 测试上传消息...');
            const uploadResponse = await panel.safeRuntimeMessage({
                type: 'START_YUNPAN_UPLOAD',
                data: {
                    files: testFiles,
                    source: 'safe_runtime_test'
                }
            });
            
            console.log('📥 上传消息响应:', uploadResponse);
            
            if (uploadResponse) {
                if (uploadResponse.success) {
                    console.log('✅ 上传消息测试成功');
                } else {
                    console.log('❌ 上传消息测试失败:', uploadResponse.error);
                    console.log('🔍 错误详情:', uploadResponse.details);
                }
            } else {
                console.log('⚠️ 上传消息返回空响应');
            }
            
        } catch (error) {
            console.error('❌ 上传消息测试出错:', error);
        }
    }
    
    return true;
}

// 4. 检查云盘页面状态
async function checkYunpanPageStatus() {
    console.log('\n=== 检查云盘页面状态 ===');
    
    try {
        const response = await new Promise((resolve) => {
            chrome.runtime.sendMessage({
                type: 'CHECK_YUNPAN_PAGE'
            }, resolve);
        });
        
        console.log('📊 云盘页面状态:', response);
        
        if (response && response.success) {
            console.log('✅ 云盘页面检查成功');
            console.log(`  页面可用: ${response.available ? '是' : '否'}`);
            console.log(`  页面URL: ${response.url || '未知'}`);
            return response.available;
        } else {
            console.log('❌ 云盘页面检查失败:', response?.error);
            return false;
        }
        
    } catch (error) {
        console.error('❌ 检查云盘页面状态出错:', error);
        return false;
    }
}

// 5. 完整的上传修复测试
async function fullUploadFixTest() {
    console.log('🚀 开始完整的上传修复测试...\n');
    
    try {
        // 1. 检查面板状态
        if (!window.ylzInjectedPanel) {
            console.log('❌ 面板实例不存在，无法进行测试');
            return false;
        }
        
        console.log('✅ 面板实例存在');
        
        // 2. 检查云盘页面状态
        const yunpanAvailable = await checkYunpanPageStatus();
        if (!yunpanAvailable) {
            console.log('⚠️ 云盘页面不可用，但继续测试请求格式');
        }
        
        // 3. 测试safeRuntimeMessage方法
        const safeMessageOK = await testSafeRuntimeMessage();
        if (!safeMessageOK) {
            console.log('❌ safeRuntimeMessage测试失败');
            return false;
        }
        
        // 4. 测试手动上传请求
        const manualRequestOK = await testManualUploadRequest();
        
        // 5. 生成测试报告
        console.log('\n📋 === 上传修复测试报告 ===');
        console.log(`✅ 面板实例: 正常`);
        console.log(`✅ 云盘页面: ${yunpanAvailable ? '可用' : '不可用'}`);
        console.log(`✅ 消息通信: ${safeMessageOK ? '正常' : '异常'}`);
        console.log(`✅ 上传请求: ${manualRequestOK ? '成功' : '失败'}`);
        
        if (manualRequestOK) {
            console.log('\n🎉 上传修复测试成功！');
            console.log('💡 现在可以尝试启用面板自动上传功能');
        } else {
            console.log('\n⚠️ 上传请求仍有问题');
            console.log('💡 建议检查后台日志和云盘页面状态');
        }
        
        return manualRequestOK;
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
        return false;
    }
}

// 6. 快速修复测试
async function quickFixTest() {
    console.log('\n⚡ 快速修复测试...');
    
    try {
        // 直接测试上传请求
        const result = await testManualUploadRequest();
        
        if (result) {
            console.log('🎉 快速测试成功！上传请求格式已修复');
        } else {
            console.log('❌ 快速测试失败，需要进一步调试');
        }
        
        return result;
        
    } catch (error) {
        console.error('❌ 快速测试出错:', error);
        return false;
    }
}

// 7. 调试上传请求数据
function debugUploadRequestData() {
    console.log('\n🔍 调试上传请求数据...');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return;
    }
    
    const panel = window.ylzInjectedPanel;
    
    // 检查文件数据
    console.log('📊 面板文件数据:');
    console.log('  pending:', panel.fileData.pending);
    console.log('  uploaded:', panel.fileData.uploaded);
    console.log('  pendingCount:', panel.fileData.pendingCount);
    console.log('  uploadedCount:', panel.fileData.uploadedCount);
    
    // 检查待上传文件列表
    const unuploadedFiles = panel.getUnuploadedFilesList();
    console.log('📁 待上传文件列表:', unuploadedFiles);
    
    // 检查自动上传状态
    console.log('🔄 自动上传状态:');
    console.log('  enabled:', panel.autoUploadEnabled);
    console.log('  uploadInProgress:', panel.uploadInProgress);
    console.log('  lastFileCount:', panel.lastFileCount);
}

// 导出函数
window.testUploadRequestFormat = testUploadRequestFormat;
window.testManualUploadRequest = testManualUploadRequest;
window.testSafeRuntimeMessage = testSafeRuntimeMessage;
window.checkYunpanPageStatus = checkYunpanPageStatus;
window.fullUploadFixTest = fullUploadFixTest;
window.quickFixTest = quickFixTest;
window.debugUploadRequestData = debugUploadRequestData;

console.log('✅ 上传修复测试脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - fullUploadFixTest() - 完整修复测试');
console.log('  - quickFixTest() - 快速修复测试');
console.log('  - debugUploadRequestData() - 调试请求数据');
console.log('  - testManualUploadRequest() - 手动测试上传请求');

// 自动运行快速测试
quickFixTest();

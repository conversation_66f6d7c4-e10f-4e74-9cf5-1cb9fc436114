// 上传状态修复脚本

console.log('🔧 开始修复上传状态问题...');

// 1. 检查当前上传状态
async function checkUploadState() {
    console.log('\n=== 检查当前上传状态 ===');
    
    try {
        const response = await new Promise((resolve) => {
            chrome.runtime.sendMessage({
                type: 'GET_UPLOAD_STATUS'
            }, resolve);
        });
        
        console.log('📊 当前上传状态:', response);
        
        if (response && response.success) {
            const status = response.status;
            console.log(`  上传中: ${status.isUploading ? '是' : '否'}`);
            console.log(`  总文件数: ${status.stats?.total || 0}`);
            console.log(`  已完成: ${status.stats?.completed || 0}`);
            console.log(`  失败数: ${status.stats?.failed || 0}`);
            console.log(`  当前路径: ${status.currentPath || '未知'}`);
            
            return status.isUploading;
        } else {
            console.log('❌ 获取上传状态失败');
            return false;
        }
        
    } catch (error) {
        console.error('❌ 检查上传状态出错:', error);
        return false;
    }
}

// 2. 强制重置上传状态
async function forceResetUploadState() {
    console.log('\n=== 强制重置上传状态 ===');
    
    // 发送重置消息到content script
    try {
        const response = await new Promise((resolve) => {
            chrome.runtime.sendMessage({
                type: 'RESET_UPLOAD_STATE'
            }, resolve);
        });
        
        console.log('🔄 重置响应:', response);
        
        if (response && response.success) {
            console.log('✅ 上传状态重置成功');
            return true;
        } else {
            console.log('❌ 上传状态重置失败');
            return false;
        }
        
    } catch (error) {
        console.error('❌ 重置上传状态出错:', error);
        return false;
    }
}

// 3. 直接操作页面重置状态（如果content script方法不可用）
function directResetPageState() {
    console.log('\n=== 直接重置页面状态 ===');
    
    try {
        // 发送自定义事件到页面
        const resetEvent = new CustomEvent('ylz_reset_upload_state', {
            detail: {
                timestamp: Date.now(),
                source: 'debug_script'
            }
        });
        
        window.dispatchEvent(resetEvent);
        console.log('✅ 重置事件已发送');
        
        return true;
        
    } catch (error) {
        console.error('❌ 直接重置失败:', error);
        return false;
    }
}

// 4. 等待上传状态清除
async function waitForUploadStateClear() {
    console.log('\n=== 等待上传状态清除 ===');
    
    let attempts = 0;
    const maxAttempts = 10;
    
    while (attempts < maxAttempts) {
        attempts++;
        console.log(`检查第 ${attempts}/${maxAttempts} 次...`);
        
        const isUploading = await checkUploadState();
        
        if (!isUploading) {
            console.log('✅ 上传状态已清除');
            return true;
        }
        
        console.log('⏰ 等待2秒后再次检查...');
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    console.log('❌ 上传状态未能清除');
    return false;
}

// 5. 测试单个文件上传
async function testSingleFileUpload() {
    console.log('\n=== 测试单个文件上传 ===');
    
    // 确保上传状态已清除
    const isUploading = await checkUploadState();
    if (isUploading) {
        console.log('⚠️ 上传状态未清除，先重置...');
        await forceResetUploadState();
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // 创建简单的测试文件
    const testFile = {
        name: 'upload-test.txt',
        fileName: 'upload-test.txt',
        path: 'upload-test.txt',
        size: 1024,
        type: 'text'
    };
    
    const uploadRequest = {
        type: 'START_YUNPAN_UPLOAD',
        data: {
            files: [testFile],
            source: 'single_file_test'
        }
    };
    
    console.log('📤 发送单文件上传请求:', uploadRequest);
    
    try {
        const response = await new Promise((resolve) => {
            chrome.runtime.sendMessage(uploadRequest, resolve);
        });
        
        console.log('📥 单文件上传响应:', response);
        
        if (response && response.success) {
            console.log('✅ 单文件上传成功');
            return true;
        } else {
            console.log('❌ 单文件上传失败:', response?.error);
            return false;
        }
        
    } catch (error) {
        console.error('❌ 单文件上传出错:', error);
        return false;
    }
}

// 6. 监控上传过程
function monitorUploadProcess() {
    console.log('\n=== 开始监控上传过程 ===');
    
    let monitorCount = 0;
    const maxMonitor = 30; // 监控30次（60秒）
    
    const monitor = setInterval(async () => {
        monitorCount++;
        
        console.log(`📊 监控 ${monitorCount}/${maxMonitor}:`);
        const isUploading = await checkUploadState();
        
        if (!isUploading && monitorCount > 1) {
            console.log('✅ 上传过程已完成');
            clearInterval(monitor);
        }
        
        if (monitorCount >= maxMonitor) {
            console.log('⏰ 监控超时，停止监控');
            clearInterval(monitor);
        }
    }, 2000);
    
    console.log('🔍 上传过程监控已启动');
    return monitor;
}

// 7. 完整的上传状态修复流程
async function fullUploadStateFix() {
    console.log('🚀 开始完整的上传状态修复流程...\n');
    
    try {
        // 1. 检查当前状态
        const initialUploading = await checkUploadState();
        
        if (initialUploading) {
            console.log('⚠️ 检测到上传状态异常，开始修复...');
            
            // 2. 尝试重置状态
            const resetOK = await forceResetUploadState();
            
            if (!resetOK) {
                console.log('🔧 尝试直接重置...');
                directResetPageState();
            }
            
            // 3. 等待状态清除
            const clearOK = await waitForUploadStateClear();
            
            if (!clearOK) {
                console.log('❌ 无法清除上传状态，可能需要重新加载页面');
                return false;
            }
        } else {
            console.log('✅ 上传状态正常');
        }
        
        // 4. 测试上传功能
        console.log('🧪 测试上传功能...');
        const testOK = await testSingleFileUpload();
        
        if (testOK) {
            console.log('🎉 上传状态修复成功！');
            
            // 5. 启动监控
            monitorUploadProcess();
            
            return true;
        } else {
            console.log('❌ 上传功能仍有问题');
            return false;
        }
        
    } catch (error) {
        console.error('❌ 修复过程中出现错误:', error);
        return false;
    }
}

// 8. 快速修复上传状态
async function quickFixUploadState() {
    console.log('\n⚡ 快速修复上传状态...');
    
    try {
        // 1. 强制重置
        await forceResetUploadState();
        directResetPageState();
        
        // 2. 等待2秒
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 3. 检查状态
        const isUploading = await checkUploadState();
        
        if (!isUploading) {
            console.log('🎉 快速修复成功！');
            
            // 4. 如果面板存在，重新启用自动上传
            if (window.ylzInjectedPanel) {
                const panel = window.ylzInjectedPanel;
                panel.uploadInProgress = false; // 重置面板状态
                
                console.log('🔄 重新启用面板自动上传...');
                if (panel.autoUploadEnabled && panel.fileData.pendingCount > 0) {
                    setTimeout(() => {
                        panel.triggerAutoUpload().catch(error => {
                            console.error('❌ 重新触发上传失败:', error);
                        });
                    }, 1000);
                }
            }
            
            return true;
        } else {
            console.log('❌ 快速修复失败');
            return false;
        }
        
    } catch (error) {
        console.error('❌ 快速修复出错:', error);
        return false;
    }
}

// 9. 重新启动面板自动上传
function restartPanelAutoUpload() {
    console.log('\n🔄 重新启动面板自动上传...');
    
    if (!window.ylzInjectedPanel) {
        console.log('❌ 面板实例不存在');
        return false;
    }
    
    const panel = window.ylzInjectedPanel;
    
    // 重置面板状态
    panel.uploadInProgress = false;
    panel.autoUploadEnabled = true;
    
    // 更新UI
    const autoUploadSwitch = document.querySelector('#ylz-autoUploadSwitch');
    if (autoUploadSwitch) {
        autoUploadSwitch.checked = true;
    }
    
    console.log('📊 面板状态:');
    console.log(`  自动上传启用: ${panel.autoUploadEnabled}`);
    console.log(`  上传进行中: ${panel.uploadInProgress}`);
    console.log(`  待上传文件数: ${panel.fileData.pendingCount || 0}`);
    
    // 如果有待上传文件，立即触发
    if (panel.fileData.pendingCount > 0) {
        console.log('🚀 立即触发自动上传...');
        setTimeout(() => {
            panel.triggerAutoUpload().then(() => {
                console.log('✅ 自动上传已重新启动');
            }).catch(error => {
                console.error('❌ 重新启动失败:', error);
            });
        }, 1000);
    } else {
        console.log('⚠️ 没有待上传文件');
    }
    
    return true;
}

// 导出函数
window.checkUploadState = checkUploadState;
window.forceResetUploadState = forceResetUploadState;
window.directResetPageState = directResetPageState;
window.waitForUploadStateClear = waitForUploadStateClear;
window.testSingleFileUpload = testSingleFileUpload;
window.monitorUploadProcess = monitorUploadProcess;
window.fullUploadStateFix = fullUploadStateFix;
window.quickFixUploadState = quickFixUploadState;
window.restartPanelAutoUpload = restartPanelAutoUpload;

console.log('✅ 上传状态修复脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - fullUploadStateFix() - 完整修复流程');
console.log('  - quickFixUploadState() - 快速修复');
console.log('  - restartPanelAutoUpload() - 重新启动面板自动上传');
console.log('  - checkUploadState() - 检查当前状态');

// 自动运行快速修复
quickFixUploadState();

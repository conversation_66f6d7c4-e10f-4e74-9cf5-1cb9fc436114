<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传功能调试工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f0f2f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #1890ff;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .log-area {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 10px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #f6ffed; border: 1px solid #b7eb8f; color: #52c41a; }
        .status.error { background: #fff1f0; border: 1px solid #ffa39e; color: #f5222d; }
        .status.warning { background: #fffbe6; border: 1px solid #ffd666; color: #fa8c16; }
        .status.info { background: #e6f7ff; border: 1px solid #91d5ff; color: #1890ff; }
        
        .file-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #d9d9d9;
            padding: 10px;
            background: white;
        }
        .file-item {
            padding: 5px;
            border-bottom: 1px solid #f0f0f0;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <h1>🚀 云盘上传功能调试工具</h1>
    
    <div class="debug-container">
        <div class="section">
            <h3>📋 系统状态检查</h3>
            <button onclick="checkExtensionStatus()">检查扩展状态</button>
            <button onclick="checkYunpanPage()">检查云盘页面</button>
            <button onclick="getFileList()">获取文件列表</button>
            <div id="status-display"></div>
        </div>
        
        <div class="section">
            <h3>📁 文件列表 (未上传)</h3>
            <div id="file-list" class="file-list">
                <div class="file-item">正在加载...</div>
            </div>
            <button onclick="refreshFileList()">刷新文件列表</button>
            <button onclick="testSingleUpload()" id="single-upload-btn" disabled>测试单文件上传</button>
        </div>
        
        <div class="section">
            <h3>☁️ 云盘上传测试</h3>
            <button onclick="testBatchUpload()">测试批量上传</button>
            <button onclick="simulateUpload()">模拟上传流程</button>
            <button onclick="clearLogs()">清空日志</button>
        </div>
    </div>
    
    <div class="debug-container">
        <h3>🔍 调试日志</h3>
        <div id="log-area" class="log-area"></div>
    </div>

    <script>
        let currentFiles = [];
        
        function log(message, type = 'info') {
            const logArea = document.getElementById('log-area');
            const time = new Date().toLocaleTimeString();
            const color = {
                info: '#00ff00',
                error: '#ff4444',
                warning: '#ffaa00',
                success: '#00aa00'
            }[type] || '#00ff00';
            
            logArea.innerHTML += `<div style="color: ${color}">[${time}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function showStatus(message, type = 'info') {
            const statusDisplay = document.getElementById('status-display');
            statusDisplay.innerHTML = `<div class="status ${type}">${message}</div>`;
            log(`状态: ${message}`, type);
        }
        
        async function checkExtensionStatus() {
            log('检查扩展状态...');
            try {
                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({ type: 'GET_CONNECTION_STATUS' }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                if (response && response.connected) {
                    showStatus('✅ 扩展运行正常，WebSocket已连接', 'success');
                } else {
                    showStatus('⚠️ WebSocket未连接', 'warning');
                }
            } catch (error) {
                showStatus(`❌ 扩展检查失败: ${error.message}`, 'error');
            }
        }
        
        async function checkYunpanPage() {
            log('检查云盘页面...');
            try {
                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({ type: 'CHECK_YUNPAN_PAGE' }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                if (response && response.available) {
                    showStatus(`✅ 云盘页面可用: ${response.tabUrl}`, 'success');
                } else {
                    showStatus(`❌ 云盘页面不可用: ${response.message}`, 'error');
                }
            } catch (error) {
                showStatus(`❌ 云盘页面检查失败: ${error.message}`, 'error');
            }
        }
        
        async function getFileList() {
            log('获取文件列表...');
            try {
                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({ type: 'GET_ADMIN_TREE' }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                if (response && response.success && response.tree) {
                    currentFiles = extractFiles(response.tree);
                    updateFileListDisplay();
                    showStatus(`✅ 获取到 ${currentFiles.length} 个文件`, 'success');
                } else {
                    showStatus('❌ 获取文件列表失败', 'error');
                }
            } catch (error) {
                showStatus(`❌ 获取文件列表异常: ${error.message}`, 'error');
            }
        }
        
        function extractFiles(tree, path = '') {
            let files = [];
            
            if (tree.files) {
                tree.files.forEach(file => {
                    const filePath = path ? `${path}/${file.name}` : file.name;
                    files.push({
                        name: file.name,
                        path: filePath,
                        size: file.size || 0,
                        type: getFileExtension(file.name)
                    });
                });
            }
            
            if (tree.folders) {
                tree.folders.forEach(folder => {
                    const folderPath = path ? `${path}/${folder.name}` : folder.name;
                    if (folder.children) {
                        files = files.concat(extractFiles(folder.children, folderPath));
                    }
                });
            }
            
            return files;
        }
        
        function getFileExtension(filename) {
            return filename.split('.').pop().toLowerCase();
        }
        
        function updateFileListDisplay() {
            const fileListEl = document.getElementById('file-list');
            const singleUploadBtn = document.getElementById('single-upload-btn');
            
            if (currentFiles.length === 0) {
                fileListEl.innerHTML = '<div class="file-item">没有文件</div>';
                singleUploadBtn.disabled = true;
            } else {
                fileListEl.innerHTML = currentFiles.slice(0, 10).map((file, index) => 
                    `<div class="file-item" onclick="selectFile(${index})" style="cursor: pointer; ${index === 0 ? 'background: #e6f7ff;' : ''}">${file.name} (${formatFileSize(file.size)})</div>`
                ).join('') + (currentFiles.length > 10 ? `<div class="file-item">... 还有 ${currentFiles.length - 10} 个文件</div>` : '');
                singleUploadBtn.disabled = false;
            }
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        let selectedFileIndex = 0;
        function selectFile(index) {
            selectedFileIndex = index;
            updateFileListDisplay();
            log(`选择文件: ${currentFiles[index].name}`);
        }
        
        async function testSingleUpload() {
            if (currentFiles.length === 0) {
                showStatus('没有可上传的文件', 'warning');
                return;
            }
            
            const file = currentFiles[selectedFileIndex];
            log(`开始测试单文件上传: ${file.name}`);
            
            try {
                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({
                        type: 'START_YUNPAN_UPLOAD',
                        data: { files: [file] }
                    }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                log(`上传响应: ${JSON.stringify(response, null, 2)}`);
                
                if (response && response.success) {
                    showStatus(`✅ 上传成功: ${file.name}`, 'success');
                } else {
                    showStatus(`❌ 上传失败: ${response.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showStatus(`❌ 上传异常: ${error.message}`, 'error');
            }
        }
        
        async function testBatchUpload() {
            if (currentFiles.length === 0) {
                showStatus('没有可上传的文件', 'warning');
                return;
            }
            
            // 限制批量上传数量，避免过载
            const filesToUpload = currentFiles.slice(0, 3);
            log(`开始测试批量上传: ${filesToUpload.length} 个文件`);
            
            try {
                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({
                        type: 'START_YUNPAN_UPLOAD',
                        data: { files: filesToUpload }
                    }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });
                
                log(`批量上传响应: ${JSON.stringify(response, null, 2)}`);
                
                if (response && response.success) {
                    showStatus(`✅ 批量上传成功`, 'success');
                } else {
                    showStatus(`❌ 批量上传失败: ${response.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showStatus(`❌ 批量上传异常: ${error.message}`, 'error');
            }
        }
        
        function simulateUpload() {
            log('模拟上传流程...');
            showStatus('🔄 模拟上传进行中...', 'info');
            
            setTimeout(() => {
                log('模拟: 检查云盘页面...');
            }, 500);
            
            setTimeout(() => {
                log('模拟: 创建日期文件夹...');
            }, 1000);
            
            setTimeout(() => {
                log('模拟: 上传文件...');
            }, 1500);
            
            setTimeout(() => {
                log('模拟: 上传完成');
                showStatus('✅ 模拟上传完成', 'success');
            }, 2000);
        }
        
        function refreshFileList() {
            getFileList();
        }
        
        function clearLogs() {
            document.getElementById('log-area').innerHTML = '';
            log('日志已清空');
        }
        
        // 页面加载时自动检查状态
        window.addEventListener('load', () => {
            log('上传调试工具已加载');
            checkExtensionStatus();
            getFileList();
        });
    </script>
</body>
</html> 
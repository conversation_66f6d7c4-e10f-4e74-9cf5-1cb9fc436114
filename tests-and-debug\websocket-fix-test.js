// WebSocket修复测试脚本

console.log('🔧 开始WebSocket修复测试...');

// 1. 测试获取连接状态
async function testGetConnectionStatus() {
    console.log('\n=== 测试获取连接状态 ===');
    
    return new Promise((resolve) => {
        chrome.runtime.sendMessage({ type: 'GET_CONNECTION_STATUS' }, (response) => {
            if (chrome.runtime.lastError) {
                console.log('❌ 获取连接状态失败:', chrome.runtime.lastError.message);
                resolve(false);
            } else {
                console.log('✅ 获取连接状态成功:', response);
                console.log(`  连接状态: ${response.connected ? '已连接' : '未连接'}`);
                console.log(`  WebSocket状态: ${response.readyState}`);
                resolve(response.connected);
            }
        });
    });
}

// 2. 测试重置连接
async function testResetConnection() {
    console.log('\n=== 测试重置连接 ===');
    
    return new Promise((resolve) => {
        chrome.runtime.sendMessage({ type: 'RESET_CONNECTION' }, (response) => {
            if (chrome.runtime.lastError) {
                console.log('❌ 重置连接失败:', chrome.runtime.lastError.message);
                resolve(false);
            } else {
                console.log('✅ 重置连接响应:', response);
                if (response.success) {
                    console.log('✅ 连接重置成功');
                    resolve(true);
                } else {
                    console.log('❌ 连接重置失败:', response.error);
                    resolve(false);
                }
            }
        });
    });
}

// 3. 测试自动刷新设置
async function testAutoRefreshSetting() {
    console.log('\n=== 测试自动刷新设置 ===');
    
    return new Promise((resolve) => {
        chrome.runtime.sendMessage({ 
            type: 'SET_AUTO_REFRESH', 
            interval: 60 
        }, (response) => {
            if (chrome.runtime.lastError) {
                console.log('❌ 设置自动刷新失败:', chrome.runtime.lastError.message);
                resolve(false);
            } else {
                console.log('✅ 设置自动刷新响应:', response);
                if (response.success) {
                    console.log('✅ 自动刷新设置成功');
                    resolve(true);
                } else {
                    console.log('❌ 自动刷新设置失败:', response.error);
                    resolve(false);
                }
            }
        });
    });
}

// 4. 测试获取自动刷新状态
async function testGetAutoRefreshStatus() {
    console.log('\n=== 测试获取自动刷新状态 ===');
    
    return new Promise((resolve) => {
        chrome.runtime.sendMessage({ type: 'GET_AUTO_REFRESH_STATUS' }, (response) => {
            if (chrome.runtime.lastError) {
                console.log('❌ 获取自动刷新状态失败:', chrome.runtime.lastError.message);
                resolve(false);
            } else {
                console.log('✅ 获取自动刷新状态成功:', response);
                if (response.success) {
                    console.log(`  启用: ${response.enabled ? '是' : '否'}`);
                    console.log(`  间隔: ${response.interval}秒`);
                    console.log(`  目标标签页: ${response.targetTabId || 'null'}`);
                    console.log(`  上次刷新: ${response.lastRefreshTime ? new Date(response.lastRefreshTime).toLocaleTimeString() : '从未'}`);
                    resolve(true);
                } else {
                    console.log('❌ 获取状态失败');
                    resolve(false);
                }
            }
        });
    });
}

// 5. 完整的WebSocket修复测试
async function fullWebSocketFixTest() {
    console.log('🚀 开始完整WebSocket修复测试...\n');
    
    try {
        // 1. 测试获取连接状态
        const getStatusOK = await testGetConnectionStatus();
        
        // 2. 测试重置连接
        const resetOK = await testResetConnection();
        
        // 等待3秒让连接重新建立
        console.log('\n⏰ 等待3秒让连接重新建立...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 3. 再次测试获取连接状态
        const getStatusAfterResetOK = await testGetConnectionStatus();
        
        // 4. 测试自动刷新设置
        const autoRefreshSetOK = await testAutoRefreshSetting();
        
        // 5. 测试获取自动刷新状态
        const autoRefreshGetOK = await testGetAutoRefreshStatus();
        
        // 生成测试报告
        console.log('\n📋 === WebSocket修复测试报告 ===');
        console.log(`✅ 获取连接状态: ${getStatusOK ? '通过' : '失败'}`);
        console.log(`✅ 重置连接: ${resetOK ? '通过' : '失败'}`);
        console.log(`✅ 重置后连接状态: ${getStatusAfterResetOK ? '通过' : '失败'}`);
        console.log(`✅ 自动刷新设置: ${autoRefreshSetOK ? '通过' : '失败'}`);
        console.log(`✅ 自动刷新状态获取: ${autoRefreshGetOK ? '通过' : '失败'}`);
        
        const allOK = getStatusOK && resetOK && getStatusAfterResetOK && autoRefreshSetOK && autoRefreshGetOK;
        
        if (allOK) {
            console.log('\n🎉 WebSocket修复测试全部通过！');
            console.log('💡 现在可以正常使用WebSocket相关功能了');
            
            // 设置自动刷新进行最终测试
            console.log('\n🧪 设置60秒自动刷新进行最终测试...');
            
            // 确保勾选"刷新当前页面"
            const allowActiveRefreshCheckbox = document.querySelector('#ylz-allowActiveRefresh');
            if (allowActiveRefreshCheckbox && !allowActiveRefreshCheckbox.checked) {
                console.log('✅ 勾选"刷新当前页面"');
                allowActiveRefreshCheckbox.click();
            }
            
            // 设置60秒刷新
            const autoRefreshSelect = document.querySelector('#ylz-autoRefreshSelect');
            if (autoRefreshSelect) {
                autoRefreshSelect.value = '60';
                autoRefreshSelect.dispatchEvent(new Event('change'));
                
                console.log('✅ 已设置60秒自动刷新 + 允许刷新当前页面');
                console.log('⏰ 请等待60秒观察页面是否自动刷新');
                
                // 启动倒计时
                let countdown = 60;
                const countdownTimer = setInterval(() => {
                    countdown--;
                    if (countdown > 0 && countdown % 10 === 0) {
                        console.log(`⏰ 倒计时: ${countdown}秒`);
                    } else if (countdown === 0) {
                        console.log('⏰ 时间到！检查页面是否刷新...');
                        clearInterval(countdownTimer);
                    }
                }, 1000);
            }
            
        } else {
            console.log('\n⚠️ WebSocket修复测试部分失败，需要进一步检查');
            
            if (!getStatusOK) {
                console.log('💡 建议: 检查后台脚本是否正常运行');
            }
            if (!resetOK) {
                console.log('💡 建议: 检查WebSocket重置逻辑');
            }
            if (!autoRefreshSetOK || !autoRefreshGetOK) {
                console.log('💡 建议: 检查自动刷新相关代码');
            }
        }
        
        return allOK;
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
        return false;
    }
}

// 6. 快速连接测试
async function quickConnectionTest() {
    console.log('\n⚡ 快速连接测试...');
    
    const connected = await testGetConnectionStatus();
    
    if (connected) {
        console.log('✅ WebSocket连接正常');
        
        // 测试自动刷新
        const autoRefreshOK = await testAutoRefreshSetting();
        if (autoRefreshOK) {
            console.log('✅ 自动刷新功能正常');
            console.log('🎉 所有核心功能正常工作！');
        } else {
            console.log('❌ 自动刷新功能异常');
        }
    } else {
        console.log('❌ WebSocket连接异常，尝试重置...');
        
        const resetOK = await testResetConnection();
        if (resetOK) {
            console.log('✅ 连接重置成功，请等待几秒后重新测试');
        } else {
            console.log('❌ 连接重置失败');
        }
    }
}

// 导出函数
window.testGetConnectionStatus = testGetConnectionStatus;
window.testResetConnection = testResetConnection;
window.testAutoRefreshSetting = testAutoRefreshSetting;
window.testGetAutoRefreshStatus = testGetAutoRefreshStatus;
window.fullWebSocketFixTest = fullWebSocketFixTest;
window.quickConnectionTest = quickConnectionTest;

console.log('✅ WebSocket修复测试脚本加载完成！');
console.log('📖 使用方法:');
console.log('  - fullWebSocketFixTest() - 完整WebSocket修复测试');
console.log('  - quickConnectionTest() - 快速连接测试');

// 自动运行快速连接测试
quickConnectionTest();

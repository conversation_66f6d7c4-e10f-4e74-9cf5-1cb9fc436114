<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接诊断工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        .error { background-color: #f8d7da; color: #721c24; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: scroll;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }
        .log-info { color: #0066cc; }
        .log-error { color: #cc0000; }
        .log-success { color: #009900; }
        .log-warning { color: #ff8800; }
        .test-section {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 WebSocket连接诊断工具</h1>
        <p>此工具用于测试文件云流转助手扩展的WebSocket连接修复效果</p>

        <div class="test-section">
            <h3>📡 连接状态</h3>
            <div id="connectionStatus" class="status disconnected">未连接</div>
            <button onclick="testConnection()">测试连接</button>
            <button onclick="reconnectTest()">测试重连</button>
            <button onclick="forceDisconnect()" id="disconnectBtn" disabled>强制断开</button>
        </div>

        <div class="test-section">
            <h3>💬 消息测试</h3>
            <button onclick="sendPing()" id="pingBtn" disabled>发送Ping</button>
            <button onclick="sendTestMessage()" id="testMsgBtn" disabled>发送测试消息</button>
            <button onclick="getFileTree()" id="fileTreeBtn" disabled>获取文件树</button>
        </div>

        <div class="test-section">
            <h3>🔍 高级诊断</h3>
            <button onclick="stateAnalysis()">状态分析</button>
            <button onclick="performanceTest()">性能测试</button>
            <button onclick="stressTest()" id="stressBtn">压力测试</button>
            <button onclick="clearLog()">清除日志</button>
        </div>

        <div class="test-section">
            <h3>📊 连接信息</h3>
            <div id="connectionInfo">
                <p><strong>目标地址:</strong> <span id="wsUrl">ws://***************:6656</span></p>
                <p><strong>当前状态:</strong> <span id="wsState">未知</span></p>
                <p><strong>重连次数:</strong> <span id="reconnectCount">0</span></p>
                <p><strong>连接时长:</strong> <span id="connectionDuration">-</span></p>
                <p><strong>消息计数:</strong> 发送 <span id="sentCount">0</span> / 接收 <span id="receivedCount">0</span></p>
            </div>
        </div>

        <div id="log"></div>
    </div>

    <script>
        // WebSocket连接配置
        const WS_URL = 'ws://***************:6656';
        const WS_STATE = {
            CONNECTING: 0,
            OPEN: 1,
            CLOSING: 2,
            CLOSED: 3
        };

        let ws = null;
        let connectTime = null;
        let reconnectAttempts = 0;
        let sentMessages = 0;
        let receivedMessages = 0;
        let connectionCheckInterval = null;
        let isReconnecting = false;

        // 状态枚举名称映射
        const stateNames = {
            [WS_STATE.CONNECTING]: 'CONNECTING',
            [WS_STATE.OPEN]: 'OPEN',
            [WS_STATE.CLOSING]: 'CLOSING',
            [WS_STATE.CLOSED]: 'CLOSED'
        };

        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[WebSocket诊断] ${message}`);
        }

        // 更新UI状态
        function updateUI() {
            const status = document.getElementById('connectionStatus');
            const wsStateElement = document.getElementById('wsState');
            const buttons = ['pingBtn', 'testMsgBtn', 'fileTreeBtn', 'disconnectBtn'];
            
            if (ws && ws.readyState === WS_STATE.OPEN) {
                status.className = 'status connected';
                status.textContent = '✅ 已连接';
                buttons.forEach(id => document.getElementById(id).disabled = false);
                wsStateElement.textContent = 'OPEN (1)';
            } else if (ws && ws.readyState === WS_STATE.CONNECTING) {
                status.className = 'status connecting';
                status.textContent = '🔄 连接中...';
                buttons.forEach(id => document.getElementById(id).disabled = true);
                wsStateElement.textContent = 'CONNECTING (0)';
            } else {
                status.className = 'status disconnected';
                status.textContent = '❌ 未连接';
                buttons.forEach(id => document.getElementById(id).disabled = true);
                wsStateElement.textContent = ws ? `${stateNames[ws.readyState]} (${ws.readyState})` : 'NULL';
            }

            // 更新连接信息
            document.getElementById('reconnectCount').textContent = reconnectAttempts;
            document.getElementById('sentCount').textContent = sentMessages;
            document.getElementById('receivedCount').textContent = receivedMessages;
            
            if (connectTime && ws && ws.readyState === WS_STATE.OPEN) {
                const duration = Math.floor((Date.now() - connectTime) / 1000);
                document.getElementById('connectionDuration').textContent = `${duration}秒`;
            } else {
                document.getElementById('connectionDuration').textContent = '-';
            }
        }

        // 检查WebSocket是否可用
        function isWebSocketReady() {
            return ws && ws.readyState === WS_STATE.OPEN;
        }

        // 创建WebSocket连接
        function createConnection() {
            if (ws && (ws.readyState === WS_STATE.CONNECTING || ws.readyState === WS_STATE.OPEN)) {
                log('连接已存在，跳过创建', 'warning');
                return;
            }

            log(`正在连接到 ${WS_URL}`, 'info');
            
            try {
                ws = new WebSocket(WS_URL);
                connectTime = Date.now();

                ws.onopen = () => {
                    log('WebSocket连接成功！', 'success');
                    reconnectAttempts = 0;
                    isReconnecting = false;
                    updateUI();
                    
                    // 发送初始化消息
                    sendMessage('init', { client: 'diagnostic-tool', version: '1.0.0' });
                };

                ws.onclose = (event) => {
                    log(`WebSocket连接关闭: code=${event.code}, reason="${event.reason}", wasClean=${event.wasClean}`, 'warning');
                    connectTime = null;
                    updateUI();
                };

                ws.onerror = (error) => {
                    log(`WebSocket错误: ${error}`, 'error');
                    updateUI();
                };

                ws.onmessage = (event) => {
                    receivedMessages++;
                    try {
                        const message = JSON.parse(event.data);
                        log(`收到消息: ${JSON.stringify(message)}`, 'success');
                    } catch (e) {
                        log(`收到原始消息: ${event.data}`, 'success');
                    }
                    updateUI();
                };

            } catch (error) {
                log(`创建WebSocket连接失败: ${error}`, 'error');
                updateUI();
            }
        }

        // 发送消息
        function sendMessage(type, data = {}) {
            if (!isWebSocketReady()) {
                log('WebSocket未就绪，无法发送消息', 'error');
                return false;
            }

            try {
                const message = JSON.stringify({ type, ...data });
                ws.send(message);
                sentMessages++;
                log(`发送消息: ${type}`, 'info');
                updateUI();
                return true;
            } catch (error) {
                log(`发送消息失败: ${error}`, 'error');
                return false;
            }
        }

        // 测试连接
        function testConnection() {
            log('开始连接测试...', 'info');
            createConnection();
        }

        // 测试重连
        function reconnectTest() {
            if (isReconnecting) {
                log('重连测试已在进行中', 'warning');
                return;
            }

            log('开始重连测试...', 'info');
            isReconnecting = true;
            reconnectAttempts++;
            
            if (ws) {
                ws.close();
            }
            
            setTimeout(() => {
                createConnection();
            }, 1000);
        }

        // 强制断开
        function forceDisconnect() {
            if (ws) {
                log('强制断开WebSocket连接', 'warning');
                ws.close(1000, '用户主动断开');
            }
        }

        // 发送Ping
        function sendPing() {
            if (sendMessage('ping')) {
                log('Ping消息已发送', 'info');
            }
        }

        // 发送测试消息
        function sendTestMessage() {
            const testData = {
                timestamp: Date.now(),
                random: Math.random(),
                test: true
            };
            
            if (sendMessage('test_message', testData)) {
                log('测试消息已发送', 'info');
            }
        }

        // 获取文件树
        function getFileTree() {
            if (sendMessage('get_full_admin_file_tree')) {
                log('文件树请求已发送', 'info');
            }
        }

        // 状态分析
        function stateAnalysis() {
            log('=== 状态分析开始 ===', 'info');
            log(`WebSocket对象: ${ws ? '存在' : '不存在'}`, 'info');
            
            if (ws) {
                log(`当前状态: ${stateNames[ws.readyState]} (${ws.readyState})`, 'info');
                log(`协议: ${ws.protocol || '未指定'}`, 'info');
                log(`URL: ${ws.url}`, 'info');
                log(`扩展: ${ws.extensions || '无'}`, 'info');
            }
            
            log(`重连次数: ${reconnectAttempts}`, 'info');
            log(`连接时间: ${connectTime ? new Date(connectTime).toLocaleString() : '未连接'}`, 'info');
            log(`消息统计: 发送${sentMessages}, 接收${receivedMessages}`, 'info');
            log(`正在重连: ${isReconnecting}`, 'info');
            log('=== 状态分析结束 ===', 'info');
        }

        // 性能测试
        function performanceTest() {
            if (!isWebSocketReady()) {
                log('需要先建立连接才能进行性能测试', 'error');
                return;
            }

            log('开始性能测试...', 'info');
            const startTime = Date.now();
            let responses = 0;
            const testCount = 10;

            for (let i = 0; i < testCount; i++) {
                sendMessage('performance_test', { index: i, timestamp: Date.now() });
            }

            // 性能测试结果将在收到响应时计算
            setTimeout(() => {
                const duration = Date.now() - startTime;
                log(`性能测试完成: ${testCount}个消息, 耗时${duration}ms`, 'success');
            }, 1000);
        }

        // 压力测试
        function stressTest() {
            if (!isWebSocketReady()) {
                log('需要先建立连接才能进行压力测试', 'error');
                return;
            }

            const stressBtn = document.getElementById('stressBtn');
            stressBtn.disabled = true;
            stressBtn.textContent = '测试中...';

            log('开始压力测试（100个快速消息）...', 'warning');
            
            for (let i = 0; i < 100; i++) {
                setTimeout(() => {
                    sendMessage('stress_test', { 
                        index: i, 
                        timestamp: Date.now(),
                        data: 'x'.repeat(100) // 一些测试数据
                    });
                    
                    if (i === 99) {
                        log('压力测试完成', 'success');
                        stressBtn.disabled = false;
                        stressBtn.textContent = '压力测试';
                    }
                }, i * 10); // 每10ms发送一个消息
            }
        }

        // 清除日志
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('日志已清除', 'info');
        }

        // 初始化
        window.onload = () => {
            log('WebSocket诊断工具已加载', 'info');
            updateUI();
            
            // 定期更新UI
            setInterval(updateUI, 1000);
        };
    </script>
</body>
</html> 
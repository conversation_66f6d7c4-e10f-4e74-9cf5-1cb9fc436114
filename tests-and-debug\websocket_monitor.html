<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket状态监控器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .monitor-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected { background-color: #28a745; }
        .status-disconnected { background-color: #dc3545; }
        .status-connecting { background-color: #ffc107; }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 10px;
            margin: 20px 0;
        }
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        .info-value {
            color: #212529;
            font-family: monospace;
        }
        .log-container {
            max-height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 2px;
        }
        .log-info { background-color: #d1ecf1; color: #0c5460; }
        .log-warn { background-color: #fff3cd; color: #856404; }
        .log-error { background-color: #f8d7da; color: #721c24; }
        .log-success { background-color: #d4edda; color: #155724; }
        .controls {
            margin: 20px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
        }
        button:hover { background-color: #0056b3; }
        .refresh-indicator {
            color: #6c757d;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="monitor-container">
        <h2>🔗 WebSocket连接监控器</h2>
        
        <div class="status-section">
            <h3>
                <span id="statusIndicator" class="status-indicator status-disconnected"></span>
                连接状态: <span id="connectionStatus">检查中...</span>
            </h3>
            
            <div class="info-grid">
                <span class="info-label">服务器地址:</span>
                <span class="info-value">ws://192.168.100.110:6656</span>
                
                <span class="info-label">连接时长:</span>
                <span class="info-value" id="connectionDuration">-</span>
                
                <span class="info-label">重连次数:</span>
                <span class="info-value" id="reconnectCount">-</span>
                
                <span class="info-label">最后ping:</span>
                <span class="info-value" id="lastPing">-</span>
                
                <span class="info-label">消息统计:</span>
                <span class="info-value" id="messageStats">发送: - / 接收: -</span>
                
                <span class="info-label">关闭原因:</span>
                <span class="info-value" id="closeReason">-</span>
            </div>
        </div>

        <div class="controls">
            <button onclick="requestConnection()">检查连接</button>
            <button onclick="forceReconnect()">强制重连</button>
            <button onclick="clearLogs()">清除日志</button>
            <span class="refresh-indicator">自动刷新: <span id="autoRefresh">启用</span></span>
        </div>

        <div class="log-section">
            <h3>📋 实时日志</h3>
            <div id="logContainer" class="log-container">
                <div class="log-entry log-info">WebSocket监控器已启动，等待连接状态...</div>
            </div>
        </div>
    </div>

    <script>
        let connectionStartTime = null;
        let logCount = 0;
        const MAX_LOGS = 100;

        // 添加日志条目
        function addLog(message, level = 'info', data = null) {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry log-${level}`;
            
            let logText = `[${timestamp}] ${message}`;
            if (data) {
                logText += ` | ${JSON.stringify(data)}`;
            }
            entry.textContent = logText;
            
            logContainer.appendChild(entry);
            logCount++;
            
            // 限制日志数量
            if (logCount > MAX_LOGS) {
                logContainer.removeChild(logContainer.firstChild);
                logCount--;
            }
            
            // 滚动到底部
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新连接状态显示
        function updateConnectionStatus(connected, data = {}) {
            const indicator = document.getElementById('statusIndicator');
            const status = document.getElementById('connectionStatus');
            
            if (connected) {
                indicator.className = 'status-indicator status-connected';
                status.textContent = '已连接';
                if (data.connectionTime) {
                    connectionStartTime = Date.now() - data.connectionTime;
                }
            } else {
                indicator.className = 'status-indicator status-disconnected';
                status.textContent = '未连接';
                connectionStartTime = null;
            }
            
            // 更新其他信息
            if (data.closeCode && data.closeReason) {
                document.getElementById('closeReason').textContent = `${data.closeCode}: ${data.closeReason}`;
            }
        }

        // 更新连接持续时间
        function updateConnectionDuration() {
            const durationElement = document.getElementById('connectionDuration');
            if (connectionStartTime) {
                const duration = Math.floor((Date.now() - connectionStartTime) / 1000);
                durationElement.textContent = `${duration}秒`;
            } else {
                durationElement.textContent = '-';
            }
        }

        // 检查连接状态
        function requestConnection() {
            addLog('请求连接状态检查...', 'info');
            chrome.runtime.sendMessage({ type: 'GET_CONNECTION_STATUS' }, (response) => {
                if (response) {
                    updateConnectionStatus(response.connected);
                    addLog(`连接状态: ${response.connected ? '已连接' : '未连接'}`, 
                           response.connected ? 'success' : 'warn');
                } else {
                    addLog('无法获取连接状态', 'error');
                }
            });
        }

        // 强制重连
        function forceReconnect() {
            addLog('请求强制重连...', 'warn');
            chrome.runtime.sendMessage({ 
                type: 'SEND_WS_MESSAGE', 
                wsMessage: { type: 'force_reconnect' } 
            }, (response) => {
                if (response && response.success) {
                    addLog('重连请求已发送', 'info');
                } else {
                    addLog('重连请求失败', 'error');
                }
            });
        }

        // 清除日志
        function clearLogs() {
            const logContainer = document.getElementById('logContainer');
            logContainer.innerHTML = '<div class="log-entry log-info">日志已清除</div>';
            logCount = 1;
        }

        // 监听来自background的消息
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.type === 'CONNECTION_STATUS_CHANGED') {
                updateConnectionStatus(message.connected, message);
                const statusText = message.connected ? '连接成功' : '连接断开';
                addLog(statusText, message.connected ? 'success' : 'warn', {
                    code: message.closeCode,
                    reason: message.closeReason
                });
            } else if (message.type === 'DEBUG_LOG') {
                addLog(message.message, message.level, message.data);
            }
        });

        // 定期更新
        setInterval(() => {
            updateConnectionDuration();
            
            // 每30秒检查一次连接
            if (Date.now() % 30000 < 1000) {
                requestConnection();
            }
        }, 1000);

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            addLog('WebSocket监控器已初始化', 'info');
            requestConnection();
        });
    </script>
</body>
</html> 
// 文件类型图标映射系统
class FileIconMapper {
    constructor() {
        // 文件扩展名到图标文件的映射
        this.iconMap = {
            // 图片文件
            'jpg': 'image.svg',
            'jpeg': 'image.svg',
            'png': 'image.svg',
            'gif': 'image.svg',
            'bmp': 'image.svg',
            'webp': 'image.svg',
            'svg': 'image.svg',
            'ico': 'image.svg',
            
            // 视频文件
            'mp4': 'video.svg',
            'avi': 'video.svg',
            'mkv': 'video.svg',
            'mov': 'video.svg',
            'wmv': 'video.svg',
            'flv': 'video.svg',
            'webm': 'video.svg',
            'm4v': 'video.svg',
            
            // 音频文件
            'mp3': 'audio.svg',
            'wav': 'audio.svg',
            'flac': 'audio.svg',
            'aac': 'audio.svg',
            'ogg': 'audio.svg',
            'wma': 'audio.svg',
            'm4a': 'audio.svg',
            
            // 文档文件
            'pdf': 'pdf.svg',
            'doc': 'word.svg',
            'docx': 'word.svg',
            'xls': 'excel.svg',
            'xlsx': 'excel.svg',
            'ppt': 'powerpoint.svg',
            'pptx': 'powerpoint.svg',
            
            // 压缩文件
            'zip': 'compressed.svg',
            'rar': 'compressed.svg',
            '7z': 'compressed.svg',
            'tar': 'compressed.svg',
            'gz': 'compressed.svg',
            'bz2': 'compressed.svg',
            
            // 代码文件
            'js': 'javascript.svg',
            'ts': 'code.svg',
            'html': 'html.svg',
            'htm': 'html.svg',
            'css': 'css.svg',
            'scss': 'css.svg',
            'sass': 'css.svg',
            'less': 'css.svg',
            'php': 'code.svg',
            'py': 'code.svg',
            'java': 'code.svg',
            'cpp': 'code.svg',
            'c': 'code.svg',
            'cs': 'code.svg',
            'go': 'code.svg',
            'rs': 'code.svg',
            'swift': 'code.svg',
            'kt': 'code.svg',
            'dart': 'code.svg',
            'vue': 'code.svg',
            'jsx': 'code.svg',
            'tsx': 'code.svg',
            
            // 数据文件
            'json': 'json.svg',
            'xml': 'xml.svg',
            'yaml': 'text.svg',
            'yml': 'text.svg',
            'csv': 'excel.svg',
            'sql': 'database.svg',
            'db': 'database.svg',
            'sqlite': 'database.svg',
            
            // 文本文件
            'txt': 'text.svg',
            'md': 'markdown.svg',
            'markdown': 'markdown.svg',
            'log': 'text.svg',
            'conf': 'text.svg',
            'ini': 'text.svg',
            'cfg': 'text.svg',
            
            // 字体文件
            'ttf': 'font.svg',
            'otf': 'font.svg',
            'woff': 'font.svg',
            'woff2': 'font.svg',
            'eot': 'font.svg'
        };
        
        // 图标缓存
        this.iconCache = new Map();
        
        // 基础图标路径
        this.iconBasePath = 'icons/file-types/';
    }
    
    /**
     * 根据文件扩展名获取图标路径
     * @param {string} filename - 文件名
     * @returns {string} 图标路径
     */
    getIconPath(filename) {
        const extension = this.getFileExtension(filename);
        const iconFile = this.iconMap[extension] || 'default.svg';
        return this.iconBasePath + iconFile;
    }
    
    /**
     * 获取文件扩展名
     * @param {string} filename - 文件名
     * @returns {string} 小写的文件扩展名
     */
    getFileExtension(filename) {
        if (!filename || typeof filename !== 'string') {
            return 'unknown';
        }
        
        const lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex === -1 || lastDotIndex === filename.length - 1) {
            return 'unknown';
        }
        
        return filename.slice(lastDotIndex + 1).toLowerCase();
    }
    
    /**
     * 判断文件是否为图片类型
     * @param {string} filename - 文件名
     * @returns {boolean} 是否为图片
     */
    isImageFile(filename) {
        const extension = this.getFileExtension(filename);
        const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico'];
        return imageExtensions.includes(extension);
    }
    
    /**
     * 判断文件是否为视频类型
     * @param {string} filename - 文件名
     * @returns {boolean} 是否为视频
     */
    isVideoFile(filename) {
        const extension = this.getFileExtension(filename);
        const videoExtensions = ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm', 'm4v'];
        return videoExtensions.includes(extension);
    }
    
    /**
     * 判断文件是否为音频类型
     * @param {string} filename - 文件名
     * @returns {boolean} 是否为音频
     */
    isAudioFile(filename) {
        const extension = this.getFileExtension(filename);
        const audioExtensions = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'];
        return audioExtensions.includes(extension);
    }
    
    /**
     * 获取文件类型分类
     * @param {string} filename - 文件名
     * @returns {string} 文件类型分类
     */
    getFileCategory(filename) {
        const extension = this.getFileExtension(filename);
        
        if (this.isImageFile(filename)) return 'image';
        if (this.isVideoFile(filename)) return 'video';
        if (this.isAudioFile(filename)) return 'audio';
        
        // 文档类型
        if (['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(extension)) {
            return 'document';
        }
        
        // 代码类型
        if (['js', 'ts', 'html', 'css', 'php', 'py', 'java', 'cpp', 'c', 'vue', 'jsx'].includes(extension)) {
            return 'code';
        }
        
        // 压缩文件
        if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].includes(extension)) {
            return 'compressed';
        }
        
        // 数据文件
        if (['json', 'xml', 'sql', 'db', 'csv'].includes(extension)) {
            return 'data';
        }
        
        // 文本文件
        if (['txt', 'md', 'log', 'conf', 'ini'].includes(extension)) {
            return 'text';
        }
        
        return 'other';
    }
    
    /**
     * 异步加载图标内容（用于内联SVG）
     * @param {string} filename - 文件名
     * @returns {Promise<string>} SVG内容
     */
    async loadIconSVG(filename) {
        const iconPath = this.getIconPath(filename);
        
        // 检查缓存
        if (this.iconCache.has(iconPath)) {
            return this.iconCache.get(iconPath);
        }
        
        try {
            const response = await fetch(chrome.runtime.getURL(iconPath));
            if (!response.ok) {
                throw new Error(`Failed to load icon: ${response.status}`);
            }
            
            const svgContent = await response.text();
            
            // 缓存结果
            this.iconCache.set(iconPath, svgContent);
            
            return svgContent;
        } catch (error) {
            console.warn('Failed to load icon:', iconPath, error);
            
            // 返回默认图标
            const defaultPath = this.iconBasePath + 'default.svg';
            if (!this.iconCache.has(defaultPath)) {
                try {
                    const defaultResponse = await fetch(chrome.runtime.getURL(defaultPath));
                    const defaultSVG = await defaultResponse.text();
                    this.iconCache.set(defaultPath, defaultSVG);
                    return defaultSVG;
                } catch (defaultError) {
                    console.error('Failed to load default icon:', defaultError);
                    return this.generateFallbackSVG(filename);
                }
            }
            
            return this.iconCache.get(defaultPath);
        }
    }
    
    /**
     * 生成回退SVG图标
     * @param {string} filename - 文件名
     * @returns {string} SVG内容
     */
    generateFallbackSVG(filename) {
        const extension = this.getFileExtension(filename).toUpperCase();
        const displayText = extension.length > 4 ? extension.slice(0, 4) : extension;
        
        return `
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" fill="none">
                <rect x="8" y="6" width="32" height="36" rx="3" fill="#F3F4F6" stroke="#9CA3AF" stroke-width="2"/>
                <text x="24" y="28" text-anchor="middle" fill="#6B7280" font-family="system-ui" font-size="10" font-weight="600">${displayText}</text>
            </svg>
        `.trim();
    }
    
    /**
     * 批量预加载常用图标
     * @param {string[]} extensions - 要预加载的扩展名列表
     */
    async preloadIcons(extensions = ['jpg', 'png', 'pdf', 'doc', 'zip', 'txt', 'mp4', 'mp3']) {
        const loadPromises = extensions.map(ext => {
            const dummyFilename = `test.${ext}`;
            return this.loadIconSVG(dummyFilename).catch(error => {
                console.warn(`Failed to preload icon for ${ext}:`, error);
            });
        });
        
        await Promise.allSettled(loadPromises);
        console.log('File icons preloaded:', extensions.length);
    }
}

// 创建全局实例
window.fileIconMapper = new FileIconMapper();

// 如果在Chrome扩展环境中，预加载常用图标
if (typeof chrome !== 'undefined' && chrome.runtime) {
    window.fileIconMapper.preloadIcons();
} 
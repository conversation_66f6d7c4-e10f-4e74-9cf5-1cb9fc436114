// 缩略图缓存管理系统
class ThumbnailCache {
  constructor() {
    this.memoryCache = new Map(); // 内存缓存
    this.storageCache = null;
    this.cacheSize = 0;
    this.maxCacheSize = 50 * 1024 * 1024; // 50MB
    this.isInitialized = false;
    
    // 添加请求去重机制
    this.pendingRequests = new Map();
    
    this.dbName = 'ThumbnailCacheDB';
    this.dbVersion = 1;
    this.storeName = 'thumbnails';
    this.db = null;
    
    // 缓存配置
    this.config = {
      memoryTTL: 5 * 60 * 1000, // 内存缓存5分钟 (从30分钟缩短)
      localStorageTTL: 60 * 60 * 1000, // localStorage缓存1小时 (从1天缩短)
      indexedDBTTL: 24 * 60 * 60 * 1000, // IndexedDB缓存1天 (从7天缩短)
      maxLocalStorageSize: 10 * 1024 * 1024, // localStorage最大10MB
      maxIndexedDBSize: 100 * 1024 * 1024 // IndexedDB最大100MB
    };
    
    this.initializeDB();
  }

  // 初始化IndexedDB
  async initializeDB() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve(this.db);
      };
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: 'key' });
          store.createIndex('timestamp', 'timestamp', { unique: false });
          store.createIndex('size', 'size', { unique: false });
        }
      };
    });
  }

  // 生成缓存键
  generateCacheKey(filePath, options = {}) {
    const { size = 'medium', quality = 'medium', format = 'jpeg' } = options;
    // 修复中文字符编码问题：先用 encodeURIComponent 处理，再用 btoa
    try {
      const encodedPath = encodeURIComponent(filePath);
      const base64Path = btoa(encodedPath);
      return `thumb_${base64Path}_${size}_${quality}_${format}`;
    } catch (error) {
      // 如果还有问题，使用更安全的哈希方法
      console.warn('使用备用缓存键生成方法:', error);
      const hashCode = filePath.split('').reduce((hash, char) => {
        const code = char.charCodeAt(0);
        return ((hash << 5) - hash) + code;
      }, 0);
      return `thumb_${Math.abs(hashCode)}_${size}_${quality}_${format}`;
    }
  }

  // 获取缩略图 - 完全禁用缓存，直接从服务器获取
  async getThumbnail(filePath, options = {}) {
    console.log('缓存已禁用，直接从服务器获取缩略图:', filePath);
    return null; // 强制从服务器获取最新数据
  }

  // 设置缩略图缓存 - 禁用存储，只返回数据
  async setThumbnail(filePath, thumbnailData, options = {}) {
    console.log('缓存已禁用，跳过存储操作:', filePath);
    
    // 直接返回数据结构，不进行任何缓存存储
    const cacheEntry = {
      data: thumbnailData,
      timestamp: Date.now(),
      size: this.estimateSize(thumbnailData),
      options: options
    };

    return cacheEntry;
  }

  // 内存缓存操作
  getFromMemoryCache(key) {
    const entry = this.memoryCache.get(key);
    if (entry && Date.now() - entry.timestamp < this.config.memoryTTL) {
      return entry;
    } else if (entry) {
      // 过期删除
      this.memoryCache.delete(key);
      this.cacheSize -= entry.size;
    }
    return null;
  }

  setToMemoryCache(key, entry) {
    // 检查内存限制
    while (this.cacheSize + entry.size > this.maxCacheSize && this.memoryCache.size > 0) {
      this.evictOldestFromMemory();
    }

    this.memoryCache.set(key, entry);
    this.cacheSize += entry.size;
  }

  evictOldestFromMemory() {
    const oldestKey = this.memoryCache.keys().next().value;
    if (oldestKey) {
      const entry = this.memoryCache.get(oldestKey);
      this.memoryCache.delete(oldestKey);
      this.cacheSize -= entry.size;
    }
  }

  // localStorage操作
  getFromLocalStorage(key) {
    try {
      const stored = localStorage.getItem(key);
      if (stored) {
        const entry = JSON.parse(stored);
        if (Date.now() - entry.timestamp < this.config.localStorageTTL) {
          return entry;
        } else {
          localStorage.removeItem(key);
        }
      }
    } catch (e) {
      console.error('localStorage读取失败:', e);
    }
    return null;
  }

  setToLocalStorage(key, entry) {
    try {
      const serialized = JSON.stringify(entry);
      
      // 检查localStorage容量 - 对于大型缩略图，跳过localStorage存储
      const estimatedSize = this.estimateSize(serialized);
      if (estimatedSize > this.config.maxLocalStorageSize / 10) { // 单个项目不超过最大容量的10%
        console.log('缩略图过大，跳过localStorage存储，直接使用IndexedDB');
        return;
      }
      
      // 检查当前localStorage使用情况
      const currentUsage = this.getLocalStorageUsage();
      if (currentUsage + estimatedSize > this.config.maxLocalStorageSize) {
        console.log('localStorage空间不足，强制清理');
        this.forceCleanupLocalStorage();
      }

      localStorage.setItem(key, serialized);
    } catch (e) {
      if (e.name === 'QuotaExceededError') {
        console.warn('localStorage空间不足，执行强制清理');
        this.forceCleanupLocalStorage();
        // 不再重试，直接跳过localStorage存储
        console.log('跳过localStorage存储，使用IndexedDB');
      } else {
        console.error('localStorage写入失败:', e);
      }
    }
  }

  // 获取localStorage当前使用量
  getLocalStorageUsage() {
    let usage = 0;
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('thumb_')) {
        try {
          const value = localStorage.getItem(key);
          usage += this.estimateSize(value);
        } catch (e) {
          // 忽略错误
        }
      }
    }
    return usage;
  }

  // 强制清理localStorage
  forceCleanupLocalStorage() {
    const keysToRemove = [];
    const now = Date.now();
    
    // 收集所有缩略图相关的键
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('thumb_')) {
        try {
          const entry = JSON.parse(localStorage.getItem(key));
          // 过期或大型文件都清理
          if (!entry.timestamp || 
              now - entry.timestamp > this.config.localStorageTTL ||
              this.estimateSize(localStorage.getItem(key)) > this.config.maxLocalStorageSize / 20) {
            keysToRemove.push(key);
          }
        } catch (e) {
          keysToRemove.push(key); // 损坏的数据也删除
        }
      }
    }

    // 如果过期数据不够，按时间戳清理最旧的
    if (keysToRemove.length < 5) {
      const allThumbnailKeys = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('thumb_')) {
          try {
            const entry = JSON.parse(localStorage.getItem(key));
            allThumbnailKeys.push({ key, timestamp: entry.timestamp || 0 });
          } catch (e) {
            allThumbnailKeys.push({ key, timestamp: 0 });
          }
        }
      }
      
      // 按时间戳排序，清理最旧的一半
      allThumbnailKeys.sort((a, b) => a.timestamp - b.timestamp);
      const toRemove = allThumbnailKeys.slice(0, Math.max(5, Math.floor(allThumbnailKeys.length / 2)));
      toRemove.forEach(item => {
        if (!keysToRemove.includes(item.key)) {
          keysToRemove.push(item.key);
        }
      });
    }

    keysToRemove.forEach(key => localStorage.removeItem(key));
    console.log(`强制清理了${keysToRemove.length}个localStorage缓存项`);
    
    return keysToRemove.length;
  }

  // IndexedDB操作
  async getFromIndexedDB(key) {
    if (!this.db) return null;

    return new Promise((resolve) => {
      const transaction = this.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      const request = store.get(key);

      request.onsuccess = () => {
        const result = request.result;
        if (result && Date.now() - result.timestamp < this.config.indexedDBTTL) {
          resolve(result);
        } else if (result) {
          // 异步删除过期数据
          this.deleteFromIndexedDB(key);
          resolve(null);
        } else {
          resolve(null);
        }
      };

      request.onerror = () => {
        console.error('IndexedDB读取失败:', request.error);
        resolve(null);
      };
    });
  }

  async setToIndexedDB(key, entry) {
    if (!this.db) return;

    return new Promise((resolve) => {
      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      const data = {
        key: key,
        ...entry
      };

      const request = store.put(data);

      request.onsuccess = () => resolve(true);
      request.onerror = () => {
        console.error('IndexedDB存储失败:', request.error);
        resolve(false);
      };
    });
  }

  async deleteFromIndexedDB(key) {
    if (!this.db) return;

    return new Promise((resolve) => {
      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const request = store.delete(key);

      request.onsuccess = () => resolve(true);
      request.onerror = () => {
        console.error('IndexedDB删除失败:', request.error);
        resolve(false);
      };
    });
  }

  // 清理过期缓存（保持接口兼容性）
  cleanupLocalStorage() {
    return this.forceCleanupLocalStorage();
  }

  async cleanupIndexedDB() {
    if (!this.db) return;

    return new Promise((resolve) => {
      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const index = store.index('timestamp');
      
      const cutoffTime = Date.now() - this.config.indexedDBTTL;
      const range = IDBKeyRange.upperBound(cutoffTime);
      
      const request = index.openCursor(range);
      let deletedCount = 0;

      request.onsuccess = (event) => {
        const cursor = event.target.result;
        if (cursor) {
          cursor.delete();
          deletedCount++;
          cursor.continue();
        } else {
          console.log(`清理了${deletedCount}个过期IndexedDB缓存`);
          resolve(deletedCount);
        }
      };

      request.onerror = () => {
        console.error('IndexedDB清理失败:', request.error);
        resolve(0);
      };
    });
  }

  // 清理所有缓存
  async clearAllCache() {
    // 清理内存缓存
    this.memoryCache.clear();
    this.cacheSize = 0;

    // 清理localStorage
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('thumb_')) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach(key => localStorage.removeItem(key));

    // 清理IndexedDB
    if (this.db) {
      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      await new Promise((resolve) => {
        const request = store.clear();
        request.onsuccess = () => resolve();
        request.onerror = () => resolve();
      });
    }

    console.log('所有缩略图缓存已清理');
  }

  // 获取缓存统计信息
  async getCacheStats() {
    const stats = {
      memory: {
        count: this.memoryCache.size,
        size: this.cacheSize,
        maxSize: this.maxCacheSize
      },
      localStorage: {
        count: 0,
        size: 0
      },
      indexedDB: {
        count: 0,
        size: 0
      }
    };

    // 统计localStorage
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('thumb_')) {
        stats.localStorage.count++;
        try {
          const value = localStorage.getItem(key);
          stats.localStorage.size += this.estimateSize(value);
        } catch (e) {
          // 忽略错误
        }
      }
    }

    // 统计IndexedDB
    if (this.db) {
      const transaction = this.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      
      await new Promise((resolve) => {
        const request = store.openCursor();
        request.onsuccess = (event) => {
          const cursor = event.target.result;
          if (cursor) {
            stats.indexedDB.count++;
            stats.indexedDB.size += cursor.value.size || 0;
            cursor.continue();
          } else {
            resolve();
          }
        };
        request.onerror = () => resolve();
      });
    }

    return stats;
  }

  // 估算数据大小
  estimateSize(data) {
    if (typeof data === 'string') {
      return data.length * 2; // UTF-16编码，每字符2字节
    } else if (data && typeof data === 'object') {
      return JSON.stringify(data).length * 2;
    }
    return 0;
  }

  // 预加载缩略图
  async preloadThumbnails(filePaths, options = {}) {
    const promises = filePaths.map(async (filePath) => {
      try {
        const cached = await this.getThumbnail(filePath, options);
        if (!cached) {
          // 如果缓存中没有，触发生成
          return this.generateAndCacheThumbnail(filePath, options);
        }
        return cached;
      } catch (error) {
        console.error(`预加载缩略图失败: ${filePath}`, error);
        return null;
      }
    });

    const results = await Promise.allSettled(promises);
    const successful = results.filter(r => r.status === 'fulfilled' && r.value).length;
    console.log(`预加载完成: ${successful}/${filePaths.length} 个缩略图`);
    
    return results;
  }

  // 生成并缓存缩略图
  async generateAndCacheThumbnail(filePath, options = {}) {
    const cacheKey = this.generateCacheKey(filePath, options);
    
    // 检查是否已有相同的请求在进行中
    if (this.pendingRequests.has(cacheKey)) {
      console.log('缩略图请求去重:', filePath);
      return this.pendingRequests.get(cacheKey);
    }
    
    try {
      // 创建新的请求Promise
      const requestPromise = this.doGenerateAndCacheThumbnail(filePath, options, cacheKey);
      
      // 存储到待处理请求中
      this.pendingRequests.set(cacheKey, requestPromise);
      
      const result = await requestPromise;
      
      // 请求完成后清理
      this.pendingRequests.delete(cacheKey);
      
      return result;
    } catch (error) {
      // 请求失败时也要清理
      this.pendingRequests.delete(cacheKey);
      throw error;
    }
  }

  // 实际的生成和缓存逻辑
  async doGenerateAndCacheThumbnail(filePath, options = {}, cacheKey) {
    try {
      // 尝试从DOM中获取额外的路径信息
      let alternatePaths = [];
      
      // 查找相关的缩略图容器元素
      const thumbnailElements = document.querySelectorAll(`[data-thumbnail-path="${filePath}"]`);
      if (thumbnailElements.length > 0) {
        const element = thumbnailElements[0];
        
        // 提取备选路径
        const alternatesAttr = element.getAttribute('data-thumbnail-alternates');
        if (alternatesAttr) {
          try {
            const parsedAlternates = JSON.parse(alternatesAttr);
            if (Array.isArray(parsedAlternates)) {
              alternatePaths = parsedAlternates;
            }
          } catch (e) {
            console.warn('解析备选路径失败:', e);
          }
        }
        
        // 获取其他路径信息
        const thumbnailName = element.getAttribute('data-thumbnail-name');
        const fileElement = element.closest('.tree-file');
        if (fileElement) {
          const originalPath = fileElement.getAttribute('data-file-original-path');
          const fullPath = fileElement.getAttribute('data-file-full-path');
          
          if (originalPath && !alternatePaths.includes(originalPath)) {
            alternatePaths.push(originalPath);
          }
          if (fullPath && !alternatePaths.includes(fullPath)) {
            alternatePaths.push(fullPath);
          }
        }
      }
      
      console.log(`为文件 ${filePath} 准备备选路径:`, alternatePaths);
      
      // 通过 chrome.runtime 向 background.js 请求缩略图
      return new Promise((resolve, reject) => {
        chrome.runtime.sendMessage({
          type: 'GET_IMAGE_THUMBNAIL',
          filePath: filePath,
          alternatePaths: alternatePaths, // 传递备选路径
          options: options
        }, (response) => {
          if (chrome.runtime.lastError) {
            console.error('请求缩略图失败:', chrome.runtime.lastError.message);
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }

          if (response && response.success && response.data && response.data.base64) {
            // 缓存缩略图数据
            this.setThumbnail(filePath, response.data.base64, options)
              .then(() => {
                console.log('缩略图生成并缓存成功:', filePath);
                resolve(response.data.base64);
              })
              .catch(cacheError => {
                console.warn('缓存缩略图失败:', cacheError);
                // 即使缓存失败，也返回缩略图数据
                resolve(response.data.base64);
              });
          } else if (response && !response.success) {
            console.warn('缩略图生成失败:', response.error || '未知错误');
            reject(new Error(response.error || '缩略图生成失败'));
          } else {
            console.warn('缩略图数据无效:', response);
            reject(new Error('服务器返回无效的缩略图数据'));
          }
        });
      });
    } catch (error) {
      console.error('生成缩略图时出错:', error);
      throw new Error(`生成缩略图失败: ${error.message}`);
    }
  }

  // 批量生成缩略图
  async batchGenerateThumbnails(filePaths, options = {}, concurrency = 3) {
    const results = [];
    
    for (let i = 0; i < filePaths.length; i += concurrency) {
      const batch = filePaths.slice(i, i + concurrency);
      const batchPromises = batch.map(filePath => 
        this.generateAndCacheThumbnail(filePath, options)
      );
      
      const batchResults = await Promise.allSettled(batchPromises);
      results.push(...batchResults);
      
      // 添加小延迟避免服务器压力
      if (i + concurrency < filePaths.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return results;
  }
}

// 创建全局实例
const thumbnailCache = new ThumbnailCache();

// 定期清理过期缓存
setInterval(() => {
  thumbnailCache.cleanupLocalStorage();
  thumbnailCache.cleanupIndexedDB();
}, 60 * 60 * 1000); // 每小时清理一次

export default thumbnailCache; 